"""
Core event invitation tasks
"""

import logging
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from core.models import EventReminder, EventScheduleEnrollment
from .whatsapp import schedule_whatsapp_invitations, send_whatsapp_invitation
from .email import schedule_email_invitations, send_email_invitation
from .utils import (
    get_random_delay,
    get_whatsapp_delay_range,
    should_use_delay_for_whatsapp,
    calculate_delay_since_last_sent,
)
from django.db.models import Q

logger = logging.getLogger(__name__)


@shared_task
def schedule_pending_invitations():
    """
    Find and schedule pending invitations that are ready to be sent
    Uses random delays to prevent Meta bans when multiple invitations are scheduled
    """
    local_tz = timezone.get_current_timezone()
    now = timezone.now().astimezone(local_tz)
    total_scheduled = 0

    # Find WhatsApp invitations ready to send
    whatsapp_reminders = EventReminder.objects.filter(
        status_whatsapp=EventReminder.PENDING,
        enrollment__event_schedule__scheduled_datetime_whatsapp__lte=now,
        enrollment__event_schedule__whatsapp_template__isnull=False,
        enrollment__event_schedule__is_whatsapp_active=True,
    ).select_related("enrollment__event_schedule")

    whatsapp_scheduled = schedule_whatsapp_invitations(whatsapp_reminders)
    total_scheduled += whatsapp_scheduled

    # Find email invitations ready to send, if emails_reminder_auto is not enable, else only for pending
    event_reminders = EventReminder.objects.filter(
        status_email=EventReminder.PENDING,
    ).select_related("enrollment__event_schedule")

    email_reminders = event_reminders.filter(
        Q(enrollment__event_schedule__emails_reminder_auto=True)
        | Q(
            enrollment__event_schedule__emails_reminder_auto=False,
            enrollment__event_schedule__scheduled_datetime_email__lte=now,
            enrollment__event_schedule__scheduled_datetime_email__isnull=False,
        )
    )

    logger.info(f"Email reminders in schedule pending: {email_reminders}")

    email_scheduled = schedule_email_invitations(email_reminders)
    total_scheduled += email_scheduled

    logger.info(
        f"Scheduled {total_scheduled} pending invitations with appropriate delays"
    )
    return f"Scheduled {total_scheduled} invitations"


@shared_task
def process_new_enrollment_invitations(enrollment_id: int):
    """
    Process invitations for a newly created enrollment

    This task will:
    1. Check if EventReminder already exists for this enrollment
    2. If not, create one (with duplicate phone validation)
    3. Apply send_now logic based on event schedule configuration
    4. Schedule immediate invitations if criteria is met

    Args:
        enrollment_id: EventScheduleEnrollment ID
    """
    try:
        logger.info(
            f"Processing new enrollment invitations for enrollment {enrollment_id}"
        )
        enrollment = EventScheduleEnrollment.objects.select_related(
            "event_schedule", "event_schedule__whatsapp_template"
        ).get(id=enrollment_id)

        event_schedule = enrollment.event_schedule
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        # Check if event has already ended - no invitations should be sent
        if event_schedule.end_date < now:
            logger.info(
                f"Event {event_schedule.name} has already ended. No invitations will be sent."
            )
            return f"El evento ha terminado. No se enviarán invitaciones para el inscrito {enrollment_id}"

        # Get or create EventReminder
        reminder = _get_or_create_event_reminder(enrollment)
        if not reminder:
            return f"Duplicate phone number detected. No reminder created for enrollment {enrollment_id}"

        scheduled_count = 0

        # Determine if we should use delays
        use_delay = should_use_delay_for_whatsapp(event_schedule)

        # Check WhatsApp invitation
        if reminder.is_ready_for_whatsapp_send():
            scheduled_count += _schedule_whatsapp_invitation(
                reminder, enrollment_id, use_delay
            )

        # Check email invitation
        if reminder.is_ready_for_email_send():
            scheduled_count += _schedule_email_invitation(reminder, enrollment_id)

        if scheduled_count == 0:
            logger.info(
                f"No immediate invitations scheduled for enrollment {enrollment_id}. "
                f"Reminders will be sent according to schedule."
            )

        return f"Processed {scheduled_count} immediate invitations for enrollment {enrollment_id}"

    except EventScheduleEnrollment.DoesNotExist:
        logger.error(f"EventScheduleEnrollment {enrollment_id} not found")
        return f"Enrollment {enrollment_id} not found"
    except Exception as e:
        logger.error(f"Error processing enrollment {enrollment_id}: {e}")
        return f"Error: {str(e)}"


def _get_or_create_event_reminder(enrollment):
    """
    Get existing or create new EventReminder for enrollment

    Args:
        enrollment: EventScheduleEnrollment instance

    Returns:
        EventReminder instance or None if duplicate phone detected
    """
    # Check for existing reminder for this enrollment
    existing_reminder = EventReminder.objects.filter(
        enrollment=enrollment, deleted=False
    ).first()

    if existing_reminder:
        logger.info(f"EventReminder already exists for enrollment {enrollment.id}")
        return existing_reminder

    # Check for duplicate phone number in the same event schedule
    if enrollment.phone_number:
        duplicate_reminder = EventReminder.objects.filter(
            enrollment__event_schedule=enrollment.event_schedule,
            enrollment__phone_number=enrollment.phone_number,
            deleted=False,
        ).first()

        if duplicate_reminder:
            logger.info(
                f"Phone number {enrollment.phone_number} already has a reminder for event {enrollment.event_schedule.name}. "
                f"Skipping to avoid duplicate WhatsApp messages."
            )
            return None

    # Create new EventReminder
    with transaction.atomic():
        reminder = EventReminder.objects.create(
            enrollment=enrollment,
            status_whatsapp=EventReminder.PENDING,
            status_email=EventReminder.PENDING,
        )
        logger.info(
            f"Created new EventReminder {reminder.rid} for enrollment {enrollment.id}"
        )
        return reminder


def _schedule_whatsapp_invitation(reminder, enrollment_id, use_delay):
    """Schedule WhatsApp invitation with appropriate delay"""
    if use_delay:
        # First check if we should use dynamic delay based on last sent message
        dynamic_delay = calculate_delay_since_last_sent(
            reminder.enrollment.event_schedule
        )

        if dynamic_delay is not None:
            # We're past scheduled time, use dynamic delay based on last sent message
            if dynamic_delay == 0:
                send_whatsapp_invitation.delay(str(reminder.rid))
                logger.info(
                    f"Scheduled immediate WhatsApp invitation for enrollment {enrollment_id} "
                    f"(sufficient time passed since last sent message)"
                )
            else:
                send_whatsapp_invitation.apply_async(
                    args=[str(reminder.rid)], countdown=dynamic_delay
                )
                logger.info(
                    f"Scheduled WhatsApp invitation for enrollment {enrollment_id} "
                    f"with {dynamic_delay}s dynamic delay based on last sent message"
                )
        else:
            # Use original logic for pending reminders (before scheduled time)
            from core.models import EventReminder

            pending_reminders = EventReminder.objects.filter(
                enrollment__event_schedule=reminder.enrollment.event_schedule,
                status_whatsapp=EventReminder.PENDING,
                deleted=False,
            ).order_by("created_at")

            # Get the position of current reminder in the pending list
            reminder_position = 0
            for index, pending_reminder in enumerate(pending_reminders):
                if pending_reminder.rid == reminder.rid:
                    reminder_position = index
                    break

            if reminder_position == 0:
                # First reminder - send immediately
                send_whatsapp_invitation.delay(str(reminder.rid))
                logger.info(
                    f"Scheduled immediate WhatsApp invitation for enrollment {enrollment_id} "
                    f"(first reminder in sequence)"
                )
            else:
                # Subsequent reminders - apply cumulative delay
                min_delay, max_delay = get_whatsapp_delay_range(
                    reminder.enrollment.event_schedule
                )
                base_delay = get_random_delay(min_delay, max_delay)
                # Calculate cumulative delay based on position
                delay_seconds = base_delay * (reminder_position + 1)

                send_whatsapp_invitation.apply_async(
                    args=[str(reminder.rid)], countdown=delay_seconds
                )
                logger.info(
                    f"Scheduled WhatsApp invitation for enrollment {enrollment_id} "
                    f"with {delay_seconds}s cumulative delay (position {reminder_position + 1})"
                )
    else:
        # Single enrollment - send immediately
        send_whatsapp_invitation.delay(str(reminder.rid))
        logger.info(
            f"Scheduled immediate WhatsApp invitation for enrollment {enrollment_id}"
        )
    return 1


def _schedule_email_invitation(reminder, enrollment_id):
    """Schedule email invitation"""
    send_email_invitation.delay(str(reminder.rid))
    logger.info(f"Scheduled email invitation for enrollment {enrollment_id}")
    return 1
