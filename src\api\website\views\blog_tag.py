from rest_framework import viewsets
from core.models import BlogPost, BlogTag
from api.mixins import SwaggerTagMixin
from api.website.serializers.blog import WebsiteBlogTagSerializer
from api.paginations import StandardResultsPagination
from django.db.models import Sum

class WebsiteBlogTagViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    """
    ViewSet for blog tags.
    Only returns tags that have published blog posts.
    """

    model_class = BlogTag
    pagination_class = StandardResultsPagination

    queryset = (
        BlogTag.objects.filter(
            blog_posts__status=BlogPost.PUBLISHED,
            blog_posts__deleted=False,
        )
        .annotate(total_views=Sum("blog_posts__view_count"))
        .order_by("-total_views", "name")  # De mayor a menor, luego por nombre
        .distinct()
    )

    serializer_class = WebsiteBlogTagSerializer
    lookup_field = "slug"
    swagger_tags = ["Blog Tags"]
