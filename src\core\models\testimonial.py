import uuid
from django.db import models
from core.models.base import (
    AuditBaseModel,
    OrderBaseModel,
)

from core.models.file import File


class Testimonial(AuditBaseModel, OrderBaseModel):
    PUBLISHED_STATUS = "Published"
    DRAFT_STATUS = "Draft"

    STATUS_CHOICES = [
        (PUBLISHED_STATUS, "Published"),
        (DRAFT_STATUS, "Draft"),
    ]
    tid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    author_name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Author Name",
    )
    author_title = models.Char<PERSON>ield(
        max_length=255,
        blank=True,
        verbose_name="Author Title",
    )
    content = models.TextField(
        blank=False,
        verbose_name="Content",
    )
    author_photo = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        related_name="testimonials",
        blank=True,
        null=True,
        verbose_name="Author Photo",
    )
    status = models.Cha<PERSON><PERSON><PERSON>(
        max_length=12,
        choices=STATUS_CHOICES,
        default=DRAFT_STATUS,
        verbose_name="Status",
    )

    def __str__(self):
        return f"Testimonial by {self.author_name}"

    class Meta:
        verbose_name = "Testimonial"
        verbose_name_plural = "Testimonials"
