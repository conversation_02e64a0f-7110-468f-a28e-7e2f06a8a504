from celery.schedules import crontab

CELERYBEAT_SCHEDULE = {
    "hello-world": {
        "task": "api.website.tasks.notification.hello_world",
        "schedule": crontab(minute="*/1"),  # Every 1 minute
    },
    "schedule_pending_invitations": {
        "task": "api.crm.tasks.event_invitations.core.schedule_pending_invitations",
        "schedule": crontab(minute="*/2"),  # Every 2 minutes
    },
}
