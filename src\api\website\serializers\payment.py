from rest_framework import serializers


class ItemSerializer(serializers.Serializer):
    oid = serializers.UUIDField()
    name = serializers.CharField()


class IdentificationSerializer(serializers.Serializer):
    type = serializers.CharField()
    number = serializers.CharField()


class PayerSerializer(serializers.Serializer):
    email = serializers.EmailField()
    identification = IdentificationSerializer()


class PaymentDataSerializer(serializers.Serializer):
    token = serializers.CharField()
    issuer_id = serializers.CharField()
    payment_method_id = serializers.CharField()
    transaction_amount = serializers.IntegerField()
    installments = serializers.IntegerField()
    payer = PayerSerializer()


class ProcessPaymentSerializer(serializers.Serializer):
    items = serializers.ListField(
        child=ItemSerializer(),
    )
    payment_data = PaymentDataSerializer()


class YapeBodySerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    otp = serializers.Char<PERSON><PERSON>()


class ProcessYapeSerializer(serializers.Serializer):
    items = serializers.ListField(
        child=ItemSerializer(),
    )
    yape_info = YapeBodySerializer()


class CreatePaypalOrderSerializer(serializers.Serializer):
    items = serializers.ListField(
        child=ItemSerializer(),
    )
