"""
Utility functions for event invitation tasks
"""

import logging
import random
from django.utils import timezone

logger = logging.getLogger(__name__)


def get_random_delay(min_delay, max_delay):
    """
    Generate a random delay between event schedule min and max seconds to prevent Meta bans

    Args:
        min_delay: Minimum delay in seconds
        max_delay: Maximum delay in seconds

    Returns:
        int: Random delay in seconds between min and max
    """
    # Validate inputs and provide fallback values
    if min_delay is None or max_delay is None:
        logger.warning(
            "WhatsApp delay range not configured, using default 5-15 seconds"
        )
        min_delay, max_delay = 5, 15

    # Ensure min is not greater than max
    if min_delay > max_delay:
        min_delay, max_delay = max_delay, min_delay

    # Ensure minimum delay is at least 1 second
    min_delay = max(1, min_delay)
    max_delay = max(1, max_delay)

    delay = random.randint(min_delay, max_delay)
    logger.info(
        f"Generated random delay: {delay} seconds (range: {min_delay}-{max_delay})"
    )
    return delay


def get_whatsapp_delay_range(event_schedule):
    """
    Get WhatsApp delay range from event schedule

    Args:
        event_schedule: EventSchedule instance

    Returns:
        tuple: (min_delay, max_delay) or (None, None) if not configured
    """
    delay_range = event_schedule.whatsapp_delay_range
    if delay_range:
        return delay_range.lower, delay_range.upper
    return None, None


def should_use_delay_for_whatsapp(event_schedule):
    """
    Determine if WhatsApp invitations should use delay based on last sent message timing
    and pending reminders count

    Args:
        event_schedule: EventSchedule instance

    Returns:
        bool: True if delay should be used

    """
    from core.models import EventReminder

    delay_needed = calculate_delay_since_last_sent(event_schedule)

    if not delay_needed:
        return False

    # Check if we have pending reminders
    pending_reminders_count = EventReminder.objects.filter(
        enrollment__event_schedule=event_schedule,
        status_whatsapp=EventReminder.PENDING,
        deleted=False,
    ).count()

    return pending_reminders_count > 1


def calculate_delay_since_last_sent(event_schedule):
    """
    Calculate delay needed based on the last sent WhatsApp message

    Args:
        event_schedule: EventSchedule instance

    Returns:
        int: Delay in seconds (0 if should send immediately, None if not past scheduled time)
    """
    from core.models import EventReminder

    local_tz = timezone.get_current_timezone()
    now = timezone.now().astimezone(local_tz)

    # Check if we're past the scheduled send time
    scheduled_time = event_schedule.scheduled_datetime_whatsapp
    if not scheduled_time or now < scheduled_time:
        # Not yet time to send, use normal pending logic
        return None

    # Find the last successfully sent reminder for this event
    last_sent_reminder = (
        EventReminder.objects.filter(
            enrollment__event_schedule=event_schedule,
            status_whatsapp=EventReminder.SENT,
            sent_at_whatsapp__isnull=False,
            deleted=False,
        )
        .order_by("-sent_at_whatsapp")
        .first()
    )

    if not last_sent_reminder:
        # No previous messages sent, send immediately
        return 0

    # Calculate time since last message was sent
    time_since_last_sent = now - last_sent_reminder.sent_at_whatsapp.astimezone(
        local_tz
    )
    seconds_since_last = int(time_since_last_sent.total_seconds())

    # Get minimum delay from configuration
    min_delay, _ = get_whatsapp_delay_range(event_schedule)
    if min_delay is None:
        min_delay = 5  # Default minimum delay

    # If enough time has passed, send immediately
    if seconds_since_last >= min_delay:
        return 0

    # Calculate remaining delay needed
    remaining_delay = min_delay - seconds_since_last
    logger.info(
        f"Last message sent {seconds_since_last}s ago, applying {remaining_delay}s delay "
        f"to maintain minimum {min_delay}s interval"
    )
    return remaining_delay
