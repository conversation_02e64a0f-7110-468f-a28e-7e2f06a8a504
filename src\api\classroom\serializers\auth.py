from rest_framework import serializers
from core.models import User, EducationalInstitution, Major, Term


class ProfileSerializer(serializers.ModelSerializer):
    institution = serializers.CharField(
        source="educational_institution.name", read_only=True
    )
    institution_id = serializers.UUIDField(
        source="educational_institution.eiid", read_only=True
    )

    major = serializers.CharField(source="major.name", read_only=True)
    major_id = serializers.UUIDField(source="major.mid", read_only=True)

    term = serializers.CharField(source="term.name", read_only=True)
    term_id = serializers.UUIDField(source="term.tid", read_only=True)

    class Meta:
        model = User
        fields = [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "ocupation",
            "institution",
            "institution_id",
            "major",
            "major_id",
            "term",
            "term_id",
        ]


class UpdateProfileSerializer(serializers.ModelSerializer):
    institution_id = serializers.PrimaryKeyRelatedField(
        queryset=EducationalInstitution.objects.all(),
        source="educational_institution",
        required=False,
    )
    major_id = serializers.PrimaryKeyRelatedField(
        queryset=Major.objects.all(), source="major", required=False
    )
    term_id = serializers.PrimaryKeyRelatedField(
        queryset=Term.objects.all(), source="term", required=False
    )

    ocupation = serializers.ChoiceField(choices=User.OCUPATION_CHOICES, required=False)

    class Meta:
        model = User
        fields = [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "ocupation",
            "institution_id",
            "major_id",
            "term_id",
        ]

    def validate_email(self, value):
        user = self.context["request"].user
        if User.objects.exclude(pk=user.pk).filter(email=value).exists():
            raise serializers.ValidationError("Este correo ya está en uso.")
        return value

    def validate_whatsapp(self, value):
        user = self.context["request"].user
        if User.objects.exclude(pk=user.pk).filter(phone_number=value).exists():
            raise serializers.ValidationError("Este número de WhatsApp ya está en uso.")
        return value


class InstitutionSelectSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(source="eiid")

    class Meta:
        model = EducationalInstitution
        fields = ["id", "name"]


class MajorSelectSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(source="mid")

    class Meta:
        model = Major
        fields = ["id", "name"]


class TermSelectSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(source="tid")

    class Meta:
        model = Term
        fields = ["id", "name"]
