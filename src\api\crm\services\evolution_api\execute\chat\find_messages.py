"""
Find messages via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def find_messages(
    instance_name: str, remote_jid: str, page: int = 1, offset: int = 10
) -> Dict[str, Any]:
    """
    Find messages in a chat

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to search messages in
        page: Page number for pagination (default: 1)
        offset: Number of messages per page (default: 10)

    Returns:
        Dict containing the API response with messages

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Ensure proper WhatsApp format
        contact_jid = remote_jid
        if "@" not in contact_jid:
            contact_jid = f"{contact_jid}@s.whatsapp.net"

        # Build request body
        body = {
            "where": {
                "key": {
                    "remoteJid": contact_jid,
                },
            },
            "page": page,
            "offset": offset,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/findMessages/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Messages search completed for {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to find messages: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error finding messages: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
