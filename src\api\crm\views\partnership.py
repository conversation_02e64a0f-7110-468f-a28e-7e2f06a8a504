from rest_framework import viewsets, status
from rest_framework.response import Response
from core.models import Partnership
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.partnership import (
    CrmBasePartnershipSerializer,
    CrmRetrievePartnershipSerializer,
    CrmCreatePartnershipSerializer,
    CrmUpdatePartnershipSerializer,
)
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmPartnershipViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Partnership
    queryset = Partnership.objects.filter(deleted=False).order_by("created_at")
    swagger_tags = ["Partnerships"]
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    def get_serializer_class(self, *args, **kwargs):
        """Return the appropriate serializer class based on the action."""
        if self.action == "list":
            return CrmBasePartnershipSerializer
        elif self.action == "retrieve":
            return CrmRetrievePartnershipSerializer
        elif self.action == "create":
            return CrmCreatePartnershipSerializer
        elif self.action in ["update", "partial_update"]:
            return CrmUpdatePartnershipSerializer
        return super().get_serializer_class(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Create a new Partnership"""

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmRetrievePartnershipSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)
