"""
Fetch business profile via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def fetch_business_profile(instance_name: str, remote_jid: str) -> Dict[str, Any]:
    """
    Fetch WhatsApp business profile information

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or contact ID to fetch business profile for

    Returns:
        Dict containing the API response with business profile data

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Ensure proper WhatsApp format
        contact_number = remote_jid
        if "@" not in contact_number:
            contact_number = f"{contact_number}@s.whatsapp.net"

        # Build request body
        body = {
            "number": contact_number,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/fetchBusinessProfile/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Business profile fetched successfully for {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch business profile for {remote_jid}: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching business profile: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
