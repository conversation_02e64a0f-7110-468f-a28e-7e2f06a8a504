import requests
from typing import Any, Dict, List
import mercadopago as mp
from uuid import uuid4
from django.conf import settings
from rest_framework import viewsets, status
from rest_framework.decorators import (
    action,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from rest_framework.request import Request
from rest_framework.response import Response
from api.website.serializers.payment import (
    ProcessPaymentSerializer,
    ProcessYapeSerializer,
    CreatePaypalOrderSerializer,
)
from core.models import Offering
from api.mixins import SwaggerTagMixin
from services.payment.paypal import PayPal


class PaymentViewSet(viewsets.GenericViewSet, SwaggerTagMixin):
    """
    ViewSet for handling payment-related operations.

    Provides endpoints for processing payments and handling webhooks
    from payment providers.
    """

    swagger_tags = ["Payment"]
    serializer_class = ProcessPaymentSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self._sdk: mp.SDK | None = None

    @property
    def sdk(self) -> mp.SDK:
        """
        Lazy initialization of MercadoPago SDK.
        Raises:
            ValueError: If MP_ACCESS_TOKEN is not configured
        """
        if self._sdk is None:
            access_token = settings.MP_ACCESS_TOKEN
            if not access_token:
                raise ValueError("MercadoPago ACCESS_TOKEN not configured")
            self._sdk = mp.SDK(access_token)
        return self._sdk

    @action(detail=False, methods=["POST"])
    def process_payment(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """
        Process a payment through MercadoPago.
        Args:
            request: The HTTP request containing payment data
        Returns:
            Response: Payment processing result
        Raises:
            ValidationError: If payment data is invalid
        """
        try:
            serializer = self.get_serializer(
                data=request.data, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)

            payment_data: Dict[str, Any] = serializer.validated_data["payment_data"]

            request_options = mp.config.RequestOptions()
            request_options.custom_headers = {"x-idempotency-key": str(uuid4())}

            result = self.sdk.payment().create(
                payment_data,
                request_options,
            )

            return Response(data=result, status=status.HTTP_202_ACCEPTED)
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["POST"])
    def process_yape(self, request: Request, *args, **kwargs) -> Response:
        """
        Process a payment through Yape.

        Args:
            request: The HTTP request containing payment data

        Returns:
            Response: Payment processing result

        Raises:
            ValidationError: If payment data is invalid
            requests.RequestException: If Yape API request fails
        """
        try:
            serializer = ProcessYapeSerializer(
                data=request.data, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)

            public_key = settings.MP_PUBLIC_KEY
            if not public_key:
                return Response(
                    {"error": "MercadoPago public key not configured"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            items = serializer.validated_data["items"]
            total_amount = self.get_total_amount(items)

            yape_info = serializer.validated_data["yape_info"]
            yape_response = requests.post(
                "https://api.mercadopago.com/platforms/pci/yape/v1/payment",
                json={
                    "phoneNumber": yape_info["phone_number"],
                    "otp": yape_info["otp"],
                    "requestId": str(uuid4()),
                },
                params={"public_key": public_key},
                timeout=10,
            )

            yape_response.raise_for_status()
            yape_data = yape_response.json()

            if "id" not in yape_data:
                return Response(
                    {"error": "Invalid response from Yape API"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            payment_data = {
                "token": yape_data["id"],
                "transaction_amount": total_amount,
                "installments": 1,
                "description": "Orden de compra",
                "payment_method_id": "yape",
                "payer": {
                    "email": request.user.email,
                },
            }

            request_options = mp.config.RequestOptions()
            request_options.custom_headers = {"x-idempotency-key": str(uuid4())}

            result = self.sdk.payment().create(
                payment_data,
                request_options,
            )

            return Response(data=result, status=status.HTTP_202_ACCEPTED)

        except requests.RequestException as e:
            return Response(
                {"error": f"Yape API request failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_total_amount(self, items: List[Dict[str, Any]]):
        """
        Calculate the total amount of a payment based on the items (Offering model).
        Args:
            items: List of items in the payment
        Returns:
            float: Total amount of the payment
        """
        total = 0.0
        for item in items:
            offering = Offering.objects.get(oid=item["oid"])
            total += float(offering.price)

        return total

    @action(detail=False, methods=["POST"])
    def paypal_order(self, request: Request, *args, **kwargs) -> Response:
        serializer = CreatePaypalOrderSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        paypal = PayPal()

        pp_order = paypal.create_order(serializer.validated_data["items"])
        return Response(
            data=pp_order,
            status=status.HTTP_201_CREATED,
        )
