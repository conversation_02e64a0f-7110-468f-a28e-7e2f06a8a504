from api.crm.services.tokechat.api_request import fetcher


def get_flows():
    try:
        flows = fetcher(url="/flows/")
        if flows.get("success"):
            return flows.get("response")["data"]

        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


"""
Get a flow by name
"""


def get_flow_by_name(name: str):
    try:
        flows = get_flows()
        if len(flows) > 0:
            return next((item for item in flows if item["name"] == name), None)

        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None
