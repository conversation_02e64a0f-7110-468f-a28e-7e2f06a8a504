"""
Create basic WhatsApp instance via Evolution API
"""

import logging
from typing import Dict, Any, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def create_instance_basic(
    instance_name: str,
    token: Optional[str] = None,
    number: Optional[str] = None,
    instance_settings: Optional[Dict[str, Any]] = None,
    proxy_settings: Optional[Dict[str, Any]] = None,
    webhook_settings: Optional[Dict[str, Any]] = None,
    rabbitmq_settings: Optional[Dict[str, Any]] = None,
    chatwoot_settings: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create a basic WhatsApp instance

    Args:
        instance_name: Name for the new instance
        token: Optional token for the instance
        number: Optional phone number for the instance
        instance_settings: Instance configuration settings
        proxy_settings: Proxy configuration
        webhook_settings: Webhook configuration
        rabbitmq_settings: RabbitMQ configuration
        chatwoot_settings: Chatwoot integration settings

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build basic request body
        body = {
            "instanceName": instance_name,
            "integration": "WHATSAPP-BAILEYS",
        }

        # Add optional token and number
        if token:
            body["token"] = token
        if number:
            body["number"] = number

        # Add instance settings if provided
        if instance_settings:
            valid_settings = {
                "rejectCall": instance_settings.get("reject_call"),
                "msgCall": instance_settings.get("msg_call"),
                "groupsIgnore": instance_settings.get("groups_ignore"),
                "alwaysOnline": instance_settings.get("always_online"),
                "readMessages": instance_settings.get("read_messages"),
                "readStatus": instance_settings.get("read_status"),
                "syncFullHistory": instance_settings.get("sync_full_history"),
            }
            # Only add non-None values
            for key, value in valid_settings.items():
                if value is not None:
                    body[key] = value

        # Add proxy settings if provided
        if proxy_settings:
            body.update(
                {
                    "proxyHost": proxy_settings.get("proxy_host", ""),
                    "proxyPort": str(proxy_settings.get("proxy_port", "1234")),
                    "proxyProtocol": proxy_settings.get("proxy_protocol", ""),
                    "proxyUsername": proxy_settings.get("proxy_username", ""),
                    "proxyPassword": proxy_settings.get("proxy_password", ""),
                }
            )

        # Add webhook settings if provided
        if webhook_settings:
            body["webhook"] = {
                "url": webhook_settings.get("webhook_url", ""),
                "byEvents": webhook_settings.get("webhook_by_events", False),
                "base64": webhook_settings.get("webhook_base64", False),
                "events": webhook_settings.get("webhook_events", []),
            }

        # Add RabbitMQ settings if provided
        if rabbitmq_settings:
            body["rabbitmq"] = {
                "enabled": rabbitmq_settings.get("rabbitmq_enabled", False),
                "events": rabbitmq_settings.get("rabbitmq_events", []),
            }

        # Add Chatwoot settings if provided
        if chatwoot_settings:
            chatwoot_fields = {
                "chatwootAccountId": chatwoot_settings.get("chatwoot_account_id"),
                "chatwootToken": chatwoot_settings.get("chatwoot_token"),
                "chatwootUrl": chatwoot_settings.get("chatwoot_url"),
                "chatwootSignMsg": chatwoot_settings.get("chatwoot_sign_msg", False),
                "chatwootReopenConversation": chatwoot_settings.get(
                    "chatwoot_reopen_conversation"
                ),
                "chatwootConversationPending": chatwoot_settings.get(
                    "chatwoot_conversation_pending"
                ),
                "chatwootImportContacts": chatwoot_settings.get(
                    "chatwoot_import_contacts"
                ),
                "chatwootNameInbox": chatwoot_settings.get("chatwoot_name_inbox"),
                "chatwootMergeBrazilContacts": chatwoot_settings.get(
                    "chatwoot_merge_brazil_contacts"
                ),
                "chatwootImportMessages": chatwoot_settings.get(
                    "chatwoot_import_messages"
                ),
                "chatwootDaysLimitImportMessages": chatwoot_settings.get(
                    "chatwoot_days_limit_import_messages"
                ),
                "chatwootOrganization": chatwoot_settings.get("chatwoot_organization"),
                "chatwootLogo": chatwoot_settings.get("chatwoot_logo"),
            }
            # Only add non-None values
            for key, value in chatwoot_fields.items():
                if value is not None:
                    body[key] = value

        # Make the request
        response = evolution_request(uri="/instance/create", method="POST", data=body)

        logger.info(f"Instance '{instance_name}' created successfully")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to create instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating instance '{instance_name}': {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
