"""
Sales Dashboard Views for CRM
Provides analytics endpoints for sales dashboard
"""

from django.db.models import Q, Sum, Case, When, DecimalField, F, Count, Value
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from decimal import Decimal
from core.models import Order, OrderItem, Payment
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.sales import CrmDashboardSalesFilter
from api.crm.serializers.dashboard.sales import (
    CrmDashboardSalesSerializer,
    CrmDashboardSalesFilterOptionsSerializer,
)
from api.crm.serializers.order import CrmOrderSerializer
from api.crm.utils.dashboard import (
    DashboardUtils,
    CurrencyConverter,
)
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.conf import settings


class CrmDashboardSalesViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Sales Dashboard Analytics
    Provides various endpoints for sales dashboard statistics and charts
    """

    model_class = Order
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    filterset_class = CrmDashboardSalesFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardSalesSerializer
    currency_converter = CurrencyConverter()

    def get_serializer_class(self):
        if self.action == "invalidate_cache":
            return None
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_sales")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for orders (non-deleted orders only)
        """
        return Order.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "created_at_after": self.request.GET.get("created_at_after", ""),
            "created_at_before": self.request.GET.get("created_at_before", ""),
            "stages": self.request.GET.get("stages", ""),
            "products": self.request.GET.get("products", ""),
            "sales_agent": self.request.GET.get("sales_agent", ""),
            "months": self.request.GET.get("months", "10"),
            "benefits": self.request.GET.get("benefits", ""),
        }

    # ==== Utilities ====

    def _get_report_dates(self):
        """Get current and previous period dates"""
        return DashboardUtils.get_report_dates(self.request)

    def _get_queryset_excluding_filters(self, exclude_fields):
        """Get queryset excluding specific filters"""
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    def _get_totals_sales_amount_optimized(self, orders_queryset):
        """
        Optimized version using SQL aggregations on OrderItems
        Avoids N+1 queries from order.total @property
        """
        # Get conversion rate once
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Get order IDs from the queryset
        order_ids = list(orders_queryset.values_list("oid", flat=True))

        if not order_ids:
            return {
                "total_sales_amount_usd": Decimal("0.00"),
                "total_sales_amount_pen": Decimal("0.00"),
                "total_sales_amount": Decimal("0.00"),
            }

        # Aggregate sales amount from OrderItems using SQL
        items_aggregation = OrderItem.objects.filter(
            order__oid__in=order_ids, deleted=False
        ).aggregate(
            # For international orders (USD)
            total_sales_amount_usd=Sum(
                Case(
                    When(
                        order__is_international=True,
                        custom_amount__isnull=False,
                        then=F("custom_amount") * F("quantity"),
                    ),
                    When(
                        order__is_international=True,
                        custom_amount__isnull=True,
                        then=F("offering__foreign_base_price")
                        * (100 - F("offering__discount"))
                        / 100
                        * F("quantity"),
                    ),
                    default=0,
                    output_field=DecimalField(max_digits=15, decimal_places=2),
                )
            ),
            # For national orders (PEN)
            total_sales_amount_pen=Sum(
                Case(
                    When(
                        order__is_international=False,
                        custom_amount__isnull=False,
                        then=F("custom_amount") * F("quantity"),
                    ),
                    When(
                        order__is_international=False,
                        custom_amount__isnull=True,
                        then=F("offering__base_price")
                        * (100 - F("offering__discount"))
                        / 100
                        * F("quantity"),
                    ),
                    default=0,
                    output_field=DecimalField(max_digits=15, decimal_places=2),
                )
            ),
        )

        # Calculate totals
        total_sales_amount_usd = items_aggregation["total_sales_amount_usd"] or Decimal(
            "0.00"
        )
        total_sales_amount_pen = items_aggregation["total_sales_amount_pen"] or Decimal(
            "0.00"
        )

        # Convert USD to PEN and calculate total
        usd_in_pen = total_sales_amount_usd * conversion_rate
        total_sales_amount = total_sales_amount_pen + usd_in_pen

        return {
            "total_sales_amount_usd": total_sales_amount_usd,
            "total_sales_amount_pen": total_sales_amount_pen,
            "total_sales_amount": total_sales_amount,
        }

    # ==== CALCULATION FUNCTIONS ====

    def calculate_general_stats(self):
        """
        Calculate general dashboard statistics - OPTIMIZED
        """
        dates = self._get_report_dates()
        filtered_queryset = self.get_filtered_queryset()
        base_queryset = self._get_queryset_excluding_filters("created_at")

        # Current period dates
        current_start = dates["current_start"]
        current_end = dates["current_end"]
        previous_start = dates["previous_start"]
        previous_end = dates["previous_end"]

        # Total sales amount - OPTIMIZED: Use SQL aggregations instead of order.total
        sold_orders_queryset = base_queryset.filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=current_start,
            sold_at__lte=current_end,
        )

        total_sales = self._get_totals_sales_amount_optimized(sold_orders_queryset)
        total_sales_amount_usd = total_sales["total_sales_amount_usd"]
        total_sales_amount_pen = total_sales["total_sales_amount_pen"]
        total_sales_amount = total_sales["total_sales_amount"]

        # Total unique clients - OPTIMIZED: Single query
        clients = filtered_queryset.values("owner").distinct().count()

        # CHANGED: Prospect conversiont rate
        # Only count prospects that became prospects in the current period and also sold in the same period

        current_metrics = base_queryset.aggregate(
            # Prospects created in current period
            current_prospects=Count(
                "oid",
                filter=Q(
                    prospect_at__gte=current_start,
                    prospect_at__lte=current_end,
                ),
                distinct=True,
            ),
            # Of those prospects, how many got sold in same period
            current_prospect_to_sold=Count(
                "oid",
                filter=Q(
                    prospect_at__gte=current_start,
                    prospect_at__lte=current_end,
                    sold_at__gte=current_start,
                    sold_at__lte=current_end,
                    stage=Order.SOLD_STAGE,
                ),
                distinct=True,
            ),
        )

        previous_metrics = base_queryset.aggregate(
            previous_prospects=Count(
                "oid",
                filter=Q(
                    prospect_at__gte=previous_start,
                    prospect_at__lte=previous_end,
                    prospect_at__isnull=False,
                ),
                distinct=True,
            ),
            previous_prospect_to_sold=Count(
                "oid",
                filter=Q(
                    prospect_at__gte=previous_start,
                    prospect_at__lte=previous_end,
                    prospect_at__isnull=False,
                    sold_at__gte=previous_start,
                    sold_at__lte=previous_end,
                    sold_at__isnull=False,
                    stage=Order.SOLD_STAGE,
                ),
                distinct=True,
            ),
        )

        # Extract values
        current_prospects = current_metrics["current_prospects"]
        current_prospect_to_sold = current_metrics["current_prospect_to_sold"]
        previous_prospects = previous_metrics["previous_prospects"]
        previous_prospect_to_sold = previous_metrics["previous_prospect_to_sold"]

        # Calculate prospect conversion rates (speed of conversion)
        current_prospect_conversion_rate = (
            (current_prospect_to_sold / current_prospects * 100)
            if current_prospects > 0
            else 0
        )

        previous_prospect_conversion_rate = (
            (previous_prospect_to_sold / previous_prospects * 100)
            if previous_prospects > 0
            else 0
        )

        conversion_percentage, conversion_tendency = (
            DashboardUtils.calculate_percentage_change(
                current_prospect_conversion_rate, previous_prospect_conversion_rate
            )
        )

        # Sales this month - OPTIMIZED: Single query for both periods
        sales_metrics = base_queryset.aggregate(
            current_month_sales=Count(
                "oid",
                filter=Q(
                    stage=Order.SOLD_STAGE,
                    sold_at__gte=current_start,
                    sold_at__lte=current_end,
                ),
            ),
            previous_month_sales=Count(
                "oid",
                filter=Q(
                    stage=Order.SOLD_STAGE,
                    sold_at__gte=previous_start,
                    sold_at__lte=previous_end,
                ),
            ),
        )

        current_month_sales = sales_metrics["current_month_sales"]
        previous_month_sales = sales_metrics["previous_month_sales"]

        sales_percentage, sales_tendency = DashboardUtils.calculate_percentage_change(
            current_month_sales, previous_month_sales
        )

        return {
            # "total_sales_amount": round(float(total_sales_amount), 2),
            "total_sales_amount": {
                "pen": round(total_sales_amount_pen, 2),
                "usd": round(total_sales_amount_usd, 2),
                "total": round(total_sales_amount, 2),
                "currency_exchange_rate": CurrencyConverter.get_conversion_rate(),
            },
            "clients": clients,
            "conversion_rate": {
                "value": round(current_prospect_conversion_rate, 2),
                "percentage": round(conversion_percentage, 2),
                "tendency": conversion_tendency,
            },
            "sales_this_month": {
                "value": current_month_sales,
                "percentage": round(sales_percentage, 2),
                "tendency": sales_tendency,
            },
        }

    def calculate_conversion_funnel(self):
        """
        Calculate conversion funnel data based on cumulative progression through stages
        Shows how many orders have reached each stage during the selected period
        """
        dates = self._get_report_dates()
        current_start = dates["current_start"]
        current_end = dates["current_end"]

        # Get base queryset excluding date filters to apply stage-specific date filters
        filtered_base = self._get_queryset_excluding_filters("created_at")

        funnel_data = []

        # Define stages with their timestamp fields
        stages = [
            ("prospect", "Prospecto", "prospect_at"),
            ("interested", "Interesado", "interested_at"),
            ("to_pay", "Por pagar", "to_pay_at"),
            ("sold", "Vendido", "sold_at"),
        ]

        for stage_code, stage_name, timestamp_field in stages:
            # Count orders that reached this stage during the selected period
            stage_filter = {
                f"{timestamp_field}__gte": current_start,
                f"{timestamp_field}__lte": current_end,
                f"{timestamp_field}__isnull": False,
            }

            count = filtered_base.filter(**stage_filter).count()

            funnel_data.append(
                {"name": stage_name, "value": count, "stage": stage_code}
            )

        return funnel_data

    def calculate_conversion_by_stages(self):
        """
        Calculate conversion rates between stages using cumulative progression
        """
        dates = self._get_report_dates()
        current_start = dates["current_start"]
        current_end = dates["current_end"]

        # Get base queryset excluding date filters
        filtered_base = self._get_queryset_excluding_filters("created_at")

        # Count orders that reached each stage during the period
        prospect_count = filtered_base.filter(
            prospect_at__gte=current_start,
            prospect_at__lte=current_end,
            prospect_at__isnull=False,
            stage=Order.PROSPECT_STAGE,
        ).count()

        interested_count = filtered_base.filter(
            interested_at__gte=current_start,
            interested_at__lte=current_end,
            interested_at__isnull=False,
            stage=Order.INTERESTED_STAGE,
        ).count()

        to_pay_count = filtered_base.filter(
            to_pay_at__gte=current_start,
            to_pay_at__lte=current_end,
            to_pay_at__isnull=False,
            stage=Order.TO_PAY_STAGE,
        ).count()

        sold_count = filtered_base.filter(
            sold_at__gte=current_start,
            sold_at__lte=current_end,
            sold_at__isnull=False,
            stage=Order.SOLD_STAGE,
        ).count()

        lost_count = (
            filtered_base.filter(
                lost_at__gte=current_start,
                lost_at__lte=current_end,
                lost_at__isnull=False,
                stage=Order.LOST_STAGE,
            )
        ).count()

        total_unique_orders = (
            filtered_base.filter(
                Q(prospect_at__gte=current_start, prospect_at__lte=current_end)
                | Q(interested_at__gte=current_start, interested_at__lte=current_end)
                | Q(to_pay_at__gte=current_start, to_pay_at__lte=current_end)
                | Q(sold_at__gte=current_start, sold_at__lte=current_end)
            )
            .distinct()
            .count()
        )

        # Calculate conversion percentages
        prospect_to_interested = (
            (interested_count / prospect_count * 100) if prospect_count > 0 else 0
        )
        interested_to_pay = (
            (to_pay_count / interested_count * 100) if interested_count > 0 else 0
        )
        to_pay_to_paid = (sold_count / to_pay_count * 100) if to_pay_count > 0 else 0

        # General conversion rate (prospect to sold)
        general_conversion_rate = (
            (sold_count / total_unique_orders * 100) if total_unique_orders > 0 else 0
        )

        return {
            "prospect_to_interested": {
                "percentage": round(prospect_to_interested, 1),
                "from_count": prospect_count,
                "to_count": interested_count,
            },
            "interested_to_pay": {
                "percentage": round(interested_to_pay, 1),
                "from_count": interested_count,
                "to_count": to_pay_count,
            },
            "to_pay_to_paid": {
                "percentage": round(to_pay_to_paid, 1),
                "from_count": to_pay_count,
                "to_count": sold_count,
            },
            "resume": {
                "general_conversion_rate": round(general_conversion_rate, 1),
                "lost": lost_count,
                "closed_sales": sold_count,
            },
        }

    def calculate_orders_by_month(self):
        """
        Calculate orders by month for the last 10 months
        """
        months = DashboardUtils.get_month_names()
        filtered_queryset = self._get_queryset_excluding_filters("created_at")

        # Get last 10 months
        current_date = timezone.now()
        orders_by_month = []

        for i in range(9, -1, -1):  # Last 10 months
            month_date = current_date - timedelta(days=30 * i)
            month_start = month_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # Calculate next month start
            if month_start.month == 12:
                month_end = month_start.replace(
                    year=month_start.year + 1, month=1
                ) - timedelta(seconds=1)
            else:
                month_end = month_start.replace(
                    month=month_start.month + 1
                ) - timedelta(seconds=1)

            month_orders = filtered_queryset.filter(
                created_at__gte=month_start, created_at__lte=month_end
            )

            month_data = {
                "month": months[month_start.month - 1],
                "total": month_orders.count(),
                "prospect": month_orders.filter(stage=Order.PROSPECT_STAGE).count(),
                "interested": month_orders.filter(stage=Order.INTERESTED_STAGE).count(),
                "to_pay": month_orders.filter(stage=Order.TO_PAY_STAGE).count(),
                "sold": month_orders.filter(stage=Order.SOLD_STAGE).count(),
                "lost": month_orders.filter(stage=Order.LOST_STAGE).count(),
            }

            orders_by_month.append(month_data)

        return orders_by_month

    def calculate_orders_by_stage(self):
        """
        Calculate orders by stage for pie chart
        """
        report_dates = self._get_report_dates()

        current_start = report_dates["current_start"]
        current_end = report_dates["current_end"]

        filtered_queryset = self._get_queryset_excluding_filters("created_at")

        # Use a single aggregation query with conditional counting for each stage
        aggregation = filtered_queryset.aggregate(
            prospect_count=Count(
                "oid",
                filter=Q(
                    stage=Order.PROSPECT_STAGE,
                    prospect_at__gte=current_start,
                    prospect_at__lte=current_end,
                ),
            ),
            interested_count=Count(
                "oid",
                filter=Q(
                    stage=Order.INTERESTED_STAGE,
                    interested_at__gte=current_start,
                    interested_at__lte=current_end,
                ),
            ),
            to_pay_count=Count(
                "oid",
                filter=Q(
                    stage=Order.TO_PAY_STAGE,
                    to_pay_at__gte=current_start,
                    to_pay_at__lte=current_end,
                ),
            ),
            sold_count=Count(
                "oid",
                filter=Q(
                    stage=Order.SOLD_STAGE,
                    sold_at__gte=current_start,
                    sold_at__lte=current_end,
                ),
            ),
            lost_count=Count(
                "oid",
                filter=Q(
                    stage=Order.LOST_STAGE,
                    lost_at__gte=current_start,
                    lost_at__lte=current_end,
                ),
            ),
        )

        stages_data = [
            {
                "name": "Prospecto",
                "value": aggregation["prospect_count"],
                "stage": Order.PROSPECT_STAGE,
            },
            {
                "name": "Interesado",
                "value": aggregation["interested_count"],
                "stage": Order.INTERESTED_STAGE,
            },
            {
                "name": "Por pagar",
                "value": aggregation["to_pay_count"],
                "stage": Order.TO_PAY_STAGE,
            },
            {
                "name": "Vendido",
                "value": aggregation["sold_count"],
                "stage": Order.SOLD_STAGE,
            },
            {
                "name": "Perdido",
                "value": aggregation["lost_count"],
                "stage": Order.LOST_STAGE,
            },
        ]

        return stages_data

    def calculate_weekly_stage_evolution(self):
        """
        Calculate weekly evolution of orders by stage for current week
        Fixed weekly metric showing progression through sales stages
        """
        # Get current week (Monday to Sunday) in local timezone
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)
        current_weekday = now.weekday()  # Monday = 0, Sunday = 6
        week_start = now - timedelta(days=current_weekday)
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

        # Generate 7 days of the week
        weekly_evolution = []
        days_names = ["Lun", "Mar", "Mié", "Jue", "Vie", "Sáb", "Dom"]

        # Apply non-date filters
        base_queryset = self._get_queryset_excluding_filters("created_at")

        for i in range(7):
            day_date = week_start + timedelta(days=i)
            day_start = day_date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_date.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )

            # Count orders by stage for this day (cumulative up to this day)
            day_data = {
                "day": days_names[i],
                "date": day_date.strftime("%Y-%m-%d"),
                "prospect": base_queryset.filter(
                    prospect_at__gte=day_start,
                    prospect_at__lte=day_end,
                    prospect_at__isnull=False,
                ).count(),
                "interested": base_queryset.filter(
                    interested_at__gte=day_start,
                    interested_at__lte=day_end,
                    interested_at__isnull=False,
                ).count(),
                "to_pay": base_queryset.filter(
                    to_pay_at__gte=day_start,
                    to_pay_at__lte=day_end,
                    to_pay_at__isnull=False,
                ).count(),
                "sold": base_queryset.filter(
                    sold_at__gte=day_start, sold_at__lte=day_end, sold_at__isnull=False
                ).count(),
                "lost": base_queryset.filter(
                    lost_at__gte=day_start, lost_at__lte=day_end, lost_at__isnull=False
                ).count(),
            }

            weekly_evolution.append(day_data)

        return weekly_evolution

    def calculate_revenue_by_month(self):
        """
        Calculate revenue by month for the last 10 months (in PEN) - OPTIMIZED
        """
        months = DashboardUtils.get_month_names()
        filtered_queryset = self.get_filtered_queryset()

        # Get conversion rate once
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Get last 10 months
        current_date = timezone.now()
        revenue_by_month = []

        for i in range(9, -1, -1):  # Last 10 months
            month_date = current_date - timedelta(days=30 * i)
            month_start = month_date.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # Calculate next month start
            if month_start.month == 12:
                month_end = month_start.replace(
                    year=month_start.year + 1, month=1
                ) - timedelta(seconds=1)
            else:
                month_end = month_start.replace(
                    month=month_start.month + 1
                ) - timedelta(seconds=1)

            # Get sold orders for this month
            month_orders = filtered_queryset.filter(
                stage=Order.SOLD_STAGE,
                sold_at__gte=month_start,
                sold_at__lte=month_end,
                deleted=False,
            )

            # Get order IDs for this month
            order_ids = list(month_orders.values_list("oid", flat=True))

            if not order_ids:
                revenue_by_month.append(
                    {
                        "month": months[month_start.month - 1],
                        "revenue": 0.00,
                    }
                )
                continue

            # Calculate revenue using SQL aggregations
            items_aggregation = OrderItem.objects.filter(
                order__oid__in=order_ids, deleted=False
            ).aggregate(
                # For international orders (USD) - convert to PEN
                revenue_usd=Sum(
                    Case(
                        When(
                            order__is_international=True,
                            custom_amount__isnull=False,
                            then=F("custom_amount")
                            * F("quantity")
                            * Value(conversion_rate),
                        ),
                        When(
                            order__is_international=True,
                            custom_amount__isnull=True,
                            then=F("offering__foreign_base_price")
                            * (100 - F("offering__discount"))
                            / 100
                            * F("quantity")
                            * Value(conversion_rate),
                        ),
                        default=0,
                        output_field=DecimalField(max_digits=15, decimal_places=2),
                    )
                ),
                # For national orders (PEN)
                revenue_pen=Sum(
                    Case(
                        When(
                            order__is_international=False,
                            custom_amount__isnull=False,
                            then=F("custom_amount") * F("quantity"),
                        ),
                        When(
                            order__is_international=False,
                            custom_amount__isnull=True,
                            then=F("offering__base_price")
                            * (100 - F("offering__discount"))
                            / 100
                            * F("quantity"),
                        ),
                        default=0,
                        output_field=DecimalField(max_digits=15, decimal_places=2),
                    )
                ),
            )

            revenue_usd = items_aggregation["revenue_usd"] or Decimal("0.00")
            revenue_pen = items_aggregation["revenue_pen"] or Decimal("0.00")
            total_revenue = revenue_usd + revenue_pen

            revenue_by_month.append(
                {
                    "month": months[month_start.month - 1],
                    "revenue": round(float(total_revenue), 2),
                }
            )

        return revenue_by_month

    def calculate_revenue_by_sales_agent(self, user, is_management=False):
        """
        Calculate revenue by sales agent (in PEN) - OPTIMIZED
        """
        dates = self._get_report_dates()

        filtered_queryset = self._get_queryset_excluding_filters("created_at").filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=dates["current_start"],
            sold_at__lte=dates["current_end"],
            sales_agent__isnull=False,
            deleted=False,
        )

        # si no está en el grupo management, filtrar solo ventas propias
        if not is_management:
            filtered_queryset = filtered_queryset.filter(sales_agent=user)

        # Get conversion rate once
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Get order IDs from filtered queryset
        order_ids = list(filtered_queryset.values_list("oid", flat=True))

        if not order_ids:
            return []

        # Calculate revenue by sales agent using SQL aggregations
        agents_revenue = (
            OrderItem.objects.filter(order__oid__in=order_ids, deleted=False)
            .select_related("order__sales_agent")
            .values(
                "order__sales_agent__uid",
                "order__sales_agent__first_name",
                "order__sales_agent__last_name",
            )
            .annotate(
                # Calculate revenue in PEN for each agent
                revenue=Sum(
                    Case(
                        # International orders: convert USD to PEN
                        When(
                            order__is_international=True,
                            custom_amount__isnull=False,
                            then=F("custom_amount")
                            * F("quantity")
                            * Value(conversion_rate),
                        ),
                        When(
                            order__is_international=True,
                            custom_amount__isnull=True,
                            then=F("offering__foreign_base_price")
                            * (100 - F("offering__discount"))
                            / 100
                            * F("quantity")
                            * Value(conversion_rate),
                        ),
                        # National orders: keep in PEN
                        When(
                            order__is_international=False,
                            custom_amount__isnull=False,
                            then=F("custom_amount") * F("quantity"),
                        ),
                        When(
                            order__is_international=False,
                            custom_amount__isnull=True,
                            then=F("offering__base_price")
                            * (100 - F("offering__discount"))
                            / 100
                            * F("quantity"),
                        ),
                        default=0,
                        output_field=DecimalField(max_digits=15, decimal_places=2),
                    )
                )
            )
            .order_by("-revenue")
        )

        # Calculate total revenue for percentage calculation
        total_revenue = sum(agent["revenue"] or 0 for agent in agents_revenue)

        # Format response data
        revenue_data = []
        for agent_data in agents_revenue:
            agent_revenue = agent_data["revenue"] or Decimal("0.00")
            agent_name = (
                f"{agent_data['order__sales_agent__first_name'] or ''} {agent_data['order__sales_agent__last_name'] or ''}".strip()
                or "Sin nombre"
            )

            percentage = (
                (agent_revenue / total_revenue * 100) if total_revenue > 0 else 0
            )

            revenue_data.append(
                {
                    "name": agent_name,
                    "value": round(float(agent_revenue), 2),
                    "effective_revenue": 0,
                    "agent_uid": str(agent_data["order__sales_agent__uid"]),
                    "percentage": round(percentage, 1),
                }
            )

        # Calculate effective revenue for each agent based on payments
        effective_revenue_by_agent = self._calculate_effective_revenue_by_agent(
            order_ids, conversion_rate
        )

        # Update revenue_data with effective_revenue
        for item in revenue_data:
            agent_uid = item["agent_uid"]
            item["effective_revenue"] = effective_revenue_by_agent.get(agent_uid, 0.0)

        return revenue_data

    def _calculate_effective_revenue_by_agent(self, order_ids, conversion_rate):
        """
        Calculate effective revenue by sales agent based on payments
        Uses created_by field to associate payments with agents
        """
        if not order_ids:
            return {}

        # Get payments for the orders, filtering by deleted=False
        payments_by_agent = (
            Payment.objects.filter(
                order__oid__in=order_ids,
                deleted=False,
                is_paid=True,
                is_lost=False,
                is_refund=False,
                created_by__isnull=False,
            )
            .select_related("created_by")
            .values("created_by__uid")
            .annotate(
                effective_revenue=Sum(
                    Case(
                        # Convert USD payments to PEN
                        When(
                            currency=Payment.USD_CURRENCY,
                            then=F("amount") * Value(conversion_rate),
                        ),
                        # Keep PEN payments as is
                        default=F("amount"),
                        output_field=DecimalField(max_digits=15, decimal_places=2),
                    )
                )
            )
        )

        # Convert to dictionary for easy lookup
        effective_revenue_dict = {}
        for payment_data in payments_by_agent:
            agent_uid = str(payment_data["created_by__uid"])
            effective_revenue = float(payment_data["effective_revenue"] or 0)
            effective_revenue_dict[agent_uid] = round(effective_revenue, 2)

        return effective_revenue_dict

    """
     TOP SELLING PRODUCTS
    """

    def _calculate_products_payment_data(self, orders_queryset):
        """
        Calculate effective_revenue and lost_revenue for each product/offering
        based on payments associated with orders using optimized SQL aggregations.

        Returns:
            dict: Dictionary with offering_oid as key and payment data as value
        """
        # Get conversion rate once for the entire calculation
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Get order IDs from the filtered queryset
        order_ids = list(orders_queryset.values_list("oid", flat=True))

        if not order_ids:
            return {}

        # Get all order items for the filtered orders with optimized query
        order_items = (
            OrderItem.objects.filter(order__oid__in=order_ids, deleted=False)
            .select_related("order", "offering")
            .annotate(
                # Calculate offering price with discount applied (price = base_price - base_price * discount / 100)
                offering_price=F("offering__base_price")
                - (F("offering__base_price") * F("offering__discount") / Value(100)),
                # Calculate offering foreign price with discount applied
                offering_foreign_price=F("offering__foreign_base_price")
                - (
                    F("offering__foreign_base_price")
                    * F("offering__discount")
                    / Value(100)
                ),
                # Calculate item total using the same logic as effective_total_price
                item_total=Case(
                    # Use custom_amount if available (for sold orders)
                    When(
                        custom_amount__isnull=False,
                        then=F("custom_amount") * F("quantity"),
                    ),
                    # Otherwise use effective price based on international status
                    default=Case(
                        When(
                            order__is_international=True,
                            then=F("offering_foreign_price") * F("quantity"),
                        ),
                        default=F("offering_price") * F("quantity"),
                        output_field=DecimalField(max_digits=10, decimal_places=2),
                    ),
                    output_field=DecimalField(max_digits=10, decimal_places=2),
                ),
            )
        )

        # Calculate order totals for proportion calculation
        order_totals = {}
        for item in order_items:
            order_id = item.order.oid
            if order_id not in order_totals:
                order_totals[order_id] = Decimal("0")
            order_totals[order_id] += item.item_total or Decimal("0")

        # Get payments with currency conversion applied
        payments = (
            Payment.objects.filter(order__oid__in=order_ids, deleted=False)
            .select_related("order")
            .annotate(
                # Convert payment amount to PEN
                payment_amount_pen=Case(
                    When(
                        currency=Payment.USD_CURRENCY,
                        then=F("amount") * Value(conversion_rate),
                    ),
                    default=F("amount"),
                    output_field=DecimalField(max_digits=10, decimal_places=2),
                )
            )
        )

        # Group payments by order for efficient processing
        payments_by_order = {}
        for payment in payments:
            order_id = payment.order.oid
            if order_id not in payments_by_order:
                payments_by_order[order_id] = []
            payments_by_order[order_id].append(payment)

        # Process each item and allocate payments proportionally
        products_payment_data = {}
        for item in order_items:
            offering_oid = str(item.offering.oid)
            order_id = item.order.oid

            if offering_oid not in products_payment_data:
                products_payment_data[offering_oid] = {
                    "effective_revenue": Decimal("0"),
                    "lost_revenue": Decimal("0"),
                }

            # Calculate item proportion in order
            order_total = order_totals.get(order_id, Decimal("0"))
            if order_total > 0:
                item_proportion = (item.item_total or Decimal("0")) / order_total
            else:
                item_proportion = Decimal("0")

            # Allocate payments for this order to this item
            order_payments = payments_by_order.get(order_id, [])
            for payment in order_payments:
                item_payment_amount = payment.payment_amount_pen * item_proportion

                # Categorize payment based on status
                if payment.is_paid and not payment.is_lost and not payment.is_refund:
                    # Effective revenue: paid and not lost/refunded
                    products_payment_data[offering_oid][
                        "effective_revenue"
                    ] += item_payment_amount
                elif payment.is_lost or payment.is_refund:
                    # Lost revenue: marked as lost or refunded
                    products_payment_data[offering_oid][
                        "lost_revenue"
                    ] += item_payment_amount

        return products_payment_data

    def calculate_top_selling_products(self):
        """
        Calculate top selling products/offerings
        """
        dates = self._get_report_dates()

        filtered_queryset = self._get_queryset_excluding_filters("created_at")
        filtered_queryset = filtered_queryset.filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=dates["current_start"],
            sold_at__lte=dates["current_end"],
            deleted=False,
        )

        # Get conversion rate once
        conversion_rate = CurrencyConverter.get_conversion_rate()

        # Get order IDs from filtered queryset
        order_ids = list(filtered_queryset.values_list("oid", flat=True))

        if not order_ids:
            return []

        # Calculate current period products data using SQL aggregations
        current_products_data = (
            OrderItem.objects.filter(order__oid__in=order_ids, deleted=False)
            .select_related("offering")
            .values("offering__oid", "offering__name")
            .annotate(
                count=Sum("quantity"),
                revenue=Sum(
                    Case(
                        # International orders: convert USD to PEN
                        When(
                            order__is_international=True,
                            custom_amount__isnull=False,
                            then=F("custom_amount")
                            * F("quantity")
                            * Value(conversion_rate),
                        ),
                        When(
                            order__is_international=True,
                            custom_amount__isnull=True,
                            then=F("offering__foreign_base_price")
                            * (100 - F("offering__discount"))
                            / 100
                            * F("quantity")
                            * Value(conversion_rate),
                        ),
                        # National orders: keep in PEN
                        When(
                            order__is_international=False,
                            custom_amount__isnull=False,
                            then=F("custom_amount") * F("quantity"),
                        ),
                        When(
                            order__is_international=False,
                            custom_amount__isnull=True,
                            then=F("offering__base_price")
                            * (100 - F("offering__discount"))
                            / 100
                            * F("quantity"),
                        ),
                        default=0,
                        output_field=DecimalField(max_digits=15, decimal_places=2),
                    )
                ),
            )
            .order_by("-revenue")
        )

        # Calculate total revenue for current period
        total_revenue = sum(
            product["revenue"] or 0 for product in current_products_data
        )

        # Convert to dictionary for easier access
        products_data = {}
        for product in current_products_data:
            offering_oid = str(product["offering__oid"])
            products_data[offering_oid] = {
                "name": product["offering__name"],
                "count": product["count"] or 0,
                "revenue": product["revenue"] or Decimal("0.00"),
                "offering_oid": offering_oid,
            }

        # Calculate participation and trends based on revenue participation difference
        previous_start = dates["previous_start"]
        previous_end = dates["previous_end"]

        # Calculate previous period revenue for comparison - OPTIMIZED
        previous_queryset = self.get_queryset().filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=previous_start,
            sold_at__lte=previous_end,
            deleted=False,
        )

        previous_order_ids = list(previous_queryset.values_list("oid", flat=True))

        previous_products_data = {}
        previous_total_revenue = 0

        if previous_order_ids:
            previous_products_aggregation = (
                OrderItem.objects.filter(
                    order__oid__in=previous_order_ids, deleted=False
                )
                .values("offering__oid")
                .annotate(
                    revenue=Sum(
                        Case(
                            # International orders: convert USD to PEN
                            When(
                                order__is_international=True,
                                custom_amount__isnull=False,
                                then=F("custom_amount")
                                * F("quantity")
                                * Value(conversion_rate),
                            ),
                            When(
                                order__is_international=True,
                                custom_amount__isnull=True,
                                then=F("offering__foreign_base_price")
                                * (100 - F("offering__discount"))
                                / 100
                                * F("quantity")
                                * Value(conversion_rate),
                            ),
                            # National orders: keep in PEN
                            When(
                                order__is_international=False,
                                custom_amount__isnull=False,
                                then=F("custom_amount") * F("quantity"),
                            ),
                            When(
                                order__is_international=False,
                                custom_amount__isnull=True,
                                then=F("offering__base_price")
                                * (100 - F("offering__discount"))
                                / 100
                                * F("quantity"),
                            ),
                            default=0,
                            output_field=DecimalField(max_digits=15, decimal_places=2),
                        )
                    )
                )
            )

            for product in previous_products_aggregation:
                offering_oid = str(product["offering__oid"])
                revenue = product["revenue"] or Decimal("0.00")
                previous_products_data[offering_oid] = revenue
                previous_total_revenue += revenue

        # Calculate effective_revenue and lost_revenue for each product
        products_payment_data = self._calculate_products_payment_data(filtered_queryset)

        top_products = []
        for product_data in products_data.values():
            # Current period participation
            current_participation = (
                (product_data["revenue"] / total_revenue * 100)
                if total_revenue > 0
                else 0
            )

            # Previous period participation
            offering_oid = product_data["offering_oid"]
            previous_revenue = previous_products_data.get(offering_oid, 0)
            previous_participation = (
                (previous_revenue / previous_total_revenue * 100)
                if previous_total_revenue > 0
                else 0
            )

            # Calculate participation change
            participation_change = current_participation - previous_participation

            # Determine tendency based on participation change
            if participation_change > 1:  # More than 1% increase
                tendency = "up"
            elif participation_change < -1:  # More than 1% decrease
                tendency = "down"
            else:
                tendency = "flat"

            # Get payment data for this product
            payment_data = products_payment_data.get(
                offering_oid, {"effective_revenue": 0, "lost_revenue": 0}
            )

            # Calculate debt (difference between revenue and effective_revenue)
            revenue = float(product_data["revenue"])
            effective_revenue = float(payment_data["effective_revenue"])
            lost_revenue = float(payment_data["lost_revenue"])

            debt = revenue - effective_revenue - lost_revenue

            top_products.append(
                {
                    "name": product_data["name"],
                    "count": product_data["count"],
                    "revenue": round(revenue, 2),
                    "effective_revenue": round(effective_revenue, 2),
                    "lost_revenue": round(float(payment_data["lost_revenue"]), 2),
                    "debt": round(debt, 2),
                    "offering_oid": product_data["offering_oid"],
                    "participation": {
                        "percentage": round(current_participation, 1),
                        "tendency": tendency,
                        "change": round(participation_change, 1),
                    },
                }
            )

        # Sort by revenue descending
        top_products.sort(key=lambda x: x["revenue"], reverse=True)

        return top_products

    """
    CURRENT MONTH PERFORMANCE
    """

    def calculate_current_month_performance(self):
        """
        Calculate current month performance compared to previous month
        """
        dates = self._get_report_dates()
        selected_start = dates["current_start"]

        # Reporte basado en rango seleccionado, se considera el mes de current_start como base
        # Ej: rango=2025-07-15 - 2025-07-18
        # current_start=2025-07-01, previous_start = 2025-06-01

        current_start = selected_start.replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        current_end = current_start + relativedelta(months=1) - timedelta(seconds=1)

        previous_start = current_start - relativedelta(months=1)
        previous_end = current_start - timedelta(seconds=1)

        filtered_queryset = self._get_queryset_excluding_filters("created_at")

        # Current period sales revenue
        current_orders = filtered_queryset.filter(
            stage=Order.SOLD_STAGE, sold_at__gte=current_start, sold_at__lte=current_end
        )

        total_sales = self._get_totals_sales_amount_optimized(current_orders)
        current_sales_revenue = total_sales["total_sales_amount"]

        # Previous period sales revenue
        previous_orders = self.get_queryset().filter(
            stage=Order.SOLD_STAGE,
            sold_at__gte=previous_start,
            sold_at__lte=previous_end,
        )

        total_previous_sales = self._get_totals_sales_amount_optimized(previous_orders)
        previous_sales_revenue = total_previous_sales["total_sales_amount"]

        # Orders count
        current_orders_count = filtered_queryset.filter(
            created_at__gte=current_start, created_at__lte=current_end
        ).count()

        previous_orders_count = (
            self.get_queryset()
            .filter(created_at__gte=previous_start, created_at__lte=previous_end)
            .count()
        )

        # Conversion rates
        current_converted = filtered_queryset.filter(
            sold_at__gte=current_start, sold_at__lte=current_end
        ).count()

        current_opportunities = (
            filtered_queryset.filter(
                Q(prospect_at__gte=current_start, prospect_at__lte=current_end)
                | Q(interested_at__gte=current_start, interested_at__lte=current_end)
                | Q(to_pay_at__gte=current_start, to_pay_at__lte=current_end)
                | Q(sold_at__gte=current_start, sold_at__lte=current_end)
            )
            .distinct()
            .count()
        )

        previous_converted = (
            self.get_queryset()
            .filter(sold_at__gte=previous_start, sold_at__lte=previous_end)
            .count()
        )

        previous_opportunities = (
            self.get_queryset()
            .filter(
                Q(prospect_at__gte=previous_start, prospect_at__lte=previous_end)
                | Q(interested_at__gte=previous_start, interested_at__lte=previous_end)
                | Q(to_pay_at__gte=previous_start, to_pay_at__lte=previous_end)
                | Q(sold_at__gte=previous_start, sold_at__lte=previous_end)
            )
            .distinct()
            .count()
        )

        current_conversion = (
            (current_converted / current_opportunities * 100)
            if current_opportunities > 0
            else 0
        )
        previous_conversion = (
            (previous_converted / previous_opportunities * 100)
            if previous_opportunities > 0
            else 0
        )

        # Calculate percentage changes and tendencies
        conversion_change = current_conversion - previous_conversion

        conversion_tendency = "flat"
        if conversion_change > 0:
            conversion_tendency = "up"
        elif conversion_change < 0:
            conversion_tendency = "down"

        sales_percentage, sales_tendency = DashboardUtils.calculate_percentage_change(
            current_sales_revenue, previous_sales_revenue
        )

        orders_percentage, orders_tendency = DashboardUtils.calculate_percentage_change(
            current_orders_count, previous_orders_count
        )

        # Calculate sales progress (sold orders vs total orders in current period) - OPTIMIZED
        current_period_orders = filtered_queryset.filter(
            created_at__gte=current_start, created_at__lte=current_end
        )

        current_sold_orders = current_period_orders.filter(stage=Order.SOLD_STAGE)

        # Calculate total revenue from sold orders in PEN using optimized method
        current_sold_revenue_data = self._get_totals_sales_amount_optimized(
            current_sold_orders
        )
        current_sold_revenue = current_sold_revenue_data["total_sales_amount"]

        return {
            "period": current_start.strftime("%B %Y"),
            "sales": {
                "current": round(float(current_sales_revenue), 2),
                "previous": round(float(previous_sales_revenue), 2),
                "percentage_change": round(sales_percentage, 1),
                "tendency": sales_tendency,
            },
            "orders": {
                "current": current_orders_count,
                "previous": previous_orders_count,
                "percentage_change": round(orders_percentage, 1),
                "tendency": orders_tendency,
            },
            "conversion": {
                "current": round(current_conversion, 2),
                "previous": round(previous_conversion, 2),
                "percentage_change": round(conversion_change, 2),
                "tendency": conversion_tendency,
            },
            "sales_progress": {
                "total_revenue": round(float(current_sold_revenue), 2),
                "target": getattr(settings, "MONTHLY_SALES_TARGET"),
            },
        }

    def get_recent_orders(self):
        """
        Get recent orders for dashboard
        """
        filtered_queryset = self.get_filtered_queryset()
        recent_orders = filtered_queryset.order_by("-created_at")[:15]
        return recent_orders

    def get_filter_options(self):
        """
        Get filter options for dashboard
        """
        return {}

    # ==== DASHBOARD ENDPOINTS ====

    @action(detail=False, methods=["GET"], url_path="summary")
    def summary(self, request):
        """
        Essential dashboard data - Fast loading
        Returns core metrics and basic visualizations
        """
        cache_key = (
            f"sales_summary_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate essential data only
            stats = self.calculate_general_stats()
            conversion_funnel = self.calculate_conversion_funnel()
            orders_by_stage = self.calculate_orders_by_stage()
            weekly_stage_evolution = self.calculate_weekly_stage_evolution()
            filter_options = self.get_filter_options()

            # Serialize filter options
            filter_options_serializer = CrmDashboardSalesFilterOptionsSerializer(
                filter_options
            )

            # Build response data
            summary_data = {
                "stats": stats,
                "conversion_funnel": conversion_funnel,
                "orders_by_stage": orders_by_stage,
                "weekly_stage_evolution": weekly_stage_evolution,
                "filter_options": filter_options_serializer.data,
            }

            # Cache the result
            self.cache_manager.set(cache_key, summary_data, timeout=self.cache_timeout)

            return Response(summary_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating summary data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="analytics")
    def analytics(self, request):
        """
        Advanced analytics - Deferred loading
        Returns conversion analysis and performance metrics
        """
        cache_key = (
            f"sales_analytics_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate analytics data
            conversion_by_sale_stages = self.calculate_conversion_by_stages()
            current_month_performance = self.calculate_current_month_performance()

            # Build response data
            analytics_data = {
                "conversion_by_sale_stages": conversion_by_sale_stages,
                "current_month_performance": current_month_performance,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, analytics_data, timeout=self.cache_timeout
            )

            return Response(analytics_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating analytics data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="products")
    def products(self, request):
        """
        Top selling products - Heavy computation, deferred loading
        Returns product performance and trends
        """
        cache_key = (
            f"sales_products_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate products data
            top_selling_products = self.calculate_top_selling_products()

            # Build response data
            products_data = {
                "top_selling_products": top_selling_products,
            }

            # Cache the result
            self.cache_manager.set(cache_key, products_data, timeout=self.cache_timeout)

            return Response(products_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating products data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="agents")
    def agents(self, request):
        """
        Revenue by sales agents - Medium computation, deferred loading
        Returns agent performance metrics
        """
        user = request.user
        is_management = user.groups.filter(name="management").exists()

        cache_key = f"sales_agents_{'management' if is_management else user.pk}_{hash(str(sorted(self.get_cache_key_params().items())))}"

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate agents data
            revenue_by_sales_agent = self.calculate_revenue_by_sales_agent(
                user, is_management
            )

            # Build response data
            agents_data = {
                "revenue_by_sales_agent": revenue_by_sales_agent,
            }

            # Cache the result
            self.cache_manager.set(cache_key, agents_data, timeout=self.cache_timeout)

            return Response(agents_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating agents data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="historical")
    def historical(self, request):
        """
        Historical data - Medium computation, deferred loading
        Returns monthly trends and historical analysis
        """
        cache_key = (
            f"sales_historical_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate historical data
            orders_by_month = self.calculate_orders_by_month()
            # revenue_by_month = self.calculate_revenue_by_month()

            # Build response data
            historical_data = {
                "orders_by_month": orders_by_month,
                # "revenue_by_month": revenue_by_month,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, historical_data, timeout=self.cache_timeout
            )

            return Response(historical_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating historical data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="recent-orders")
    def recent_orders_endpoint(self, request):
        """
        Recent orders - Fast loading, independent data
        Returns latest orders for quick reference
        """
        cache_key = f"sales_recent_orders_{hash(str(sorted(self.get_cache_key_params().items())))}"

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Get recent orders
            recent_orders = self.get_recent_orders()

            # Serialize recent orders
            recent_orders_serializer = CrmOrderSerializer(recent_orders, many=True)

            # Build response data
            orders_data = {
                "recent_orders": recent_orders_serializer.data,
            }

            # Cache the result
            self.cache_manager.set(cache_key, orders_data, timeout=self.cache_timeout)

            return Response(orders_data)

        except Exception as e:
            return Response(
                {"error": f"Error getting recent orders: {str(e)}"}, status=500
            )

    # ==== INVALIDATE CACHE ENDPOINT ====
    @action(detail=False, methods=["POST"], url_path="invalidate-cache")
    def invalidate_cache(self, request):
        """
        Invalidate dashboard cache
        """
        self.cache_manager.invalidate()
        return Response(
            {
                "message": "Dashboard and currency conversion cache invalidated successfully"
            }
        )
