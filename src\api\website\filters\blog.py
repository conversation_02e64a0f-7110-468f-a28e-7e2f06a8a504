from django_filters import rest_framework as filters
from core.models import BlogPost, BlogCategory, BlogTag
from django.db.models import Q


class WebsiteBlogPostFilter(filters.FilterSet):
    search = filters.Char<PERSON>ilter(
        method="filter_search", label="Search by title or summary"
    )
    category = filters.CharFilter(field_name="categories__slug")
    tag = filters.CharFilter(field_name="tags__slug")
    featured = filters.BooleanFilter(field_name="featured")
    published_at = filters.DateFromToRangeFilter()

    def filter_search(self, queryset, _, value):
        return queryset.filter(Q(title__icontains=value))

    class Meta:
        model = BlogPost
        fields = [
            "category",
            "tag",
            "featured",
            "published_at",
        ]
