"""
Update group name via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def update_group_name(
    instance_name: str, group_jid: str, subject: str
) -> Dict[str, Any]:
    """
    Update WhatsApp group name/subject

    Args:
        instance_name: Name of the WhatsApp instance
        group_jid: Group JID identifier
        subject: New group name/subject

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If subject is empty
    """
    try:
        if not subject or not subject.strip():
            raise ValueError("Group subject cannot be empty")

        # Build request body
        body = {
            "subject": subject.strip(),
        }

        # Make the request
        response = evolution_request(
            uri=f"/group/updateGroupSubject/{instance_name}?groupJid={group_jid}",
            method="POST",
            data=body,
        )

        logger.info(
            f"Group name updated successfully for group {group_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to update group name: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for update_group_name: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error updating group name: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
