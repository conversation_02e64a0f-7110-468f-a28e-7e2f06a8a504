from rest_framework import viewsets
from core.models import Offering
from api.mixins import SwaggerTagMixin
from django_filters import rest_framework as filters
from api.website.serializers.offering import (
    WebsiteOfferingSerializer,
    WebsiteOfferingDetailedSerializer,
)
from api.website.filters.offering import WebsiteOfferingFilter
from api.paginations import StandardResultsPagination


class WebsiteOfferingViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = Offering
    queryset = Offering.objects.filter(
        deleted=False,
        stage__in=[
            Offering.LAUNCHED_STAGE,
            Offering.ENROLLMENT_STAGE,
        ],
    ).order_by("order", "created_at")
    pagination_class = StandardResultsPagination
    lookup_field = "slug"
    filterset_class = WebsiteOfferingFilter
    filter_backends = (filters.DjangoFilterBackend,)

    swagger_tags = ["Offering"]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return WebsiteOfferingDetailedSerializer
        return WebsiteOfferingSerializer
