"""
Django signals for CRM module
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from core.models import EventScheduleEnrollment
from api.crm.tasks.event_invitations import process_new_enrollment_invitations
from django.db import transaction

logger = logging.getLogger(__name__)


@receiver(post_save, sender=EventScheduleEnrollment)
def handle_new_enrollment(sender, instance, created, **kwargs):
    """
    Handle new EventScheduleEnrollment creation to trigger automatic invitations
    when send_now criteria is met.

    This signal is triggered after an enrollment is created and will:
    1. Create an EventReminder if it doesn't exist (with duplicate phone validation)
    2. Process immediate invitations if send_now conditions are met
    3. Schedule future invitations according to event schedule configuration
    """
    if created:
        logger.info(
            f"New enrollment created: ID={instance.id}, "
            f"Event={instance.event_schedule.name}, "
            f"Phone={instance.phone_number}, "
            f"Email={instance.email}"
        )

        def schedule_invitations():
            # Process invitations asynchronously to avoid blocking the enrollment creation
            process_new_enrollment_invitations.delay(instance.id)
            logger.info(f"Scheduled invitation processing for enrollment {instance.id}")

        transaction.on_commit(schedule_invitations)
        logger.info(f"Transaction committed for enrollment {instance.id}")
