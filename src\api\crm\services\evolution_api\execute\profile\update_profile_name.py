"""
Update profile name via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def update_profile_name(instance_name: str, name: str) -> Dict[str, Any]:
    """
    Update WhatsApp profile name

    Args:
        instance_name: Name of the WhatsApp instance
        name: New profile name

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If name is empty
    """
    try:
        if not name or not name.strip():
            raise ValueError("Profile name cannot be empty")

        # Build request body
        body = {
            "name": name.strip(),
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/updateProfileName/{instance_name}", method="POST", data=body
        )

        logger.info(f"Profile name updated successfully for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to update profile name: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for update_profile_name: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error updating profile name: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
