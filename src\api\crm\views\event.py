from rest_framework import viewsets, status
from api.mixins import AuditMixin, SwaggerTagMixin
from core.models import Event
from api.crm.serializers.event import (
    CrmEventListItemSerializer,
    CrmEventRetrieveSerializer,
    CrmEventCreateSerializer,
    CrmEventUpdateSerializer,
    CrmEventFileSerializer,
)
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import (
    Is<PERSON><PERSON><PERSON><PERSON>ted,
    IsAdminUser,
    DjangoModelPermissions,
)
from rest_framework.decorators import action
from api.shared.serializers.file import FileSerializer
from api.crm.filters.event import CrmEventFilter

class CrmEventViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Event
    queryset = Event.objects.filter(deleted=False).order_by("-created_at")
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser & 
    DjangoModelPermissions]
    filterset_class = CrmEventFilter
 
    ordering_fields = ["name", "created_at", "updated_at", "price"]
    ordering = ["-created_at"]

    swagger_tags = ["Events"]

    def get_serializer_class(self):
        if self.action == "list":
            return CrmEventListItemSerializer
        elif self.action == "retrieve":
            return CrmEventRetrieveSerializer
        elif self.action == "create":
            return CrmEventCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return CrmEventUpdateSerializer
        return CrmEventListItemSerializer

    @action(
        detail=True,
        methods=["POST"],
        url_path="upload-thumbnail",
    )
    def upload_thumbnail(self, request, pk=None):
        event = self.get_object()
        serializer = CrmEventFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        file = serializer.save()

        if event.thumbnail:
            event.thumbnail.delete()

        event.thumbnail = file
        file.is_used = True
        file.save()
        event.save()
        return Response(
            status=status.HTTP_200_OK,
            data=FileSerializer(file).data,
        )

    @action(
        detail=True,
        methods=["DELETE"],
        url_path="remove-thumbnail/(?P<fid>[^/.]+)",
    )
    def remove_thumbnail(self, request, pk=None, fid=None):
        event = self.get_object()
        if event.thumbnail and str(event.thumbnail.fid) == fid:
            event.thumbnail.delete()
            event.thumbnail = None
            event.save()
            return Response(status=status.HTTP_204_NO_CONTENT)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @action(
        detail=True,
        methods=["POST"],
        url_path="upload-cover-image",
    )
    def upload_cover_image(self, request, pk=None):
        event = self.get_object()
        serializer = CrmEventFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        file = serializer.save()

        if event.cover_image:
            event.cover_image.delete()

        event.cover_image = file
        event.save()

        file.is_used = True
        file.save()
        return Response(
            status=status.HTTP_200_OK,
            data=FileSerializer(file).data,
        )

    @action(
        detail=True,
        methods=["DELETE"],
        url_path="remove-cover-image/(?P<fid>[^/.]+)",
    )
    def remove_cover_image(self, request, pk=None, fid=None):
        event = self.get_object()
        if event.cover_image and str(event.cover_image.fid) == fid:
            event.cover_image.delete()
            event.cover_image = None
            event.save()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
            )
        return Response(status=status.HTTP_404_NOT_FOUND)
