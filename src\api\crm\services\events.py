from core.appsheet import appsheet_client


def get_events():
    events = appsheet_client.find_items("Evento")
    return events


def get_current_events():
    events = appsheet_client.find_items("Evento", "En curso", target_column="Estado")
    return events


def get_event_registrations(event_id: int):
    if not event_id:
        return []
    registrations = appsheet_client.find_items(
        "Inscripción a Evento", f"{event_id}", target_column="ID Evento"
    )

    # discard if registration["Deleted"] is "Y"
    registrations = [
        registration for registration in registrations if registration["Deleted"] != "Y"
    ]
    registrations = [
        {"Nombres": registration["Nombres"], "WhatsApp": registration["WhatsApp"]}
        for registration in registrations
    ]

    return registrations
