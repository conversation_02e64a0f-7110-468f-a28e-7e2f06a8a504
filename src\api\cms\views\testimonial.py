from rest_framework import viewsets
from core.models import Testimonial
from api.cms.serializers.testimonial import (
    CmsTestimonialSerializer,
    CmsCreateTestimonialSerializer,
    CmsUpdateTestimonialSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>ica<PERSON>, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin


class CmsTestimonialViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Testimonial
    queryset = Testimonial.objects.filter(deleted=False).order_by("order")
    serializer_class = CmsTestimonialSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    swagger_tags = ["Testimonial"]

    def get_serializer_class(self):

        if self.action == "create":
            return CmsCreateTestimonialSerializer
        if self.action in ["update", "partial_update"]:
            return CmsUpdateTestimonialSerializer
        return super().get_serializer_class()
