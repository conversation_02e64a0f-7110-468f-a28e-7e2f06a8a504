from .tasks.classroom import (
    send_classroom_invitation,
    process_classroom_invitation_for_order,
)
from .tasks.event_invitations import (
    send_whatsapp_invitation,
    send_email_invitation,
    send_bulk_email_invitations,
    schedule_pending_invitations,
    process_new_enrollment_invitations,
    retry_failed_invitations,
)
from .tasks.tokechat import check_tokechat_availability, sync_whatsapp_templates

# Make tasks available at module level
__all__ = [
    "send_classroom_invitation",
    "process_classroom_invitation_for_order",
    "send_whatsapp_invitation",
    "send_email_invitation",
    "send_bulk_email_invitations",
    "schedule_pending_invitations",
    "process_new_enrollment_invitations",
    "check_tokechat_availability",
    "sync_whatsapp_templates",
    "retry_failed_invitations",
]
