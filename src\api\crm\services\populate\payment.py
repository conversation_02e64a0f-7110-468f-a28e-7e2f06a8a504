import json
from core.models import (
    PaymentMethod,
    Payment,
    File,
    Order,
)
from datetime import datetime


def parse_payment_date(date_str):
    """Parse payment date handling different formats"""
    if not date_str or date_str == "NaT":
        return None

    # Try format with timezone first
    try:
        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S%z")
    except ValueError:
        pass

    # Try format without timezone
    try:
        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return None


POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"

payments_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/12_payments.json"
voucher_files_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/17_vouchers.json"


def populate_voucher_data():
    with open(voucher_files_json_file_path) as f:
        data = json.load(f)

        vouchers = [
            File(
                fid=item["fid"],
                name=item["name"],
                bucket_name=item["bucket_name"],
                object_name=item["object_name"],
                is_private=item["is_private"],
                is_used=item["is_used"],
                width=item["width"],
                height=item["height"],
            )
            for item in data
        ]

        File.objects.bulk_create(vouchers)


def populate_payment_data():
    with open(payments_json_file_path) as f:
        data = json.load(f)

        payments = [
            Payment(
                pid=item["pid"],
                order=(
                    Order.objects.get(oid=item["order"]) if item.get("order") else None
                ),
                is_paid=item["is_paid"],
                amount=item["amount"],
                payment_date=parse_payment_date(item.get("payment_date")),
                currency=item["currency"].lower() if item.get("currency") else "",
                voucher=(
                    File.objects.get(fid=item["voucher"])
                    if item.get("voucher")
                    else None
                ),
                payment_method=(
                    PaymentMethod.objects.get(pmid=item["payment_method"])
                    if item.get("payment_method")
                    else None
                ),
                is_first_payment=item["is_first_payment"],
                observations=item.get("observations", ""),
            )
            for item in data
        ]

        Payment.objects.bulk_create(payments)
