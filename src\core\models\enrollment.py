import uuid
from django.db import models
from .base import AuditBaseModel


class Enrollment(AuditBaseModel):
    STUDENT_ENROLLMENT = "STUDENT"
    TEACHER_ENROLLMENT = "TEACHER"

    ENROLLMENT_TYPE_CHOICES = [
        (STUDENT_ENROLLMENT, "Student"),
        (TEACHER_ENROLLMENT, "Teacher"),
    ]

    eid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    user = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        related_name="enrollments",
        verbose_name="User",
    )
    offering = models.ForeignKey(
        "Offering",
        on_delete=models.CASCADE,
        related_name="offering_enrollments",
        verbose_name="Offering",
    )
    enrollment_type = models.CharField(
        max_length=24,
        choices=ENROLLMENT_TYPE_CHOICES,
        default=STUDENT_ENROLLMENT,
        verbose_name="Enrollment Type",
    )
    order = models.<PERSON><PERSON><PERSON>(
        "core.Order",
        on_delete=models.CASCADE,
        related_name="enrollments",
        verbose_name="Order",
        null=True,
        blank=True,
    )
