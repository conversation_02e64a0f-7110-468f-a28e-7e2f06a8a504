from rest_framework import serializers
from core.models import B<PERSON><PERSON><PERSON>, Blog<PERSON>ategory, Blog<PERSON>ag, Instructor
from api.shared.serializers.file import FileSerializer


class WebsiteBlogAuthorSerializer(serializers.ModelSerializer):
    profile_photo = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Instructor
        fields = [
            "iid",
            "full_name",
            "title",
            "profile_photo",
        ]
        read_only_fields = fields


class WebsiteBlogCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogCategory
        fields = [
            "bcid",
            "name",
            "slug",
        ]
        read_only_fields = fields


class WebsiteBlogTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogTag
        fields = [
            "btid",
            "name",
            "slug",
            "badge_color",
        ]
        read_only_fields = fields


class WebsiteFeaturedBlogPostSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogPost
        fields = ["bid", "title", "slug", "featured_order"]
        read_only_fields = fields


class WebsiteBlogPostListSerializer(serializers.ModelSerializer):
    thumbnail = FileSerializer(read_only=True, allow_null=True)
    categories = WebsiteBlogCategorySerializer(many=True, read_only=True)
    tags = WebsiteBlogTagSerializer(many=True, read_only=True)
    authors = WebsiteBlogAuthorSerializer(many=True, read_only=True)

    class Meta:
        model = BlogPost
        fields = [
            "bid",
            "title",
            "slug",
            "summary",
            "thumbnail",
            "reading_time",
            "published_at",
            "featured",
            "categories",
            "tags",
            "authors",
            "created_at",
            "updated_at",
        ]
        read_only_fields = fields


class WebsiteBlogPostDetailSerializer(WebsiteBlogPostListSerializer):
    cover_image = FileSerializer(read_only=True, allow_null=True)

    class Meta(WebsiteBlogPostListSerializer.Meta):
        fields = WebsiteBlogPostListSerializer.Meta.fields + [
            "content",
            "cover_image",
            "meta_title",
            "meta_description",
            "meta_keywords",
        ]
        read_only_fields = fields
