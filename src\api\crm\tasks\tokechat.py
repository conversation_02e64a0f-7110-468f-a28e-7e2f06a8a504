from api.crm.services.tokechat.flows import get_flows
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from core.models import Template
import logging

logger = logging.getLogger(__name__)


def check_tokechat_availability():
    """
    Periodic task to check TokeChat API availability and update cache
    """
    from api.crm.services.invitations.whatsapp import TokeChatAPIService

    tokechat = TokeChatAPIService()
    is_available = tokechat.is_available()

    logger.info(
        f"TokeChat availability check: {'Available' if is_available else 'Unavailable'}"
    )

    # The availability is already cached by the TokeChat.is_available() method
    return {
        "tokechat_available": is_available,
        "checked_at": timezone.now().isoformat(),
    }


@shared_task
def sync_whatsapp_templates():
    """
    Periodic task to synchronize TokeChat flows with Template database
    This task runs every 5 minutes to keep flows updated
    """
    try:
        # Get flows from TokeChat API
        flows = get_flows()

        if not flows:
            logger.warning("No flows retrieved from TokeChat API")
            return "No flows retrieved"

        created_count = 0
        updated_count = 0

        # Delete templates that no longer exist in TokeChat
        tokechat_flow_ids = set()
        for flow in flows:
            flow_id = str(flow.get("id"))
            if flow_id:
                tokechat_flow_ids.add(flow_id)

        templates_to_delete = (
            Template.objects.filter(
                ext_reference__isnull=False,
            )
            .exclude(ext_reference__exact="")
            .exclude(ext_reference__in=tokechat_flow_ids)
        )

        deleted_count = templates_to_delete.count()
        for template in templates_to_delete:
            template_name = template.name
            template_id = template.ext_reference
            template.delete()
            logger.info(
                f"Deleted template for removed TokeChat flow: {template_name} (ID: {template_id})"
            )

        # Add or update templates
        with transaction.atomic():
            for flow in flows:
                flow_id = str(flow.get("id"))
                flow_name = flow.get("name", "")

                if not flow_id:
                    continue

                # Check if template with this ext_reference already exists
                template, created = Template.objects.get_or_create(
                    ext_reference=flow_id,
                    defaults={
                        "name": flow_name,
                        "body_text": f"TokeChat Flow: {flow_name}",
                        "status": Template.APPROVED,  # Set as approved for use
                    },
                )

                if created:
                    created_count += 1
                    logger.info(
                        f"Created new template for TokeChat flow: {flow_name} (ID: {flow_id})"
                    )
                else:
                    # Update existing template if name changed
                    if template.name != flow_name:
                        template.name = flow_name
                        template.body_text = f"TokeChat Flow: {flow_name}"
                        template.save()
                        updated_count += 1
                        logger.info(
                            f"Updated template for TokeChat flow: {flow_name} (ID: {flow_id})"
                        )

        result = f"Synced TokeChat flows: {created_count} created, {updated_count} updated, {deleted_count} deleted"
        logger.info(result)
        return result

    except Exception as e:
        error_msg = f"Error syncing TokeChat flows: {str(e)}"
        logger.error(error_msg)
        return error_msg
