from rest_framework import serializers
from core.models import Partnership, EducationalInstitution


class CrmPartnershipInstitutionSerializer(serializers.ModelSerializer):
    class Meta:
        model = EducationalInstitution
        fields = [
            "eiid",
            "name",
        ]


class CrmBasePartnershipSerializer(serializers.ModelSerializer):
    """Base serializer for Partnership, used for listing."""

    key = serializers.CharField(source="pid", read_only=True)
    institution = CrmPartnershipInstitutionSerializer()

    class Meta:
        model = Partnership
        fields = [
            "key",
            "pid",
            "name",
            "description",
            "institution",
            "created_at",
            "updated_at",
        ]


class CrmRetrievePartnershipSerializer(CrmBasePartnershipSerializer):
    """Serializer for retrieving a single Partnership."""

    class Meta(CrmBasePartnershipSerializer.Meta):
        fields = CrmBasePartnershipSerializer.Meta.fields


class CrmCreatePartnershipSerializer(serializers.ModelSerializer):
    """Serializer for creating a new Partnership."""

    description = serializers.Char<PERSON>ield(
        allow_blank=True,
        required=False,
        help_text="Partnership description",
    )

    def validate_institution(self, value):
        if not EducationalInstitution.objects.filter(pk=value.pk).exists():
            raise serializers.ValidationError("La institución no existe.")
        return value

    def validate(self, data):
        """Validate that the combination of institution and description is unique."""
        institution = data.get("institution")
        description = data.get("description")

        if institution and description:
            if Partnership.objects.filter(
                institution=institution, description=description
            ).exists():
                raise serializers.ValidationError(
                    "Ya existe una asociación con esta institución y descripción."
                )

        return data

    class Meta:
        model = Partnership
        fields = [
            "name",
            "description",
            "institution",
        ]


class CrmUpdatePartnershipSerializer(serializers.ModelSerializer):
    """Serializer for updating an existing Partnership."""

    def validate(self, data):
        """Validate that the combination of institution and description is unique."""
        institution = data.get("institution", self.instance.institution)
        description = data.get("description", self.instance.description)

        if institution and description:
            if (
                Partnership.objects.filter(
                    institution=institution, description=description
                )
                .exclude(pk=self.instance.pk)
                .exists()
            ):
                raise serializers.ValidationError(
                    "Ya existe una asociación con esta institución y descripción."
                )

        return data

    def validate_name(self, value):
        """Validate that the name is not too similar to existing ones for the same institution."""
        if value == self.instance.name:
            return value

        institution = self.initial_data.get("institution", self.instance.institution_id)

        if institution:
            import difflib

            existing_partnerships = Partnership.objects.filter(
                institution=institution
            ).exclude(pk=self.instance.pk)
            existing_names = [p.name for p in existing_partnerships]

            value_lower = value.lower()
            for name in existing_names:
                if name.lower() == value_lower:
                    raise serializers.ValidationError(
                        "Ya existe una asociación con un nombre similar para esta institución."
                    )
                similarity = difflib.SequenceMatcher(
                    None, name.lower(), value_lower
                ).ratio()
                if similarity > 0.85:
                    raise serializers.ValidationError(
                        "Ya existe una asociación con un nombre muy similar para esta institución."
                    )

        return value

    class Meta:
        model = Partnership
        fields = [
            "name",
            "description",
            "institution",
        ]
