from rest_framework.views import APIView
from rest_framework.response import Response
from api.crm.services.sales import get_sales_by_status

class GetSoldSalesView(APIView):
    def get(self, request):
        offering_id = request.query_params.get('offering_id') or None
        page_size = request.query_params.get('page_size') or None
        if page_size:
            page_size = int(page_size)
        page = request.query_params.get('page') or None
        if page:
            page = int(page)

        sales_by_status = get_sales_by_status("Vendido", offering_id, page_size, page)
        return Response(sales_by_status)

class GetProspectSalesView(APIView):
    def get(self, request):
        offering_id = request.query_params.get('offering_id') or None
        page_size = request.query_params.get('page_size') or None
        if page_size:
            page_size = int(page_size)
        page = request.query_params.get('page') or None
        if page:
            page = int(page)

        sales_by_status = get_sales_by_status("Prospecto", offering_id, page_size, page)
        return Response(sales_by_status)

class GetInterestedSalesView(APIView):
    def get(self, request):
        offering_id = request.query_params.get('offering_id') or None
        page_size = request.query_params.get('page_size') or None
        if page_size:
            page_size = int(page_size)
        page = request.query_params.get('page') or None
        if page:
            page = int(page)

        sales_by_status = get_sales_by_status("Interesado", offering_id, page_size, page)
        return Response(sales_by_status)

class GetPendingPaymentSalesView(APIView):
    def get(self, request):
        offering_id = request.query_params.get('offering_id') or None
        page_size = request.query_params.get('page_size') or None
        if page_size:
            page_size = int(page_size)
        page = request.query_params.get('page') or None
        if page:
            page = int(page)

        sales_by_status = get_sales_by_status("Por Pagar", offering_id, page_size, page)
        return Response(sales_by_status)