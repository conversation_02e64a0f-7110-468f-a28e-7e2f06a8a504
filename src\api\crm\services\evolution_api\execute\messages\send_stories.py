"""
Send stories via Evolution API
"""

import logging
from typing import Dict, Any, List, Optional, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

StoryType = Literal["text", "image", "video", "audio"]


def send_stories(
    instance_name: str,
    content: str,
    story_type: StoryType,
    caption: str = "",
    all_contacts: bool = False,
    background_color: str = "#000000",
    font: int = 1,
    contacts: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Send stories via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        content: Story content (text or media URL/base64)
        story_type: Type of story (text, image, video, audio)
        caption: Caption for media stories
        all_contacts: Whether to send to all contacts
        background_color: Background color for text stories (hex format)
        font: Font style for text stories (1-5)
        contacts: List of specific contacts to send to (if not all_contacts)

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If story parameters are invalid
    """
    try:
        # Validate content format for non-text stories
        if story_type != "text" and not (
            content.startswith("http") or content.startswith("data:")
        ):
            raise ValueError(
                "Content must be a valid URL or base64 for non-text stories"
            )

        # Build request body
        body = {
            "type": story_type,
            "content": content,
            "backgroundColor": background_color,
            "font": font,
            "allContacts": all_contacts,
        }

        # Add caption for media stories
        if story_type != "text" and caption:
            body["caption"] = caption

        # Add specific contacts if not sending to all
        if not all_contacts and contacts:
            # Ensure contacts have WhatsApp format
            formatted_contacts = []
            for contact in contacts:
                contact = contact.strip()
                if "@s.whatsapp.net" not in contact:
                    contact = f"{contact}@s.whatsapp.net"
                formatted_contacts.append(contact)
            body["contacts"] = formatted_contacts

        # Make the request
        response = evolution_request(
            uri=f"/message/sendStories/{instance_name}", method="POST", data=body
        )

        logger.info(f"Story sent successfully via instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send story: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid story parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending story: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
