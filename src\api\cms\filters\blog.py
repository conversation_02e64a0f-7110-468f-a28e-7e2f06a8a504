from django_filters import rest_framework as filters
from core.models import BlogPost
from django.db.models import Q


class CmsBlogPostFilter(filters.FilterSet):
    search = filters.CharFilter(method="filter_search", label="Search by title")

    status = filters.MultipleChoiceFilter(
        choices=BlogPost.STATUS_CHOICES,
        field_name="status",
    )

    featured = filters.BooleanFilter(field_name="featured")

    created_at = filters.DateFromToRangeFilter()
    published_at = filters.DateFromToRangeFilter()

    author = filters.UUIDFilter(field_name="authors__iid")
    category = filters.UUIDFilter(field_name="categories__cid")
    tag = filters.UUIDFilter(field_name="tags__tid")

    def filter_search(self, queryset, _, value):
        return queryset.filter(Q(title__icontains=value))

    class Meta:
        model = BlogPost
        fields = [
            "status",
            "featured",
            "created_at",
            "published_at",
            "author",
            "category",
            "tag",
        ]
