# Generated by Django 5.0.6 on 2025-08-15 16:37

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("core", "0019_eventschedule_emails_reminder_auto"),
    ]

    operations = [
        migrations.CreateModel(
            name="TemplateType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "ttid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name in Spanish to display in the frontend (e.g., 'Recordatorio de eventos')",
                        max_length=255,
                        verbose_name="Nombre",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="If this template type is available to use",
                        verbose_name="Activo",
                    ),
                ),
                (
                    "content_type",
                    models.OneToOneField(
                        help_text="Django model associated with this template type",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                        verbose_name="Modelo Asociado",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Message Template Type",
                "verbose_name_plural": "Message Template Types",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="template",
            name="type",
            field=models.ForeignKey(
                blank=True,
                help_text="Template type that determines available variables",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="core.templatetype",
                verbose_name="Tipo de Plantilla",
            ),
        ),
        migrations.CreateModel(
            name="TemplateVariable",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "tvid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name used in templates (e.g., 'Nombre del contacto', 'Link del evento')",
                        max_length=255,
                        verbose_name="Variable Name",
                    ),
                ),
                (
                    "path",
                    models.CharField(
                        help_text="Django model path to extract the value (e.g., 'enrollment__user__first_name')",
                        max_length=255,
                        verbose_name="Model Path",
                    ),
                ),
                (
                    "example",
                    models.CharField(
                        blank=True,
                        help_text="Example value to show in template preview",
                        max_length=255,
                        null=True,
                        verbose_name="Example Value",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of what this variable represents",
                        null=True,
                        verbose_name="Description",
                    ),
                ),
                (
                    "data_type",
                    models.CharField(
                        choices=[
                            ("string", "String"),
                            ("datetime", "DateTime"),
                            ("date", "Date"),
                            ("integer", "Integer"),
                            ("boolean", "Boolean"),
                            ("array", "Array"),
                            ("decimal", "Decimal"),
                        ],
                        default="string",
                        help_text="Type of data this variable represents",
                        max_length=20,
                        verbose_name="Data Type",
                    ),
                ),
                (
                    "data_format",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("date_long", "Fecha larga (Viernes, 11 de julio de 2025)"),
                            ("date_short", "Fecha corta (11/07/2025)"),
                            (
                                "date_weekday_medium",
                                "Fecha media (Viernes, 11 de julio)",
                            ),
                            ("time_12h", "Hora 12h (10:30 AM)"),
                            ("time_24h", "Hora 24h (22:30)"),
                            (
                                "datetime_full",
                                "Fecha y hora completa (Viernes, 11 de julio de 2025 a las 13:00)",
                            ),
                            ("datetime_short", "Fecha y hora corta (11/07/2025 13:00)"),
                            ("currency", "Moneda"),
                        ],
                        help_text="How to format the value when displaying",
                        max_length=30,
                        null=True,
                        verbose_name="Format",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "template_type",
                    models.ForeignKey(
                        help_text="Template type to which this variable belongs",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.templatetype",
                        verbose_name="Tipo de Plantilla",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Message Template Variable",
                "verbose_name_plural": "Message Template Variables",
            },
        ),
        migrations.AddIndex(
            model_name="templatetype",
            index=models.Index(
                fields=["is_active"], name="core_templa_is_acti_0e3f93_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="templatevariable",
            index=models.Index(
                fields=["template_type"], name="core_templa_templat_a794f1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="templatevariable",
            index=models.Index(fields=["name"], name="core_templa_name_f0ab84_idx"),
        ),
        migrations.AddIndex(
            model_name="templatevariable",
            index=models.Index(
                fields=["data_type"], name="core_templa_data_ty_9d7229_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="templatevariable",
            unique_together={("name", "template_type")},
        ),
    ]
