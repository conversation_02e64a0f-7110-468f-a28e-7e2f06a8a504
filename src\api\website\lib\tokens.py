import datetime
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils import timezone


class TenMinuteResetTokenGenerator(PasswordResetTokenGenerator):
    def _make_hash_value(self, user, timestamp):
        login_timestamp = (
            ""
            if user.last_login is None
            else user.last_login.replace(microsecond=0, tzinfo=None)
        )
        email_field = user.get_email_field_name()
        email = getattr(user, email_field, "") or ""
        return f"{user.pk}{user.password}{login_timestamp}{timestamp}{email}"

    def _now(self):
        return timezone.now()

    def _num_seconds(self, dt):
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=datetime.timezone.utc)
        base = datetime.datetime(2001, 1, 1, tzinfo=datetime.timezone.utc)
        return int((dt - base).total_seconds())

    def _make_timestamp(self):
        return self._num_seconds(self._now())

    def check_token(self, user, token):

        if not super().check_token(user, token):
            return False

        try:
            ts_b36 = token.split("-")[0]
            ts_int = int(ts_b36, 36)
        except Exception:
            return False

        now_ts = self._make_timestamp()

        if now_ts - ts_int > 600:
            return False

        return True
