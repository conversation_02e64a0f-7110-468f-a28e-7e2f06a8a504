"""
Payment Dashboard Serializers for CRM
Provides serialization for payment dashboard analytics data
"""

from rest_framework import serializers
from core.models import Payment, PaymentMethod, User, Offering
from api.crm.serializers.offering import CrmOfferingSerializer


class CrmDashboardPaymentOwnerSerializer(serializers.ModelSerializer):
    """Serializer for payment order owner information"""

    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "uid",
            "first_name",
            "last_name",
            "full_name",
            "email",
            "phone_number",
        ]

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "No name"


class CrmDashboardPaymentMethodSerializer(serializers.ModelSerializer):
    """Serializer for payment method information"""

    class Meta:
        model = PaymentMethod
        fields = ["pmid", "name", "description"]


class CrmDashboardPaymentFilterOptionsSerializer(serializers.Serializer):
    """Serializer for filter options"""

    payment_methods = CrmDashboardPaymentMethodSerializer(many=True, read_only=True)
    offerings = CrmOfferingSerializer(many=True, read_only=True)
    currencies = serializers.ListField(child=serializers.DictField(), read_only=True)
    status_options = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )
    type_date_options = serializers.ListField(
        child=serializers.DictField(), read_only=True
    )


class CrmDashboardPaymentSerializer(serializers.Serializer):
    """Main serializer for payment dashboard data"""

    # Summary statistics
    total_registered_amount = serializers.DictField(read_only=True)
    total_debt_paid_amount = serializers.DictField(read_only=True)
    total_pending_debt_amount = serializers.DictField(read_only=True)
    total_paid_first_payment_amount = serializers.DictField(read_only=True)
    total_pending_first_payment_amount = serializers.DictField(read_only=True)

    # Performance metrics
    debt_collection_rate = serializers.DictField(read_only=True)
    average_payment_days = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    payment_conversion_rate = serializers.DictField(read_only=True)
    recovery_rate = serializers.DictField(read_only=True)

    # Metadata
    currency_exchange_rate = serializers.DecimalField(
        max_digits=10, decimal_places=4, read_only=True
    )
    filters_applied = serializers.DictField(read_only=True)


class CrmDashboardPaymentHistoricalSerializer(serializers.Serializer):
    """Serializer for historical payment data"""

    period_type = serializers.CharField(read_only=True)
    total_periods = serializers.IntegerField(read_only=True)
    currency_exchange_rate = serializers.DecimalField(
        max_digits=10, decimal_places=4, read_only=True
    )
    filters_applied = serializers.DictField(read_only=True)
    data = serializers.ListField(child=serializers.DictField(), read_only=True)


class CrmDashboardPaymentMethodsHistoricalSerializer(serializers.Serializer):
    """Serializer for historical payment methods data"""

    period_type = serializers.CharField(read_only=True)
    total_periods = serializers.IntegerField(read_only=True)
    currency_exchange_rate = serializers.DecimalField(
        max_digits=10, decimal_places=4, read_only=True
    )
    filters_applied = serializers.DictField(read_only=True)
    data = serializers.ListField(child=serializers.DictField(), read_only=True)


class CrmDashboardPaymentDistributionSerializer(serializers.Serializer):
    """Serializer for payment distribution data (methods or currency)"""

    total_payments = serializers.IntegerField(read_only=True)
    filters_applied = serializers.DictField(read_only=True)
    data = serializers.ListField(child=serializers.DictField(), read_only=True)


class CrmDashboardLastPaymentsSerializer(serializers.Serializer):
    """Serializer for last payments data"""

    total_returned = serializers.IntegerField(read_only=True)
    filters_applied = serializers.DictField(read_only=True)
    data = serializers.ListField(child=serializers.DictField(), read_only=True)
