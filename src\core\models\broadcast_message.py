import uuid
from django.db import models
from core.models.base import AuditBaseModel


class BroadcastMessage(AuditBaseModel):
    DRAFT = "DRAFT"
    PENDING = "PENDING"
    SENT = "SENT"
    FAILED = "FAILED"

    STATUS_CHOICES = [
        (DRAFT, "Draft"),
        (PENDING, "Pending"),
        (SENT, "Sent"),
        (FAILED, "Failed"),
    ]

    PROSPECT = "PROSPECT"
    LEAD = "LEAD"
    PENDING_PAYMENT = "PENDING_PAYMENT"
    SOLD = "SOLD"

    PIPELINE_STATES = [
        (PROSPECT, "Prospect"),
        (LEAD, "Lead"),
        (PENDING_PAYMENT, "Pending Payment"),
        (SOLD, "Sold"),
    ]

    mid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    offering_id = models.IntegerField(
        verbose_name="Offering ID",
        blank=True,
        null=True,
    )
    offering_name = models.Char<PERSON><PERSON>(
        max_length=100,
        verbose_name="Offering Name",
        blank=True,
        null=True,
    )
    pipeline_state = models.CharField(
        max_length=15,
        choices=PIPELINE_STATES,
        default=PROSPECT,
        verbose_name="Pipeline State",
    )
    template_id = models.ForeignKey(
        "Template",
        on_delete=models.CASCADE,
        related_name="broadcast_messages",
        verbose_name="Message Template",
    )
    variables = models.JSONField(
        blank=True,
        null=True,
        verbose_name="Template Variables",
    )
    send_at = models.DateTimeField(verbose_name="Scheduled Send Time")
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=DRAFT,
        verbose_name="Status",
    )

    def __str__(self):
        return f"{self.pipeline_state} Broadcast Message"

    class Meta:
        db_table = "broadcast_message"
        verbose_name = "Broadcast Message"
        verbose_name_plural = "Broadcast Messages"
