"""
Logout WhatsApp instance via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def logout_instance(instance_name: str) -> Dict[str, Any]:
    """
    Logout a WhatsApp instance
    
    Args:
        instance_name: Name of the instance to logout
        
    Returns:
        Dict containing the API response
        
    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/instance/logout/{instance_name}",
            method="DELETE"
        )
        
        logger.info(f"Instance '{instance_name}' logged out successfully")
        return response
        
    except EvolutionAPIError as e:
        logger.error(f"Failed to logout instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error logging out instance '{instance_name}': {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
