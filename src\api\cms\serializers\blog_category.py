from rest_framework import serializers
from core.models import BlogCategory
from django.utils.text import slugify


class CmsBlogCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogCategory
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]


class CmsCreateBlogCategorySerializer(CmsBlogCategorySerializer):
    slug = serializers.SlugField(write_only=True, required=False)

    class Meta:
        model = BlogCategory
        fields = ["name", "description", "slug"]

    def create(self, validated_data):
        # Generate slug from name
        name = validated_data.get("name")
        if not validated_data.get("slug"):
            slug = slugify(name)

            # Check if slug exists and make it unique if needed
            if BlogCategory.objects.filter(slug=slug).exists():
                count = 1
                while BlogCategory.objects.filter(slug=f"{slug}-{count}").exists():
                    count += 1
                slug = f"{slug}-{count}"

            validated_data["slug"] = slug
        return super().create(validated_data)


class CmsUpdateBlogCategorySerializer(CmsBlogCategorySerializer):
    slug = serializers.SlugField(write_only=True, required=False)

    class Meta:
        model = BlogCategory
        fields = ["name", "description", "parent", "slug"]

    def update(self, instance, validated_data):
        # If name is updated but slug is not, update the slug
        if "name" in validated_data and "slug" not in validated_data:
            name = validated_data.get("name")
            slug = slugify(name)
            
            # Check if slug exists and make it unique if needed
            if BlogCategory.objects.filter(slug=slug).exclude(bcid=instance.bcid).exists():
                count = 1
                while BlogCategory.objects.filter(slug=f"{slug}-{count}").exclude(bcid=instance.bcid).exists():
                    count += 1
                slug = f"{slug}-{count}"
            
            validated_data["slug"] = slug
        
        return super().update(instance, validated_data)
