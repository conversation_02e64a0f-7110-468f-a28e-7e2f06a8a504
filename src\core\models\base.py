from django.db import models


class AuditBaseModel(models.Model):
    created_at = models.DateTimeField(
        auto_now_add=True,
        editable=False,
        verbose_name="Created At",
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        editable=False,
        verbose_name="Last Updated",
    )
    created_by = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        editable=False,
        related_name="%(class)s_created_by",
        verbose_name="Created By",
    )
    updated_by = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        editable=False,
        related_name="%(class)s_updated_by",
        verbose_name="Updated By",
    )

    deleted = models.BooleanField(
        default=False,
        verbose_name="Deleted",
    )
    deleted_at = models.DateTimeField(
        blank=True,
        null=True,
        editable=False,
        verbose_name="Deleted At",
    )
    deleted_by = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        editable=False,
        related_name="%(class)s_deleted_by",
        verbose_name="Deleted By",
    )

    class Meta:
        abstract = True


class OrderBaseModel(models.Model):
    order = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="Order",
        help_text="Order in the Website",
    )

    class Meta:
        abstract = True
