"""
Update group description via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def update_group_description(
    instance_name: str, group_jid: str, description: str
) -> Dict[str, Any]:
    """
    Update WhatsApp group description

    Args:
        instance_name: Name of the WhatsApp instance
        group_jid: Group JID identifier
        description: New group description

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "description": description,
        }

        # Make the request
        response = evolution_request(
            uri=f"/group/updateGroupDescription/{instance_name}?groupJid={group_jid}",
            method="POST",
            data=body,
        )

        logger.info(
            f"Group description updated successfully for group {group_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to update group description: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating group description: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
