from django.core.management.base import BaseCommand, CommandError
from api.crm.services.populate.educational_institution import (
    populate_educational_institution_data,
    populate_partnerships_data,
)
from api.crm.services.populate.contact import (
    populate_contact_data,
    populate_majors_data,
    populate_terms_data,
)
from api.crm.services.populate.offering import populate_offering_data
from api.crm.services.populate.order import (
    populate_benefit_data,
    populate_payment_method_data,
    populate_lead_source_data,
    populate_order_data,
    populate_order_item_data,
)
from api.crm.services.populate.payment import (
    populate_voucher_data,
    populate_payment_data,
)
from api.crm.services.populate.activity import populate_activity_data
from api.crm.services.populate.event import (
    populate_event_data,
    populate_event_schedule_data,
    populate_event_schedule_enrollment_data,
)


class Command(BaseCommand):
    help = "Populate the database with the migrations and initial CRM data."

    def handle(self, *args, **options):
        try:
            # First populate the educational institution data
            populate_educational_institution_data()
            self.stdout.write(
                self.style.SUCCESS(
                    "1. Educational institution data populated successfully."
                )
            )

            # Secondly, populate the majors data
            populate_majors_data()
            self.stdout.write(
                self.style.SUCCESS("2. Majors data populated successfully.")
            )

            # Thirdly, populate the terms data
            populate_terms_data()
            self.stdout.write(
                self.style.SUCCESS("3. Terms data populated successfully.")
            )

            # Then, populate the contact data
            populate_contact_data()
            self.stdout.write(
                self.style.SUCCESS("4. Contact data populated successfully.")
            )

            # Populate the partnerships data
            populate_partnerships_data()
            self.stdout.write(
                self.style.SUCCESS("5. Partnerships data populated successfully.")
            )

            # Now, populate the offering data
            populate_offering_data()
            self.stdout.write(
                self.style.SUCCESS("6. Offering data populated successfully.")
            )

            # Benefits data
            populate_benefit_data()
            self.stdout.write(
                self.style.SUCCESS("7. Benefits data populated successfully.")
            )

            # Payment methods data
            populate_payment_method_data()
            self.stdout.write(
                self.style.SUCCESS("8. Payment methods data populated successfully.")
            )

            # Lead sources data
            populate_lead_source_data()
            self.stdout.write(
                self.style.SUCCESS("9. Lead sources data populated successfully.")
            )

            # Order data
            populate_order_data()
            self.stdout.write(
                self.style.SUCCESS("10. Order data populated successfully.")
            )

            # Order items data
            populate_order_item_data()
            self.stdout.write(
                self.style.SUCCESS("11. Order items data populated successfully.")
            )

            # Voucher data
            populate_voucher_data()
            self.stdout.write(
                self.style.SUCCESS("12. Voucher data populated successfully.")
            )

            # Payment data
            populate_payment_data()
            self.stdout.write(
                self.style.SUCCESS("13. Payment data populated successfully.")
            )

            # Activity data
            populate_activity_data()
            self.stdout.write(
                self.style.SUCCESS("14. Activity data populated successfully.")
            )

            # Event data
            populate_event_data()
            self.stdout.write(
                self.style.SUCCESS("15. Event data populated successfully.")
            )

            # Event schedule data
            populate_event_schedule_data()
            self.stdout.write(
                self.style.SUCCESS("16. Event schedule data populated successfully.")
            )

            # Event schedule enrollment data
            populate_event_schedule_enrollment_data()
            self.stdout.write(
                self.style.SUCCESS(
                    "17. Event schedule enrollment data populated successfully."
                )
            )

        except Exception as e:
            raise CommandError(f"Error populating CRM data: {str(e)}")
