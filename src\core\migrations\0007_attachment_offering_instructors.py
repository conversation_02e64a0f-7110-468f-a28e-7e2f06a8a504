# Generated by Django 5.0.6 on 2025-02-12 19:44

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0006_rename_frecuency_offering_frequency_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Attachment",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "aid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "instructor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="offerings",
                        to="core.instructor",
                        verbose_name="Instructor of the Attachment",
                    ),
                ),
                (
                    "offering",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="offering_attachments",
                        to="core.offering",
                        verbose_name="Offering of the Attachment",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="offering",
            name="instructors",
            field=models.ManyToManyField(
                related_name="attached_offerings",
                through="core.Attachment",
                to="core.instructor",
                verbose_name="Attached Instructors",
            ),
        ),
    ]
