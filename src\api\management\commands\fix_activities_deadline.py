from django.core.management.base import BaseCommand, CommandError

from api.crm.services.populate.activity import add_deadline_date


class Command(BaseCommand):
    help = "Fixes the deadline dates of existing activities in the CRM."

    def handle(self, *args, **options):
        try:
            add_deadline_date()
            self.stdout.write(
                self.style.SUCCESS("Activity deadlines updated successfully.")
            )
        except Exception as e:
            raise CommandError(f"Error updating activity deadlines: {e}")
