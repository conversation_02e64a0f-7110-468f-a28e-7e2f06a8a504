import logging
from typing import List, Tu<PERSON>, Dict, Any, Callable

logger = logging.getLogger(__name__)


class SeedRegistry:
    """
    Registry for managing and executing seeds in order
    """

    def __init__(self):
        self._seeds: List[Dict[str, Any]] = []

    def register(
        self,
        name: str,
        function: Callable[[], Tuple[int, int]],
        order: int,
        description: str = "",
    ):
        """
        Register a seed function

        Args:
            name: Unique name for the seed
            function: Function that returns (created_count, updated_count)
            description: Description of what the seed does
            order: Execution order (lower numbers execute first)
        """
        self._seeds.append(
            {
                "name": name,
                "function": function,
                "description": description,
                "order": order,
            }
        )
        # Keep seeds sorted by order
        self._seeds.sort(key=lambda x: x["order"])

    def execute_all(self) -> Dict[str, Any]:
        """
        Execute all registered seeds in order

        Returns:
            Dictionary with execution results
        """
        logger.info("Starting execution of all registered seeds...")

        total_created = 0
        total_updated = 0
        executed_seeds = []
        failed_seeds = []

        for seed in self._seeds:
            seed_name = seed["name"]
            seed_function = seed["function"]
            seed_description = seed["description"]
            seed_order = seed["order"]

            logger.info(f"{seed_order}. Executing seed: {seed_name}")

            try:
                created_count, updated_count = seed_function()

                total_created += created_count
                total_updated += updated_count

                executed_seeds.append(
                    {
                        "order": seed_order,
                        "name": seed_name,
                        "description": seed_description,
                        "created": created_count,
                        "updated": updated_count,
                        "success": True,
                    }
                )

                logger.info(
                    f"✓ {seed_name} completed - Created: {created_count}, Updated: {updated_count}"
                )

            except Exception as e:
                error_message = str(e)
                logger.error(f"✗ {seed_name} failed: {error_message}")

                failed_seeds.append(
                    {
                        "order": seed_order,
                        "name": seed_name,
                        "description": seed_description,
                        "error": error_message,
                        "success": False,
                    }
                )
                # Detener ejecución si los seeds fallan
                break

        result = {
            "total_created": total_created,
            "total_updated": total_updated,
            "executed_seeds": executed_seeds,
            "failed_seeds": failed_seeds,
            "success": len(failed_seeds) == 0,
        }

        if result["success"]:
            logger.info(
                f"All seeds executed successfully! "
                f"Total created: {total_created}, Total updated: {total_updated}"
            )
        else:
            logger.error(
                f"Some seeds failed. Successful: {len(executed_seeds)}, "
                f"Failed: {len(failed_seeds)}"
            )

        return result

    def get_registered_seeds(self) -> List[Dict[str, Any]]:
        """
        Get list of all registered seeds

        Returns:
            List of seed information
        """
        return [
            {
                "name": seed["name"],
                "description": seed["description"],
                "order": seed["order"],
            }
            for seed in self._seeds
        ]


# Global seed registry instance
seed_registry = SeedRegistry()


def register_seed(name: str, description: str = "", order: int = 100):
    """
    Decorator to register a seed function

    Args:
        name: Unique name for the seed
        description: Description of what the seed does
        order: Execution order (lower numbers execute first)
    """

    def decorator(func: Callable[[], Tuple[int, int]]):
        seed_registry.register(name, func, description, order)
        return func

    return decorator


def execute_all_seeds() -> Dict[str, Any]:
    """
    Execute all registered seeds

    Returns:
        Dictionary with execution results
    """
    return seed_registry.execute_all()


def get_registered_seeds() -> List[Dict[str, Any]]:
    """
    Get list of all registered seeds

    Returns:
        List of seed information
    """
    return seed_registry.get_registered_seeds()
