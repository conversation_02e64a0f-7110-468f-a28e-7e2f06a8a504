from rest_framework import serializers
from core.models import Term


class CrmTermSerializer(serializers.ModelSerializer):
    key = serializers.CharField(source="tid", read_only=True)

    def validate_name(self, value):
        import difflib

        # Si es update, excluir el propio objeto
        qs = Term.objects.all()
        if self.instance is not None:
            qs = qs.exclude(pk=self.instance.pk)
        existing_names = qs.values_list("name", flat=True)
        value_lower = value.lower()
        for name in existing_names:
            if name.lower() == value_lower:
                raise serializers.ValidationError(
                    "Ya existe un ciclo con un nombre similar (coincidencia insensible a mayúsculas/minúsculas)."
                )
            similarity = difflib.SequenceMatcher(
                None, name.lower(), value_lower
            ).ratio()
            if similarity > 0.85:
                raise serializers.ValidationError(
                    "Ya existe un ciclo con un nombre muy similar."
                )
        return value

    class Meta:
        model = Term
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]
