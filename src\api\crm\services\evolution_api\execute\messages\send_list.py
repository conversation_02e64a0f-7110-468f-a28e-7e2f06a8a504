"""
Send list message via Evolution API
"""

import logging
from typing import Dict, Any, List, Optional, TypedDict
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


class ListRow(TypedDict, total=False):
    """List row structure"""

    title: str
    description: Optional[str]
    row_id: Optional[str]


class ListSection(TypedDict):
    """List section structure"""

    title: str
    rows: List[ListRow]


def send_list(
    instance_name: str,
    remote_jid: str,
    title: str,
    description: str,
    button_text: str,
    sections: List[ListSection],
    footer: str = "",
    delay: Optional[int] = None,
    quoted_message_id: Optional[str] = None,
    mentions_everyone: Optional[bool] = None,
    mentioned_numbers: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Send list message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        title: Title of the list message
        description: Description text
        button_text: Text for the list button
        sections: List of sections with rows
        footer: Footer text (optional)
        delay: Delay in milliseconds before sending
        quoted_message_id: ID of message to quote/reply to
        mentions_everyone: Whether to mention everyone in group
        mentioned_numbers: List of phone numbers to mention

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If sections configuration is invalid
    """
    try:
        # Validate sections
        if not sections or not isinstance(sections, list):
            raise ValueError("At least one section with options is required")

        # Build sections list
        sections_list = []
        for section in sections:
            if not section.get("rows"):
                continue

            rows_list = []
            for row in section["rows"]:
                row_data = {
                    "title": row["title"],
                }
                if row.get("description"):
                    row_data["description"] = row["description"]
                if row.get("row_id"):
                    row_data["rowId"] = row["row_id"]

                rows_list.append(row_data)

            if rows_list:  # Only add section if it has rows
                sections_list.append({"title": section["title"], "rows": rows_list})

        if not sections_list:
            raise ValueError("At least one section with valid rows is required")

        # Build request body
        body = {
            "number": remote_jid,
            "listMessage": {
                "title": title,
                "description": description,
                "buttonText": button_text,
                "footer": footer,
                "sections": sections_list,
            },
        }

        # Add optional delay
        if delay is not None:
            body["delay"] = delay

        # Add quoted message
        if quoted_message_id:
            body["quoted"] = {
                "key": {
                    "id": quoted_message_id,
                }
            }

        # Handle mentions
        if mentions_everyone:
            body["mentionsEveryOne"] = True
        elif mentioned_numbers:
            # Ensure numbers have WhatsApp format
            formatted_numbers = []
            for num in mentioned_numbers:
                num = num.strip()
                if "@s.whatsapp.net" not in num:
                    num = f"{num}@s.whatsapp.net"
                formatted_numbers.append(num)
            body["mentioned"] = formatted_numbers

        # Make the request
        response = evolution_request(
            uri=f"/message/sendList/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"List message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send list message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid list parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending list message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
