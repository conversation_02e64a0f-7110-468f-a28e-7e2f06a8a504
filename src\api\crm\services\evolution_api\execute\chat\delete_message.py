"""
Delete message via Evolution API
"""

import logging
from typing import Dict, Any, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def delete_message(
    instance_name: str,
    remote_jid: str,
    message_id: str,
    from_me: bool,
    participant: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Delete a message

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID where the message is
        message_id: ID of the message to delete
        from_me: Whether the message was sent by me
        participant: Participant who sent the message (required if from_me is False)

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If participant is required but not provided
    """
    try:
        # Format remote JID
        if "@g.us" not in remote_jid and "@s.whatsapp.net" not in remote_jid:
            formatted_remote_jid = f"{remote_jid}@s.whatsapp.net"
        else:
            formatted_remote_jid = remote_jid

        # Build request body
        body = {
            "id": message_id,
            "fromMe": from_me,
            "remoteJid": formatted_remote_jid,
        }

        # Add participant if message is not from me
        if not from_me:
            if not participant:
                raise ValueError(
                    "Participant is required when deleting messages not sent by me"
                )

            if "@s.whatsapp.net" not in participant:
                formatted_participant = f"{participant}@s.whatsapp.net"
            else:
                formatted_participant = participant

            body["participant"] = formatted_participant

        # Make the request
        response = evolution_request(
            uri=f"/chat/deleteMessage/{instance_name}", method="DELETE", data=body
        )

        logger.info(
            f"Message {message_id} deleted successfully via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to delete message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for delete_message: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error deleting message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
