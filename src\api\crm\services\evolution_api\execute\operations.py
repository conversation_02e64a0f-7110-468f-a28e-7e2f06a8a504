"""
Evolution API Client - Object-Oriented Interface
This module provides a clean, maintainable OOP interface for Evolution API operations
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union


class EvolutionAPIResource(ABC):
    """Base class for Evolution API resources"""

    def __init__(self, instance_name: str = None):
        self.instance_name = instance_name

    @property
    @abstractmethod
    def resource_name(self) -> str:
        """Resource identifier"""
        pass


class MessagesAPI(EvolutionAPIResource):
    """Messages operations"""

    @property
    def resource_name(self) -> str:
        return "messages"

    def send_text(self, remote_jid: str, message_text: str, **kwargs) -> Dict[str, Any]:
        """Send text message"""
        from .messages.send_text import send_text

        return send_text(self.instance_name, remote_jid, message_text, **kwargs)

    def send_image(self, remote_jid: str, media: str, **kwargs) -> Dict[str, Any]:
        """Send image message"""
        from .messages.send_image import send_image

        return send_image(self.instance_name, remote_jid, media, **kwargs)

    def send_video(self, remote_jid: str, media: str, **kwargs) -> Dict[str, Any]:
        """Send video message"""
        from .messages.send_video import send_video

        return send_video(self.instance_name, remote_jid, media, **kwargs)

    def send_audio(self, remote_jid: str, media: str, **kwargs) -> Dict[str, Any]:
        """Send audio message"""
        from .messages.send_audio import send_audio

        return send_audio(self.instance_name, remote_jid, media, **kwargs)

    def send_document(self, remote_jid: str, media: str, **kwargs) -> Dict[str, Any]:
        """Send document message"""
        from .messages.send_document import send_document

        return send_document(self.instance_name, remote_jid, media, **kwargs)

    def send_contact(
        self, remote_jid: str, contacts: List[Dict[str, Any]], **kwargs
    ) -> Dict[str, Any]:
        """Send contact message"""
        from .messages.send_contact import send_contact

        return send_contact(self.instance_name, remote_jid, contacts, **kwargs)

    def send_buttons(
        self,
        remote_jid: str,
        title: str,
        description: str,
        buttons: List[Dict[str, Any]],
        **kwargs,
    ) -> Dict[str, Any]:
        """Send buttons message"""
        from .messages.send_buttons import send_buttons

        return send_buttons(
            self.instance_name, remote_jid, title, description, buttons, **kwargs
        )

    def send_list(
        self,
        remote_jid: str,
        title: str,
        description: str,
        button_text: str,
        sections: List[Dict[str, Any]],
        **kwargs,
    ) -> Dict[str, Any]:
        """Send list message"""
        from .messages.send_list import send_list

        return send_list(
            self.instance_name,
            remote_jid,
            title,
            description,
            button_text,
            sections,
            **kwargs,
        )

    def send_poll(
        self, remote_jid: str, poll_title: str, options: List[str], **kwargs
    ) -> Dict[str, Any]:
        """Send poll message"""
        from .messages.send_poll import send_poll

        return send_poll(self.instance_name, remote_jid, poll_title, options, **kwargs)

    def send_reaction(
        self, remote_jid: str, message_id: str, from_me: bool, reaction: str, **kwargs
    ) -> Dict[str, Any]:
        """Send reaction to message"""
        from .messages.send_reaction import send_reaction

        return send_reaction(
            self.instance_name, remote_jid, message_id, from_me, reaction, **kwargs
        )

    def send_pix(
        self, remote_jid: str, name: str, key_type: str, key: str, **kwargs
    ) -> Dict[str, Any]:
        """Send PIX payment message"""
        from .messages.send_pix import send_pix

        return send_pix(self.instance_name, remote_jid, name, key_type, key, **kwargs)

    def send_stories(self, content: str, story_type: str, **kwargs) -> Dict[str, Any]:
        """Send stories"""
        from .messages.send_stories import send_stories

        return send_stories(self.instance_name, content, story_type, **kwargs)


class InstanceAPI(EvolutionAPIResource):
    """Instance operations"""

    @property
    def resource_name(self) -> str:
        return "instances"

    def create_basic(self, **kwargs) -> Dict[str, Any]:
        """Create basic WhatsApp instance"""
        from .instance.create_instance_basic import create_instance_basic

        return create_instance_basic(self.instance_name, **kwargs)

    def fetch_all(self, **kwargs) -> Dict[str, Any]:
        """Fetch all instances"""
        from .instance.fetch_instances import fetch_instances

        return fetch_instances(**kwargs)

    def fetch(self, **kwargs) -> Dict[str, Any]:
        """Fetch specific instance"""
        from .instance.fetch_instances import fetch_instances

        return fetch_instances(self.instance_name, **kwargs)

    def connect(self, **kwargs) -> Dict[str, Any]:
        """Connect instance"""
        from .instance.instance_connect import instance_connect

        return instance_connect(self.instance_name, **kwargs)

    def delete(self, **kwargs) -> Dict[str, Any]:
        """Delete instance"""
        from .instance.delete_instance import delete_instance

        return delete_instance(self.instance_name, **kwargs)

    def logout(self, **kwargs) -> Dict[str, Any]:
        """Logout instance"""
        from .instance.logout_instance import logout_instance

        return logout_instance(self.instance_name, **kwargs)

    def restart(self, **kwargs) -> Dict[str, Any]:
        """Restart instance"""
        from .instance.restart_instance import restart_instance

        return restart_instance(self.instance_name, **kwargs)

    def set_presence(self, presence: str, **kwargs) -> Dict[str, Any]:
        """Set presence status"""
        from .instance.set_presence import set_presence

        return set_presence(self.instance_name, presence, **kwargs)

    def configure_settings(self, **kwargs) -> Dict[str, Any]:
        """Configure instance settings"""
        from .instance.instance_settings import instance_settings

        return instance_settings(self.instance_name, **kwargs)

    def set_proxy(
        self, enabled: bool, host: str, port: str, **kwargs
    ) -> Dict[str, Any]:
        """Set proxy configuration"""
        from .instance.set_proxy import set_proxy

        return set_proxy(self.instance_name, enabled, host, port, **kwargs)

    def find_proxy(self, **kwargs) -> Dict[str, Any]:
        """Find proxy configuration"""
        from .instance.set_proxy import find_proxy

        return find_proxy(self.instance_name, **kwargs)


class ChatAPI(EvolutionAPIResource):
    """Chat operations"""

    @property
    def resource_name(self) -> str:
        return "chat"

    def find_contacts(
        self, list_all: bool = False, remote_jid: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """Find contacts"""
        from .chat.find_contacts import find_contacts

        return find_contacts(self.instance_name, list_all, remote_jid, **kwargs)

    def check_numbers(self, numbers: Union[List[str], str], **kwargs) -> Dict[str, Any]:
        """Check WhatsApp numbers"""
        from .chat.check_number import check_number

        return check_number(self.instance_name, numbers, **kwargs)

    def find_messages(
        self, remote_jid: str, page: int = 1, offset: int = 10, **kwargs
    ) -> Dict[str, Any]:
        """Find messages in chat"""
        from .chat.find_messages import find_messages

        return find_messages(self.instance_name, remote_jid, page, offset, **kwargs)

    def read_messages(self, messages: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """Mark messages as read"""
        from .chat.read_messages import read_messages

        return read_messages(self.instance_name, messages, **kwargs)

    def read_single_message(
        self, remote_jid: str, from_me: bool, message_id: str, **kwargs
    ) -> Dict[str, Any]:
        """Mark single message as read"""
        from .chat.read_messages import read_single_message

        return read_single_message(
            self.instance_name, remote_jid, from_me, message_id, **kwargs
        )

    def delete_message(
        self, remote_jid: str, message_id: str, from_me: bool, **kwargs
    ) -> Dict[str, Any]:
        """Delete message"""
        from .chat.delete_message import delete_message

        return delete_message(
            self.instance_name, remote_jid, message_id, from_me, **kwargs
        )

    def block_contact(self, remote_jid: str, status: str, **kwargs) -> Dict[str, Any]:
        """Block/unblock contact"""
        from .chat.block_contact import block_contact

        return block_contact(self.instance_name, remote_jid, status, **kwargs)

    def send_presence(
        self, remote_jid: str, presence: str, delay: int = 0, **kwargs
    ) -> Dict[str, Any]:
        """Send presence status"""
        from .chat.send_presence import send_presence

        return send_presence(self.instance_name, remote_jid, presence, delay, **kwargs)


class GroupsAPI(EvolutionAPIResource):
    """Groups operations"""

    @property
    def resource_name(self) -> str:
        return "groups"

    def create(
        self,
        subject: str,
        description: str,
        participants: Union[List[str], str],
        **kwargs,
    ) -> Dict[str, Any]:
        """Create WhatsApp group"""
        from .groups.create_group import create_group

        return create_group(
            self.instance_name, subject, description, participants, **kwargs
        )

    def fetch_all(self, get_participants: bool = False, **kwargs) -> Dict[str, Any]:
        """Fetch all groups"""
        from .groups.fetch_groups import fetch_all_groups

        return fetch_all_groups(self.instance_name, get_participants, **kwargs)

    def fetch_by_invite_code(self, invite_code: str, **kwargs) -> Dict[str, Any]:
        """Fetch group by invite code"""
        from .groups.fetch_groups import fetch_groups_by_invite_code

        return fetch_groups_by_invite_code(self.instance_name, invite_code, **kwargs)

    def fetch_by_jid(self, group_jid: str, **kwargs) -> Dict[str, Any]:
        """Fetch group by JID"""
        from .groups.fetch_groups import fetch_group_by_jid

        return fetch_group_by_jid(self.instance_name, group_jid, **kwargs)

    def update_participants(
        self, group_jid: str, action: str, participants: Union[List[str], str], **kwargs
    ) -> Dict[str, Any]:
        """Update group participants"""
        from .groups.update_participants import update_participants

        return update_participants(
            self.instance_name, group_jid, action, participants, **kwargs
        )

    def join(self, invite_code: str, **kwargs) -> Dict[str, Any]:
        """Join group via invite code"""
        from .groups.join_group import join_group

        return join_group(self.instance_name, invite_code, **kwargs)

    def leave(self, group_jid: str, **kwargs) -> Dict[str, Any]:
        """Leave group"""
        from .groups.leave_group import leave_group

        return leave_group(self.instance_name, group_jid, **kwargs)

    def update_name(self, group_jid: str, subject: str, **kwargs) -> Dict[str, Any]:
        """Update group name"""
        from .groups.update_group_name import update_group_name

        return update_group_name(self.instance_name, group_jid, subject, **kwargs)

    def get_invite_code(self, group_jid: str, **kwargs) -> Dict[str, Any]:
        """Get group invite code"""
        from .groups.fetch_invite_code import fetch_invite_code

        return fetch_invite_code(self.instance_name, group_jid, **kwargs)

    def update_description(
        self, group_jid: str, description: str, **kwargs
    ) -> Dict[str, Any]:
        """Update group description"""
        from .groups.update_group_description import update_group_description

        return update_group_description(
            self.instance_name, group_jid, description, **kwargs
        )


class ProfileAPI(EvolutionAPIResource):
    """Profile operations"""

    @property
    def resource_name(self) -> str:
        return "profile"

    def fetch(self, remote_jid: str, **kwargs) -> Dict[str, Any]:
        """Fetch profile information"""
        from .profile.fetch_profile import fetch_profile

        return fetch_profile(self.instance_name, remote_jid, **kwargs)

    def update_name(self, name: str, **kwargs) -> Dict[str, Any]:
        """Update profile name"""
        from .profile.update_profile_name import update_profile_name

        return update_profile_name(self.instance_name, name, **kwargs)

    def update_status(self, status: str, **kwargs) -> Dict[str, Any]:
        """Update profile status"""
        from .profile.update_profile_status import update_profile_status

        return update_profile_status(self.instance_name, status, **kwargs)

    def fetch_business(self, remote_jid: str, **kwargs) -> Dict[str, Any]:
        """Fetch business profile"""
        from .profile.fetch_business_profile import fetch_business_profile

        return fetch_business_profile(self.instance_name, remote_jid, **kwargs)

    def fetch_privacy_settings(self, **kwargs) -> Dict[str, Any]:
        """Fetch privacy settings"""
        from .profile.fetch_privacy_settings import fetch_privacy_settings

        return fetch_privacy_settings(self.instance_name, **kwargs)


class EventsAPI(EvolutionAPIResource):
    """Events operations"""

    @property
    def resource_name(self) -> str:
        return "events"

    def set_webhook(self, enabled: bool, webhook_url: str, **kwargs) -> Dict[str, Any]:
        """Set webhook configuration"""
        from .events.set_webhook import set_webhook

        return set_webhook(self.instance_name, enabled, webhook_url, **kwargs)

    def find_webhook(self, **kwargs) -> Dict[str, Any]:
        """Find webhook configuration"""
        from .events.set_webhook import find_webhook

        return find_webhook(self.instance_name, **kwargs)


class IntegrationsAPI(EvolutionAPIResource):
    """Integrations operations"""

    @property
    def resource_name(self) -> str:
        return "integrations"

    def set_chatwoot(
        self, account_id: str, token: str, url: str, **kwargs
    ) -> Dict[str, Any]:
        """Set Chatwoot integration"""
        from .integrations.set_chatwoot import set_chatwoot

        return set_chatwoot(self.instance_name, account_id, token, url, **kwargs)

    def find_chatwoot(self, **kwargs) -> Dict[str, Any]:
        """Find Chatwoot configuration"""
        from .integrations.set_chatwoot import find_chatwoot

        return find_chatwoot(self.instance_name, **kwargs)


class InfoAPI(EvolutionAPIResource):
    """Info operations"""

    @property
    def resource_name(self) -> str:
        return "info"

    def get_info(self, **kwargs) -> Dict[str, Any]:
        """Fetch info"""
        from .info import get_info

        return get_info(**kwargs)


# Main client class
class EvolutionAPIClient:
    """
    Main Evolution API client providing a clean, object-oriented interface

    Usage:
        client = EvolutionAPIClient("my_instance")

        # Send a text message
        response = client.messages.send_text("***********", "Hello!")

        # Create a group
        response = client.groups.create("My Group", "Group description", ["***********", "***********"])

        # Check WhatsApp numbers
        response = client.chat.check_numbers(["***********", "***********"])
    """

    def __init__(self, instance_name: str):
        """
        Initialize Evolution API client

        Args:
            instance_name: Name of the WhatsApp instance to use
        """
        self.instance_name = instance_name
        self._messages = None
        self._instances = None
        self._chat = None
        self._groups = None
        self._profile = None
        self._events = None
        self._integrations = None

    @property
    def info(self) -> InfoAPI:
        """Access to info operations"""
        if self._info is None:
            self._info = InfoAPI(self.instance_name)
        return self._info

    @property
    def messages(self) -> MessagesAPI:
        """Access to messages operations"""
        if self._messages is None:
            self._messages = MessagesAPI(self.instance_name)
        return self._messages

    @property
    def instances(self) -> InstanceAPI:
        """Access to instance operations"""
        if self._instances is None:
            self._instances = InstanceAPI(self.instance_name)
        return self._instances

    @property
    def chat(self) -> ChatAPI:
        """Access to chat operations"""
        if self._chat is None:
            self._chat = ChatAPI(self.instance_name)
        return self._chat

    @property
    def groups(self) -> GroupsAPI:
        """Access to groups operations"""
        if self._groups is None:
            self._groups = GroupsAPI(self.instance_name)
        return self._groups

    @property
    def profile(self) -> ProfileAPI:
        """Access to profile operations"""
        if self._profile is None:
            self._profile = ProfileAPI(self.instance_name)
        return self._profile

    @property
    def events(self) -> EventsAPI:
        """Access to events operations"""
        if self._events is None:
            self._events = EventsAPI(self.instance_name)
        return self._events

    @property
    def integrations(self) -> IntegrationsAPI:
        """Access to integrations operations"""
        if self._integrations is None:
            self._integrations = IntegrationsAPI(self.instance_name)
        return self._integrations

    def __repr__(self) -> str:
        return f"EvolutionAPIClient(instance_name='{self.instance_name}')"
