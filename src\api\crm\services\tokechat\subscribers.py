from api.crm.services.tokechat.api_request import fetcher

"""
Get a subscriber (contact) by phone number.

Note: The phone number should be in the format 51987654321, with the
country code
"""

ENDPOINT = "/tickets"


def get_subscriber_by_phone(phone: str):
    try:
        # remove '+' and spaces
        phone = phone.replace("+", "").replace(" ", "")
        res = fetcher(f"{ENDPOINT}/get_contact/{phone}/")
        if res.get("success"):
            return res.get("response")["data"]

        print(f"Subscriber not found: {phone}")
        return None

    except Exception as e:
        print(f"An error occurred: {e}")
        return None


"""
Create a subscriber (contact) in TokeChat.
"""


def create_subscriber(phone, name):
    try:
        body = {"phone": phone, "name": name}

        created = fetcher(url=ENDPOINT, method="POST", data=body)

        if created.get("success"):
            return created.get("response")["data"]

        print(f"Error creating subscriber")
        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


"""
Send message to subscriber

Args:
    subscriber_id (str): The ID of the subscriber to send the message to.
    message (str or file path): The message content or file path to send.
    type (str): The type of message to send. Options:
        - "text": Send a text message (default)
        - "mead": Send URL ou Base64
Examples:
    # Send a text message
    send_message(subscriber_id, "Hello, world!")

    # Send a file
    send_message(subscriber_id, "url.jpg", type="file")
"""


def send_message(subscriber_id, message, type="text"):
    try:
        if type == "text" or type == "media":
            # Send a regular text message
            response = fetcher(
                f"{ENDPOINT}/{subscriber_id}/send_message/",
                method="POST",
                data={"type": type, "message": message},
            )
        else:
            raise ValueError(
                f"Unsupported message type: {type}. Supported types are 'text' and 'file'."
            )

        if response.get("success"):
            return response

        raise Exception(f"Error sending message to: {subscriber_id}")
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


"""
Send a flow to a subscriber
"""


def send_flow(subscriber_id: int, flow_id: int):
    try:
        response = fetcher(
            f"{ENDPOINT}/{subscriber_id}/send_flow/",
            method="POST",
            data={"flowId": flow_id},
        )

        if response.get("success"):
            return response

        raise Exception(f"Error sending flow to: {subscriber_id}")
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


"""
Endpoint doesn't exist in the new version
"""
# """
# Assign a subscriber to a sequence
# """
# def assign_subscriber_to_sequence(subscriber_id: int, sequence_id: int):
#     try:
#         response = fetcher(
#             f"{ENDPOINT}/{subscriber_id}/sequences/{sequence_id}/",
#             method="POST",
#             data={},
#         )

#         if response.get("success"):
#             return response

#         raise Exception(f"Error assigning subscriber to sequence: {subscriber_id}")
#     except Exception as e:
#         print(f"An error occurred: {e}")

"""
Not working, unexpected error
"""


def add_tag_to_subscriber(subscriber_id: int, tag_id: int):
    try:
        response = fetcher(
            f"{ENDPOINT}/{subscriber_id}/markers/{tag_id}",
            method="PUT",
            data={},
        )

        if response.get("success"):
            return response

        raise Exception(f"Error adding tag to subscriber: {subscriber_id}")
    except Exception as e:
        print(f"An error occurred: {e}")


"""
Endpoint doesn't exist in the new version
"""
# def remove_tag_from_subscriber(subscriber_id: int, tag_id: int):
#     try:
#         response = fetcher(
#             f"{ENDPOINT}/{subscriber_id}/markers/{tag_id}/",
#             method="DELETE",
#             data={},
#         )

#         if response.get("success"):
#             return response

#         raise Exception(f"Error removing tag from subscriber: {subscriber_id}")
#     except Exception as e:
#         print(f"An error occurred: {e}")
