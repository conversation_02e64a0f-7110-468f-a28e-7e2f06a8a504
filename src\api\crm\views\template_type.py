from rest_framework import viewsets
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter

from core.models.template import TemplateType
from api.crm.serializers.template_type import (
    CrmTemplateTypeSerializer,
)

from api.mixins import AuditMixin, SwaggerTagMixin


class CrmTemplateTypeViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing template variables
    """

    swagger_tags = ["Templates"]
    
    queryset = TemplateType.objects.filter(deleted=False)
    serializer_class = CrmTemplateTypeSerializer
    
    lookup_field = "ttid"
    filter_backends = [DjangoFilterBackend, OrderingFilter, SearchFilter]

    ordering_fields = ["name", "-created_at"]
    ordering = ["name"]
    search_fields = ["name"]

    http_method_names = ["get"]
