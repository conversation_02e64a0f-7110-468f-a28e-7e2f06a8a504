from rest_framework import serializers
from core.models import EducationalInstitution


class BaseEducationalInstitutionSerializer(serializers.ModelSerializer):
    """Base serializer for Educational Institution, used for listing."""

    key = serializers.CharField(source="eiid", read_only=True)

    class Meta:
        model = EducationalInstitution
        fields = [
            "key",
            "eiid",
            "name",
            "country",
            "region",
            "city",
            "acronym",
            "institution_type",
            "created_at",
            "updated_at",
        ]


class RetrieveEducationalInstitutionSerializer(BaseEducationalInstitutionSerializer):
    """Serializer for retrieving a single Educational Institution."""

    class Meta(BaseEducationalInstitutionSerializer.Meta):
        fields = BaseEducationalInstitutionSerializer.Meta.fields


class CreateEducationalInstitutionSerializer(serializers.ModelSerializer):
    """Serializer for creating a new Educational Institution."""

    def validate_name(self, value):
        import difflib

        existing_names = EducationalInstitution.objects.values_list("name", flat=True)
        value_lower = value.lower()
        for name in existing_names:
            if name.lower() == value_lower:
                raise serializers.ValidationError(
                    "Ya existe una institución educativa con un nombre similar (coincidencia insensible a mayúsculas/minúsculas)."
                )
            similarity = difflib.SequenceMatcher(
                None, name.lower(), value_lower
            ).ratio()
            if similarity > 0.85:
                raise serializers.ValidationError(
                    "Ya existe una institución educativa con un nombre muy similar."
                )
        return value

    def validate_acronym(self, value):
        if value:
            existing_acronyms = EducationalInstitution.objects.values_list(
                "acronym", flat=True
            )
            value_lower = value.lower()
            for acronym in existing_acronyms:
                if acronym and acronym.lower() == value_lower:
                    raise serializers.ValidationError(
                        "Ya existe una institución educativa con el mismo acrónimo."
                    )
        return value

    class Meta:
        model = EducationalInstitution
        fields = [
            "name",
            "country",
            "region",
            "city",
            "acronym",
            "institution_type",
        ]


class UpdateEducationalInstitutionSerializer(serializers.ModelSerializer):
    """Serializer for updating an existing Educational Institution."""

    class Meta:
        model = EducationalInstitution
        fields = [
            "name",
            "country",
            "region",
            "city",
            "acronym",
            "institution_type",
        ]
