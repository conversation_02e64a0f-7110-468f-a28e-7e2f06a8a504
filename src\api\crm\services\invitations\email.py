"""
Email invitation services with Google Calendar integration
"""

import logging
from django.conf import settings
from services.google.event import GoogleEventsManager
from .base import (
    InvitationServiceBase,
    InvitationResult,
    ServiceUnavailableError,
)
from core.models import EventScheduleEnrollment

logger = logging.getLogger(__name__)


class EmailInvitationService(InvitationServiceBase):
    """
    Email invitation service using Google Calendar integration
    this must work only adding an attendee to the event and automatically send the email by google
    """

    def __init__(self):
        self.google_calendar = GoogleEventsManager()

    def get_service_name(self) -> str:
        return "Email (Google Calendar)"

    def is_available(self) -> bool:
        """Check if Google Calendar service is available"""
        try:
            # Basic check for Google Calendar credentials and configuration
            return bool(
                self.google_calendar and hasattr(self.google_calendar, "service")
            )
        except Exception:
            return False

    def send_invitation(self, enrollment: EventScheduleEnrollment) -> InvitationResult:
        """
        Send email invitation with Google Calendar integration, the attendee role is only to see the meeting
        """
        try:
            # Get email address
            email = enrollment.email or (
                enrollment.user.email if enrollment.user else None
            )

            if not email:
                return InvitationResult(
                    success=False,
                    message="No email address available for invitation",
                    error_details={"error_type": "missing_email"},
                )

            # Use event ext_event_reference as calendar event id
            event_id = enrollment.event_schedule.ext_event_id

            if not event_id:
                return InvitationResult(
                    success=False,
                    message="No Google Calendar event ID found for this event schedule",
                    error_details={"error_type": "missing_event_id"},
                )

            # Add attendee to existing Google Calendar event
            response = self.google_calendar.add_attendee_to_event(
                event_id=event_id,
                attendee_email=email,
                calendar_id=getattr(settings, "GOOGLE_CALENDAR_ID", "primary"),
            )

            if not response:
                return InvitationResult(
                    success=False,
                    message="Failed to add attendee to Google Calendar event",
                    error_details={"error_type": "calendar_error"},
                )

            return InvitationResult(
                success=True,
                message="Email invitation sent successfully via Google Calendar",
                external_id=response.get("id"),
            )

        except ServiceUnavailableError:
            return InvitationResult(
                success=False,
                message="Email service is currently unavailable",
                error_details={"error_type": "service_unavailable"},
                retry_after=60,  # Retry after 1 minute
            )

        except Exception as e:
            logger.error(f"Email invitation failed: {e}")
            return InvitationResult(
                success=False,
                message=f"Failed to send email invitation: {str(e)}",
                error_details={"error_type": "email_error", "details": str(e)},
            )

    def send_bulk_invitations(self, event_schedule, enrollments) -> dict:
        """
        Send email invitations for multiple enrollments in a single Google Calendar update
        to avoid API rate limiting issues

        Args:
            event_schedule: EventSchedule instance
            enrollments: List of EventScheduleEnrollment instances

        Returns:
            dict: Results with success/failure counts and details
        """
        try:
            # Get event ID
            event_id = event_schedule.ext_event_id
            if not event_id:
                return {
                    "success": False,
                    "message": "No Google Calendar event ID found for this event schedule",
                    "results": [],
                }

            # Collect valid emails
            valid_attendees = []
            results = []

            for enrollment in enrollments:
                email = enrollment.email or (
                    enrollment.user.email if enrollment.user else None
                )

                if not email:
                    results.append(
                        {
                            "enrollment_id": enrollment.id,
                            "success": False,
                            "message": "No email address available",
                            "email": None,
                        }
                    )
                    continue

                valid_attendees.append({"email": email, "enrollment_id": enrollment.id})

            if not valid_attendees:
                return {
                    "success": False,
                    "message": "No valid email addresses found",
                    "results": results,
                }

            # Add all attendees in a single API call
            attendee_emails = [attendee["email"] for attendee in valid_attendees]
            response = self.google_calendar.add_multiple_attendees_to_event(
                event_id=event_id,
                attendee_emails=attendee_emails,
                calendar_id=getattr(settings, "GOOGLE_CALENDAR_ID", "primary"),
            )

            if response:
                # Mark all as successful
                for attendee in valid_attendees:
                    results.append(
                        {
                            "enrollment_id": attendee["enrollment_id"],
                            "success": True,
                            "message": "Email invitation sent successfully via Google Calendar",
                            "email": attendee["email"],
                        }
                    )

                return {
                    "success": True,
                    "message": f"Successfully sent {len(valid_attendees)} email invitations",
                    "results": results,
                }
            else:
                # Mark all as failed
                for attendee in valid_attendees:
                    results.append(
                        {
                            "enrollment_id": attendee["enrollment_id"],
                            "success": False,
                            "message": "Failed to add attendees to Google Calendar event",
                            "email": attendee["email"],
                        }
                    )

                return {
                    "success": False,
                    "message": "Failed to add attendees to Google Calendar event",
                    "results": results,
                }

        except Exception as e:
            logger.error(f"Bulk email invitation failed: {e}")
            # Mark all enrollments as failed
            for enrollment in enrollments:
                results.append(
                    {
                        "enrollment_id": enrollment.id,
                        "success": False,
                        "message": f"Failed to send email invitation: {str(e)}",
                        "email": enrollment.email
                        or (enrollment.user.email if enrollment.user else None),
                    }
                )

            return {
                "success": False,
                "message": f"Failed to send bulk email invitations: {str(e)}",
                "results": results,
            }
