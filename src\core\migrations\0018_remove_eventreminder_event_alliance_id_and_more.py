# Generated by Django 5.0.6 on 2025-08-06 21:00

import django.contrib.postgres.fields.ranges
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0017_register_new_permissions_for_order_activity_and_user"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="eventreminder",
            name="event_alliance_id",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="event_name",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="reminder_type",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="send_at",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="status",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="template_id",
        ),
        migrations.RemoveField(
            model_name="eventreminder",
            name="variables",
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="enrollment",
            field=models.ForeignKey(
                blank=True,
                help_text="The enrollment this reminder is associated with",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="event_reminders",
                to="core.eventscheduleenrollment",
                verbose_name="Event Schedule Enrollment",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="last_error_email",
            field=models.TextField(
                blank=True,
                help_text="Last error message for email invitation",
                null=True,
                verbose_name="Email Last Error",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="last_error_whatsapp",
            field=models.TextField(
                blank=True,
                help_text="Last error message for WhatsApp invitation",
                null=True,
                verbose_name="WhatsApp Last Error",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="retry_count_email",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Number of retry attempts for email invitation",
                verbose_name="Email Retry Count",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="retry_count_whatsapp",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Number of retry attempts for WhatsApp invitation",
                verbose_name="WhatsApp Retry Count",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="sent_at_email",
            field=models.DateTimeField(
                blank=True,
                help_text="When the email invitation was actually sent",
                null=True,
                verbose_name="Email Sent At",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="sent_at_whatsapp",
            field=models.DateTimeField(
                blank=True,
                help_text="When the WhatsApp invitation was actually sent",
                null=True,
                verbose_name="WhatsApp Sent At",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="status_email",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("SENT", "Sent"),
                    ("FAILED", "Failed"),
                    ("RETRYING", "Retrying"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                max_length=10,
                verbose_name="Email Status",
            ),
        ),
        migrations.AddField(
            model_name="eventreminder",
            name="status_whatsapp",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("SENT", "Sent"),
                    ("FAILED", "Failed"),
                    ("RETRYING", "Retrying"),
                    ("CANCELLED", "Cancelled"),
                ],
                default="PENDING",
                max_length=10,
                verbose_name="WhatsApp Status",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="is_whatsapp_active",
            field=models.BooleanField(
                blank=True,
                default=True,
                help_text="Enable or disable WhatsApp notifications for this event schedule. If disabled, no WhatsApp invitations will be sent.",
                null=True,
                verbose_name="WhatsApp Notifications Active",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="scheduled_datetime_email",
            field=models.DateTimeField(
                blank=True,
                help_text="When to start sending email notifications for this event schedule",
                null=True,
                verbose_name="Email Scheduled DateTime",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="scheduled_datetime_whatsapp",
            field=models.DateTimeField(
                blank=True,
                help_text="When to start sending WhatsApp notifications for this event schedule",
                null=True,
                verbose_name="WhatsApp Scheduled DateTime",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="whatsapp_delay_range",
            field=django.contrib.postgres.fields.ranges.IntegerRangeField(
                blank=True,
                help_text="Range of delay before sending WhatsApp notifications [min-max]",
                null=True,
                verbose_name="WhatsApp Delay Range (in seconds)",
            ),
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="whatsapp_template",
            field=models.ForeignKey(
                blank=True,
                help_text="Template for WhatsApp notifications (uses ext_reference as flow ID)",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="event_schedules_whatsapp",
                to="core.template",
                verbose_name="WhatsApp Template",
            ),
        ),
        migrations.AddField(
            model_name="template",
            name="ext_reference",
            field=models.CharField(
                blank=True,
                null=True,
                verbose_name="External Reference for integrations, ej: flow ID's",
            ),
        ),
        migrations.AlterField(
            model_name="template",
            name="body_text",
            field=models.TextField(
                blank=True, max_length=1024, null=True, verbose_name="Body Text"
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["status_whatsapp"], name="core_eventr_status__c7ac1b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["status_email"], name="core_eventr_status__ee747b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="eventreminder",
            index=models.Index(
                fields=["enrollment"], name="core_eventr_enrollm_1abda8_idx"
            ),
        ),
    ]
