from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from core.models import BroadcastMessage
from api.crm.serializers.broadcast_message import (
    CrmBroadcastMessageSerializer,
    CrmCreateBroadcastMessageSerializer,
    CrmSendBroadcastMessageSerializer,
    CrmUpdateBroadcastMessageSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsA<PERSON>enticated, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin


class CrmBroadcastMessageViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = BroadcastMessage
    queryset = BroadcastMessage.objects.filter(deleted=False).order_by("created_at")
    serializer_class = CrmBroadcastMessageSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    swagger_tags = ["BroadcastMessages"]

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateBroadcastMessageSerializer
        if self.action in ["update", "partial_update"]:
            return CrmUpdateBroadcastMessageSerializer
        return super().get_serializer_class()

    @action(detail=True, methods=["post"], url_path="send")
    def send_broadcast_message(self, request, pk=None):
        """
        Acción personalizada para enviar un mensaje de evento a WhatsApp.
        """
        broadcast_message = self.get_object()
        serializer = CrmSendBroadcastMessageSerializer(broadcast_message)
        try:
            res = serializer.send_broadcast_message(broadcast_message)
            if res.get("errors"):
                return Response(
                    {
                        "detail": "Mensajes enviados a WhatsApp con algunos errores.",
                        "errors": res.get("errors"),
                        "count": res.get("count"),
                    }
                )
            return Response(
                {
                    "detail": "Mensajes enviados a WhatsApp exitosamente.",
                    "count": res.get("count"),
                }
            )
        except Exception as e:
            return Response(
                {"detail": f"{str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def destroy(self, request, *args, **kwargs):
        """
        Marca un mensaje como eliminada en lugar de eliminarla físicamente.
        """
        instance = self.get_object()
        instance.deleted = True
        instance.save()

        return Response(
            {"detail": "El mensaje se marcó como eliminado."},
            status=status.HTTP_204_NO_CONTENT,
        )
