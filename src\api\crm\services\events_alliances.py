from core.appsheet import appsheet_client


def get_events_alliances():
    events_alliances = appsheet_client.find_items(table_name="Evento Alianza")
    return events_alliances


def get_current_events_alliances():
    events_alliances = appsheet_client.find_items(
        table_name="Evento Alianza", filter_expression='[Estado] = "En curso"'
    )
    return events_alliances


def get_event_registrations_alliances(event_alliance_id: int):
    if not event_alliance_id:
        return []
    registrations = appsheet_client.find_items(
        table_name="Inscripción a Evento",
        filter_expression=f'[ID Evento Alianza] = "{event_alliance_id}"',
    )

    # discard if registration["Deleted"] is "Y"
    registrations = [
        registration for registration in registrations if registration["Deleted"] != "Y"
    ]
    registrations = [
        {
            "Id": registration["ID Inscripción"],
            "Nombres": registration["Nombres"],
            "WhatsApp": registration["WhatsApp"],
        }
        for registration in registrations
    ]

    return registrations
