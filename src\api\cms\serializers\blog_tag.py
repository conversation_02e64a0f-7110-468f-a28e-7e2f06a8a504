from rest_framework import serializers
from core.models import BlogTag
from django.utils.text import slugify


class CmsBlogTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogTag
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]


class CmsCreateBlogTagSerializer(CmsBlogTagSerializer):
    slug = serializers.SlugField(write_only=True, required=False)
    
    class Meta:
        model = BlogTag
        fields = ["name", "description", "badge_color", "slug"]

    def create(self, validated_data):
        # Generate slug from name
        name = validated_data.get("name")

        # Create slug from name if not provided
        if not validated_data.get("slug"):
            slug = slugify(name)

            # Check if slug exists and make it unique if needed
            if BlogTag.objects.filter(slug=slug).exists():
                count = 1
                while BlogTag.objects.filter(slug=f"{slug}-{count}").exists():
                    count += 1
                slug = f"{slug}-{count}"

            validated_data["slug"] = slug
        return super().create(validated_data)


class CmsUpdateBlogTagSerializer(CmsBlogTagSerializer):
    class Meta:
        model = BlogTag
        fields = ["name", "description", "slug", "badge_color"]

    def update(self, instance, validated_data):
        # If name is updated but slug is not, update the slug
        if "name" in validated_data and "slug" not in validated_data:
            name = validated_data.get("name")
            slug = slugify(name)
            
            # Check if slug exists and make it unique if needed
            if BlogTag.objects.filter(slug=slug).exclude(btid=instance.btid).exists():
                count = 1
                while BlogTag.objects.filter(slug=f"{slug}-{count}").exclude(btid=instance.btid).exists():
                    count += 1
                slug = f"{slug}-{count}"
            
            validated_data["slug"] = slug
        
        return super().update(instance, validated_data)
