from django.utils import timezone
from rest_framework import viewsets, status, filters
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from core.models import User as Contact, File
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.contact import (
    CrmBaseContactSerializer,
    CrmContactListItemSerializer,
    CrmCreateContactSerializer,
    CrmUpdateContactSerializer,
    CrmRetrieveContactSerializer,
)
from api.shared.serializers.file import FileSerializer
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser, CanManageUsers
from services.google.contact import GoogleContactsManager
from api.crm.services.file import upload_file_to_minio
from uuid import UUID
from api.crm.filters.contact import CrmContactFilter


class CrmContactViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Contact
    queryset = Contact.objects.filter(deleted=False).order_by("-created_at")
    swagger_tags = ["Contacts"]
    serializer_class = (CrmBaseContactSerializer,)
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser & CanManageUsers]
    pagination_class = StandardResultsPagination

    filterset_class = CrmContactFilter
    ordering_fields = ["created_at"]

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateContactSerializer
        elif self.action == "list":
            return CrmContactListItemSerializer
        elif self.action == "retrieve":
            return CrmRetrieveContactSerializer
        elif self.action == "partial_update":
            return CrmUpdateContactSerializer
        return super().get_serializer_class()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmRetrieveContactSerializer(
            serializer.instance, context={"request": request}
        )
        return Response(
            retrieve_serializer.data,
            status=status.HTTP_201_CREATED,
        )

    @action(
        detail=True,
        methods=["PATCH"],
        url_path="sync-google",
        serializer_class=None,
    )
    def sync_google(self, request, pk=None):
        contact = self.get_object()
        google_contacts_manager = GoogleContactsManager()

        # If contact not have google_contact_id but it has phone_number
        if not contact.google_contact_id:

            # Search contact in google by phone_number
            google_contacts_manager = GoogleContactsManager()
            results = google_contacts_manager.search_contacts(
                query=contact.phone_number
            )

            if results:
                # If found, update contact with google_contact_id
                contact.google_contact_id = results[0]["person"]["resourceName"].split(
                    "/"
                )[-1]
                contact.last_google_sync = timezone.now()
                contact.save()
            else:
                # If not found, create contact in google
                res = google_contacts_manager.create_contact(
                    first_name=contact.first_name,
                    last_name=contact.last_name,
                    email=contact.email,
                    phone=contact.phone_number,
                )
                if res:
                    contact.google_contact_id = res["resourceName"].split("/")[-1]
                    contact.last_google_sync = timezone.now()
                    contact.save()
        elif contact.google_contact_id:
            # If contact has google_contact_id, update contact in google
            google_contacts_manager = GoogleContactsManager()
            res = google_contacts_manager.update_contact(
                resource_name=f"people/{contact.google_contact_id}",
                first_name=contact.first_name,
                last_name=contact.last_name,
                email=contact.email,
                phone=contact.phone_number,
            )
            contact.last_google_sync = timezone.now()
            contact.save()

        return Response(
            status=status.HTTP_200_OK,
            data=CrmRetrieveContactSerializer(contact).data,
        )

    @action(
        methods=["POST"],
        detail=True,
        url_path="upload-profile-photo",
    )
    def upload_profile_photo(self, request, pk=None):
        contact = self.get_object()
        if "file" not in request.FILES:
            return Response(
                {"error": "No se ha proporcionado ningún archivo"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # check if contact has a profile photo, if so, delete it
        if contact.profile_photo:
            contact.profile_photo.delete()
            contact.profile_photo = None
            contact.save()

        file_obj = request.FILES["file"]
        fid, object_name = upload_file_to_minio(file_obj)
        file = File.objects.create(
            fid=fid,
            is_used=False,
            is_private=True,
            name=object_name.split("/")[-1],
            bucket_name="private",
            object_name=object_name,
        )
        file.save()
        contact.profile_photo = file
        contact.save()
        file.is_used = True
        file.save()
        return Response(
            status=status.HTTP_200_OK,
            data=FileSerializer(file).data,
        )

    @action(
        detail=True,
        methods=["DELETE"],
        url_path="delete-profile-photo/(?P<fid>[^/.]+)",
    )
    def delete_profile_photo(self, request, pk=None, fid=None):
        contact = self.get_object()
        try:
            fid_uuid = UUID(str(fid))
        except (ValueError, TypeError, AttributeError):
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"detail": "Invalid file ID format."},
            )
        if str(contact.profile_photo.fid) != str(fid_uuid):
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"detail": "File ID does not match the profile photo."},
            )

        if contact.profile_photo:
            contact.profile_photo.delete()
            contact.profile_photo = None
            contact.save()
            return Response(
                status=status.HTTP_204_NO_CONTENT,
                data={"detail": "Profile photo deleted successfully."},
            )
        return Response(
            status=status.HTTP_400_BAD_REQUEST,
            data={"detail": "No profile photo to delete."},
        )
