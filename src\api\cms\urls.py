from rest_framework import routers
from api.cms.views import instructor as instructor_views
from api.cms.views import testimonial as testimonial_views
from api.cms.views import offering as offering_views
from api.cms.views import blog as blog_views
from api.cms.views import blog_category as blog_category_views
from api.cms.views import blog_tag as blog_tag_views

router = routers.DefaultRouter(trailing_slash=False)
router.register(
    r"instructors",
    instructor_views.CmsInstructorViewSet,
    basename="cms-instructors",
)
router.register(
    r"testimonials",
    testimonial_views.CmsTestimonialViewSet,
    basename="cms-testimonials",
)
router.register(
    r"offerings",
    offering_views.CmsOfferingViewSet,
    basename="cms-offerings",
)
router.register(
    r"blogs/posts",
    blog_views.CmsBlogPostViewSet,
    basename="cms-blog-posts",
)
router.register(
    r"blogs/categories",
    blog_category_views.CmsBlogCategoryViewSet,
    basename="cms-blog-categories",
)
router.register(
    r"blogs/tags",
    blog_tag_views.CmsBlogTagViewSet,
    basename="cms-blog-tags",
)

urlpatterns = router.urls
