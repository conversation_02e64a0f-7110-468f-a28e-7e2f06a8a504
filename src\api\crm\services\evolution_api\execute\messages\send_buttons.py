"""
Send buttons message via Evolution API
"""

import logging
from typing import Dict, Any, List, Optional, TypedDict, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

ButtonType = Literal["reply", "copy", "url", "call"]


class ButtonInfo(TypedDict, total=False):
    """Button information structure"""

    type: ButtonType
    display_text: str
    id: Optional[str]
    copy_code: Optional[str]
    url: Optional[str]
    phone_number: Optional[str]


def send_buttons(
    instance_name: str,
    remote_jid: str,
    title: str,
    description: str,
    buttons: List[ButtonInfo],
    footer: str = "",
    delay: Optional[int] = None,
    quoted_message_id: Optional[str] = None,
    mentions_everyone: Optional[bool] = None,
    mentioned_numbers: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Send buttons message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        title: Title of the buttons message
        description: Description text
        buttons: List of button configurations (1-3 buttons)
        footer: Footer text (optional)
        delay: Delay in milliseconds before sending
        quoted_message_id: ID of message to quote/reply to
        mentions_everyone: Whether to mention everyone in group
        mentioned_numbers: List of phone numbers to mention

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If buttons configuration is invalid
    """
    try:
        # Validate buttons
        if not buttons or not isinstance(buttons, list) or len(buttons) > 3:
            raise ValueError("Must provide between 1 and 3 buttons")

        # Build buttons list
        button_list = []
        for button in buttons:
            button_data = {
                "type": button["type"],
                "displayText": button["display_text"],
            }

            # Add type-specific fields
            if button["type"] == "reply" and button.get("id"):
                button_data["id"] = button["id"]
            elif button["type"] == "copy" and button.get("copy_code"):
                button_data["copyCode"] = button["copy_code"]
            elif button["type"] == "url" and button.get("url"):
                button_data["url"] = button["url"]
            elif button["type"] == "call" and button.get("phone_number"):
                button_data["phoneNumber"] = button["phone_number"]

            button_list.append(button_data)

        # Build request body
        body = {
            "number": remote_jid,
            "buttonMessage": {
                "title": title,
                "description": description,
                "footer": footer,
                "buttons": button_list,
            },
        }

        # Add optional delay
        if delay is not None:
            body["delay"] = delay

        # Add quoted message
        if quoted_message_id:
            body["quoted"] = {
                "key": {
                    "id": quoted_message_id,
                }
            }

        # Handle mentions
        if mentions_everyone:
            body["mentionsEveryOne"] = True
        elif mentioned_numbers:
            # Ensure numbers have WhatsApp format
            formatted_numbers = []
            for num in mentioned_numbers:
                num = num.strip()
                if "@s.whatsapp.net" not in num:
                    num = f"{num}@s.whatsapp.net"
                formatted_numbers.append(num)
            body["mentioned"] = formatted_numbers

        # Make the request
        response = evolution_request(
            uri=f"/message/sendButtons/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Buttons message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send buttons message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid buttons parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending buttons message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
