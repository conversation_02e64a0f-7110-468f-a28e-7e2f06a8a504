"""
Filters for EventReminder model
"""

import django_filters
from django.db.models import Q
from core.models import EventReminder
from api.crm.utils.phone_numbers import PhoneNumberUtils


class CrmEventReminderFilter(django_filters.FilterSet):
    """Filter for EventReminder with user search capabilities"""

    # User name search (searches in both enrollment user and enrollment direct fields)
    user = django_filters.CharFilter(
        method="filter_by_user", label="User Name, Phone Number or Email"
    )

    # Status filters
    status_whatsapp = django_filters.ChoiceFilter(
        choices=EventReminder.STATUS_CHOICES, label="WhatsApp Status"
    )
    status_email = django_filters.ChoiceFilter(
        choices=EventReminder.STATUS_CHOICES, label="Email Status"
    )

    # Event schedule filters
    event_schedule = django_filters.UUIDFilter(
        field_name="enrollment__event_schedule__esid", label="Event Schedule ID"
    )

    # Template filters (now from event schedule)
    whatsapp_template = django_filters.UUIDFilter(
        field_name="enrollment__event_schedule__whatsapp_template__tid",
        label="WhatsApp Template ID",
    )

    # Failed invitations filter
    has_failed_invitations = django_filters.BooleanFilter(
        method="filter_failed_invitations", label="Has Failed Invitations"
    )

    # Pending invitations filter
    has_pending_invitations = django_filters.BooleanFilter(
        method="filter_pending_invitations", label="Has Pending Invitations"
    )

    # Sent invitations filter
    has_sent_invitations = django_filters.BooleanFilter(
        method="filter_sent_invitations", label="Has Sent Invitations"
    )

    class Meta:
        model = EventReminder
        fields = [
            "user",
            "status_whatsapp",
            "status_email",
            "event_schedule",
            "whatsapp_template",
            "has_failed_invitations",
            "has_pending_invitations",
            "has_sent_invitations",
        ]

    def filter_by_user(self, queryset, name, value):
        """
        Filter by user name (searches in enrollment user and enrollment direct fields)
        """
        if not value:
            return queryset

        # Check if it's a phone number using the utility
        if PhoneNumberUtils.is_phone_number(value):
            # Use only the cleaned phone number for search
            value = PhoneNumberUtils.normalize_phone_number(value)["cleaned"]

            # filter only for phone number
            return queryset.filter(
                Q(enrollment__phone_number__icontains=value)
                | Q(enrollment__user__phone_number__icontains=value)
            )

        return queryset.filter(
            Q(enrollment__user__first_name__icontains=value)
            | Q(enrollment__user__last_name__icontains=value)
            | Q(enrollment__first_name__icontains=value)
            | Q(enrollment__last_name__icontains=value)
            | Q(enrollment__email__icontains=value)
            | Q(enrollment__user__email__icontains=value)
            | Q(enrollment__phone_number__icontains=value)
            | Q(enrollment__user__phone_number__icontains=value)
        ).distinct()

    def filter_failed_invitations(self, queryset, name, value):
        """
        Filter reminders that have failed invitations
        """
        if value is None:
            return queryset

        if value:
            return queryset.filter(
                Q(status_whatsapp=EventReminder.FAILED)
                | Q(status_email=EventReminder.FAILED)
            ).distinct()
        else:
            return queryset.exclude(
                Q(status_whatsapp=EventReminder.FAILED)
                | Q(status_email=EventReminder.FAILED)
            ).distinct()

    def filter_pending_invitations(self, queryset, name, value):
        """
        Filter reminders that have pending invitations
        """
        if value is None:
            return queryset

        if value:
            return queryset.filter(
                Q(status_whatsapp=EventReminder.PENDING)
                | Q(status_email=EventReminder.PENDING)
            ).distinct()
        else:
            return queryset.exclude(
                Q(status_whatsapp=EventReminder.PENDING)
                | Q(status_email=EventReminder.PENDING)
            ).distinct()

    def filter_sent_invitations(self, queryset, name, value):
        """
        Filter reminders that have sent invitations
        """
        if value is None:
            return queryset

        if value:
            return queryset.filter(
                Q(status_whatsapp=EventReminder.SENT)
                | Q(status_email=EventReminder.SENT)
            ).distinct()
        else:
            return queryset.exclude(
                Q(status_whatsapp=EventReminder.SENT)
                | Q(status_email=EventReminder.SENT)
            ).distinct()
