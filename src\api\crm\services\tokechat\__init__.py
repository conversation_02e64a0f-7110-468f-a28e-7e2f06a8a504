from api.crm.services.tokechat.subscribers import (
    get_subscriber_by_phone,
    create_subscriber,
    send_flow,
)
from services.cache.redis import CacheManager
import logging
from typing import Dict, Any
from api.crm.services.invitations.base import (
    ServiceUnavailableError,
)

logger = logging.getLogger(__name__)


class TokeChatAPIService:
    """
    TokeChat API integration for WhatsApp messaging
    """

    CACHE_PREFIX = "tokechat"
    TIMEOUT = 60 * 2  # 2 minutes
    cache_manager = CacheManager(prefix=CACHE_PREFIX, timeout=TIMEOUT)

    def is_available(self) -> bool:
        """Check if TokeChat API is available with caching"""
        cached_status = self.cache_manager.get("tokechat_availability")
        if cached_status is not None:
            return cached_status

        try:
            # Simple health check endpoint
            response = get_subscriber_by_phone("51987654321")
            is_available = response.get("id", None) is not None

            # Cache the result
            self.cache_manager.set("tokechat_availability", is_available)

            return is_available

        except Exception as e:
            logger.warning(f"TokeChat availability check failed: {e}")
            # Cache negative result for shorter time
            self.cache_manager.set("tokechat_availability", False)
            return False

    def send_flow_message(
        self, phone_number: str, flow_id: str, variables: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Send a flow message via TokeChat using the correct order:
        1. Get subscriber_id by phone number
        2. Send flow using subscriber_id and flow_id

        Args:
            phone_number: Target phone number (format: +51928141807)
            flow_id: Flow ID from template.ext_reference
            variables: Variables for the flow (not used in current TokeChat implementation)

        Returns:
            API response as dictionary
        """
        if not self.is_available():
            raise ServiceUnavailableError("TokeChat API is not available")

        try:
            # Step 1: Clean phone number (remove + and spaces)
            clean_phone = phone_number.replace("+", "").replace(" ", "")

            # Step 2: Get subscriber by phone number
            subscriber = get_subscriber_by_phone(clean_phone)

            if not subscriber:
                # If subscriber doesn't exist, create one
                # Extract name from variables or use default
                name = variables.get("name", "Usuario") if variables else "Usuario"
                subscriber = create_subscriber(clean_phone, name)

                if not subscriber:
                    raise Exception(
                        f"Failed to create subscriber for phone: {clean_phone}"
                    )

            subscriber_id = subscriber.get("id")
            if not subscriber_id:
                raise Exception(f"No subscriber ID found for phone: {clean_phone}")

            # Step 3: Send flow to subscriber
            response = send_flow(subscriber_id, int(flow_id))

            if not response or not response.get("success"):
                raise Exception(
                    f"Failed to send flow {flow_id} to subscriber {subscriber_id}"
                )

            return response

        except Exception as e:
            logger.error(f"TokeChat flow sending error: {e}")
            raise
