from rest_framework import serializers
from core.models import (
    Offering,
    Enrollment,
    OfferingModule,
    ModuleCourse,
    Topic,
    Session,
    SessionResource,
)
from api.shared.serializers.file import FileSerializer


class InstructorSerializer(serializers.Serializer):
    uid = serializers.SerializerMethodField()
    fullname = serializers.SerializerMethodField()

    def get_uid(self, obj):
        return f"{obj.user.uid}"

    def get_fullname(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"


class ClassroomOfferingBaseSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="oid")
    offering_type = serializers.CharField(source="type")
    instructors = serializers.SerializerMethodField()
    thumbnail = FileSerializer(
        read_only=True,
        allow_null=True,
    )
    progress = serializers.SerializerMethodField()

    class Meta:
        model = Offering
        fields = [
            "oid",
            "key",
            "slug",
            "name",
            "description",
            "offering_type",
            "duration",
            "start_date",
            "end_date",
            "progress",
            "instructors",
            "thumbnail",
            "created_at",
        ]

    def get_instructors(self, obj):
        instructors = obj.offering_enrollments.filter(
            enrollment_type=Enrollment.TEACHER_ENROLLMENT
        ).select_related("user")
        return InstructorSerializer(instructors, many=True).data

    def get_progress(self, obj):
        return 40


class ModuleSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="omid")
    courses = serializers.SerializerMethodField()

    class Meta:
        model = OfferingModule
        fields = ["key", "title", "courses"]

    def get_courses(self, obj):
        return CourseSerializer(obj.courses.all(), many=True).data


class CourseSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="mcid")
    topics = serializers.SerializerMethodField()

    class Meta:
        model = ModuleCourse
        fields = ["key", "title", "topics"]

    def get_topics(self, obj):
        return TopicSerializer(obj.topics.all(), many=True).data


class TopicSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="tid")
    sessions = serializers.SerializerMethodField()

    class Meta:
        model = Topic
        fields = ["key", "title", "sessions"]

    def get_sessions(self, obj):
        return SessionSerializer(obj.sessions.all(), many=True).data


class SessionSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="sid")
    resources = serializers.SerializerMethodField()

    class Meta:
        model = Session
        fields = ["key", "title", "description", "resources"]

    def get_resources(self, obj):
        return SessionResourceSerializer(obj.resources.all(), many=True).data


class SessionResourceSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="rid")
    file = FileSerializer(read_only=True)

    class Meta:
        model = SessionResource
        fields = [
            "key",
            "title",
            "description",
            "resource_type",
            "file",
        ]


class ClassroomOfferingDetailedSerializer(ClassroomOfferingBaseSerializer):
    modules = serializers.SerializerMethodField()

    def get_modules(self, obj):
        return ModuleSerializer(obj.modules.all(), many=True).data

    class Meta(ClassroomOfferingBaseSerializer.Meta):
        fields = ClassroomOfferingBaseSerializer.Meta.fields + [
            "modules",
        ]
