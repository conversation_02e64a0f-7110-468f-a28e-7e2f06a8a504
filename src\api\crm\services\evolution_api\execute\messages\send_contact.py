"""
Send contact message via Evolution API
"""

import logging
from typing import Dict, Any, List, Optional, TypedDict
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


class ContactInfo(TypedDict):
    """Contact information structure"""

    full_name: str
    wuid: str
    phone_number: str
    organization: Optional[str]
    email: Optional[str]
    url: Optional[str]


def send_contact(
    instance_name: str, remote_jid: str, contacts: List[ContactInfo]
) -> Dict[str, Any]:
    """
    Send contact(s) message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        contacts: List of contact information dictionaries

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If contacts list is empty or invalid
    """
    try:
        # Validate contacts
        if not contacts or not isinstance(contacts, list):
            raise ValueError("At least one contact is required")

        # Build contact list
        contact_list = []
        for contact in contacts:
            contact_data = {
                "fullName": contact["full_name"],
                "wuid": contact["wuid"],
                "phoneNumber": contact["phone_number"],
            }

            # Add optional fields if provided
            if contact.get("organization"):
                contact_data["organization"] = contact["organization"]
            if contact.get("email"):
                contact_data["email"] = contact["email"]
            if contact.get("url"):
                contact_data["url"] = contact["url"]

            contact_list.append(contact_data)

        # Build request body
        body = {
            "number": remote_jid,
            "contact": contact_list,
        }

        # Make the request
        response = evolution_request(
            uri=f"/message/sendContact/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Contact message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send contact message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid contact parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending contact message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
