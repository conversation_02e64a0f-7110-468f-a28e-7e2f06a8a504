import uuid
import secrets
import string
from django.db import models
from core.models.base import AuditBaseModel


def generate_unique_slug():
    """
    Generate a unique slug for blog categories.
    Examples:
        - 'category-1'
        - 'category-2'
        - 'category-3'
    """
    length = 12
    characters = string.ascii_lowercase + string.digits
    return "".join(secrets.choice(characters) for _ in range(length))


class BlogCategory(AuditBaseModel):
    bcid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Name",
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        verbose_name="Slug",
        default=generate_unique_slug,
    )
    description = models.TextField(blank=True, null=True, verbose_name="Description")
    parent = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='children',
        verbose_name="Parent Category",
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Blog Category"
        verbose_name_plural = "Blog Categories"
