from django.contrib import admin
from core.models import Instructor


@admin.register(Instructor)
class InstructorAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "full_name",
        "status",
        "profile_photo",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = (
        "status",
        "deleted",
    )
    search_fields = ("title",)
