"""
Update profile status via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def update_profile_status(instance_name: str, status: str) -> Dict[str, Any]:
    """
    Update WhatsApp profile status

    Args:
        instance_name: Name of the WhatsApp instance
        status: New profile status message

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "status": status,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/updateProfileStatus/{instance_name}", method="POST", data=body
        )

        logger.info(f"Profile status updated successfully for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to update profile status: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating profile status: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
