from django.contrib.auth.models import Permission
from django.db import transaction
import importlib
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType


class SyncPermissions:
    """
    Clase para sincronizar permisos personalizados del sistema
     - Permisos personalizados por módulo se encuentran en archvo del módulo (ejemplo: erp/permissions.py)
    """

    @classmethod
    def get_permissions_definition(cls):
        # concatenar las definiciones de permisos de los diferentes módulos si existen

        result = []
        modules = ["crm", "cms", "lms", "erp", "system"]
        modules_config = [
            (
                f"api.{module}.permissions",
                "CUSTOM_PERMISSIONS_DEFINITION",
            )
            for module in modules
        ]

        for module_path, permission_attr in modules_config:
            try:
                # Importar el módulo dinámicamente
                module = importlib.import_module(module_path)

                # Obtener la definición de permisos
                permissions_definition = getattr(module, permission_attr, [])

                if permissions_definition:
                    result.extend(permissions_definition)

            except ImportError:
                # El módulo no existe, continuar con el siguiente
                continue
            except AttributeError:
                # El atributo no existe en el módulo, continuar
                continue

        return result

    @classmethod
    @transaction.atomic
    def sync_permissions(cls, force=False, verbosity=0):
        """
        Sincronizar permisos con la base de datos

        Args:
            force (bool): Forzar actualización incluso si ya existen
            verbosity (int): Nivel de detalle en los mensajes
        """
        try:
            User = get_user_model()
            content_type = ContentType.objects.get_for_model(User)

            custom_permissions = cls.get_permissions_definition()

            if not custom_permissions:
                if verbosity > 0:
                    print("ℹ️  No custom permissions defined")
                return {"created": 0, "updated": 0, "total": 0}

            existing_permissions = Permission.objects.filter(
                content_type=content_type,
                codename__in=[perm[0] for perm in custom_permissions],
            )

            all_permissions_exist = existing_permissions.count() == len(
                custom_permissions
            )

            if not force and all_permissions_exist:
                if verbosity > 0:
                    print(
                        "⚠️  Custom permissions already exist. Use force=True to update."
                    )
                return {"created": 0, "updated": 0, "total": len(custom_permissions)}

            created_count = 0
            updated_count = 0

            for codename, name in custom_permissions:
                permission, created = Permission.objects.get_or_create(
                    content_type=content_type,
                    codename=codename,
                    defaults={"name": name},
                )

                if created:
                    created_count += 1
                    if verbosity > 1:
                        print(f"✅ Created: {name}")
                elif permission.name != name:
                    permission.name = name
                    permission.save()
                    updated_count += 1
                    if verbosity > 1:
                        print(f"🔄 Updated: {name}")

            if verbosity > 0:
                print(
                    f"✅ Permissions sync completed: {created_count} created, {updated_count} updated"
                )

            return {
                "created": created_count,
                "updated": updated_count,
                "total": len(custom_permissions),
            }

        except Exception as e:
            if verbosity > 0:
                print(f"❌ Error syncing permissions: {e}")
            raise
