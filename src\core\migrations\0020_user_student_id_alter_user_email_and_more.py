# Generated by Django 5.0.6 on 2025-08-13 03:40

from django.db import migrations, models


def assign_student_ids(apps, schema_editor):
    User = apps.get_model("core", "User")

    # Get all unique years from created_at
    years = User.objects.dates("created_at", "year")

    for year_date in years:
        year = year_date.year
        # Get users from the year ordered by created_at
        year_users = User.objects.filter(created_at__year=year).order_by("created_at")

        # Assign student_id for each user
        for index, user in enumerate(year_users, start=1):
            student_id = f"{year}{index:05d}"

            # Ensure uniqueness (shouldn't be needed but just in case)
            while User.objects.filter(student_id=student_id).exists():
                index += 1
                student_id = f"{year}{index:05d}"

            user.student_id = student_id
            user.save(update_fields=["student_id"])


def reverse_assign_student_ids(apps, schema_editor):
    # No need to do anything on reverse
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0019_eventschedule_emails_reminder_auto"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="student_id",
            field=models.CharField(
                db_index=True,
                help_text="Generated Sequential Student ID",
                max_length=9,
                unique=True,
                null=True,  # Temporarily allow null
                blank=True,  # Temporarily allow blank
            ),
        ),
        migrations.RunPython(
            assign_student_ids,
            reverse_assign_student_ids,
        ),
        migrations.AlterField(
            model_name="user",
            name="student_id",
            field=models.CharField(
                db_index=True,
                help_text="Generated Sequential Student ID",
                max_length=9,
                unique=True,
                null=False,  # Now make it required
                blank=False,  # Now make it required
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="email",
            field=models.EmailField(
                blank=True,
                db_index=True,
                help_text="Email address",
                max_length=128,
                null=True,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=models.CharField(
                blank=True,
                db_index=True,
                help_text="Phone number & country code",
                max_length=64,
                null=True,
                unique=True,
            ),
        ),
    ]
