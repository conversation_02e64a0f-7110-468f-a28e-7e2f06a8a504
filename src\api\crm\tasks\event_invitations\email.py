"""
Email invitation tasks
"""

import logging
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from core.models import EventReminder
from api.crm.services.invitations import EmailInvitationService
from django.db.models import QuerySet

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def send_bulk_email_invitations(
    self, event_schedule_id: str, enrollment_ids: list = None
):
    """
    Send email invitations for all pending reminders of an event schedule in bulk
    to avoid Google Calendar API rate limiting

    Args:
        event_schedule_id: EventSchedule UUID
        enrollment_ids: Optional list of specific enrollment IDs to process
    """
    try:
        from core.models import EventSchedule

        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        # Get event schedule
        event_schedule = EventSchedule.objects.get(esid=event_schedule_id)

        # Get pending email reminders for this event schedule
        reminders_query = EventReminder.objects.filter(
            enrollment__event_schedule=event_schedule,
            status_email=EventReminder.PENDING,
        ).select_related("enrollment__user")

        # Filter by specific enrollment IDs if provided
        if enrollment_ids:
            reminders_query = reminders_query.filter(enrollment__id__in=enrollment_ids)

        pending_reminders = reminders_query.all()

        if not pending_reminders:
            logger.info(
                f"No pending email reminders for event schedule {event_schedule_id}"
            )
            return {"success": True, "message": "No pending reminders"}

        # Get enrollments from reminders
        enrollments = [reminder.enrollment for reminder in pending_reminders]

        # Initialize service
        email_service = EmailInvitationService()

        # Send bulk invitations - Fixed: pass enrollments parameter
        bulk_result = email_service.send_bulk_invitations(event_schedule, enrollments)

        # Update reminder statuses based on results
        for result in bulk_result["results"]:
            try:
                reminder = next(
                    r
                    for r in pending_reminders
                    if r.enrollment.id == result["enrollment_id"]
                )

                if result["success"]:
                    reminder.status_email = EventReminder.SENT
                    reminder.sent_at_email = now
                    reminder.last_error_email = None
                    logger.info(
                        f"Email invitation {reminder.rid} sent successfully in bulk"
                    )
                else:
                    reminder.status_email = EventReminder.FAILED
                    reminder.last_error_email = result["message"]
                    reminder.retry_count_email += 1
                    logger.error(
                        f"Email invitation {reminder.rid} failed in bulk: {result['message']}"
                    )

                reminder.save()

            except StopIteration:
                logger.warning(
                    f"Reminder not found for enrollment {result['enrollment_id']}"
                )
                continue

        return {
            "success": bulk_result["success"],
            "message": bulk_result["message"],
            "processed_count": len(bulk_result["results"]),
        }

    except Exception as e:
        logger.error(
            f"Unexpected error in bulk email invitations {event_schedule_id}: {e}"
        )

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=300 * (2**self.request.retries))

        return {"success": False, "message": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def send_email_invitation(self, reminder_id: str):
    """
    Send email invitation for a specific reminder

    Args:
        reminder_id: EventReminder UUID
    """
    try:
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        reminder = EventReminder.objects.select_related(
            "enrollment__event_schedule", "enrollment__user"
        ).get(rid=reminder_id)

        # Check if already sent or cancelled
        if reminder.status_email not in [EventReminder.PENDING, EventReminder.RETRYING]:
            logger.info(
                f"Email invitation {reminder_id} already processed with status: {reminder.status_email}"
            )
            return

        # Update status to retrying
        reminder.status_email = EventReminder.RETRYING
        reminder.save(update_fields=["status_email"])

        # Initialize service
        email_service = EmailInvitationService()

        # Send invitation
        result = email_service.send_invitation(
            enrollment=reminder.enrollment,
        )

        # Update reminder based on result
        with transaction.atomic():
            reminder.refresh_from_db()

            if result.success:
                reminder.status_email = EventReminder.SENT
                reminder.sent_at_email = now
                reminder.last_error_email = None
                logger.info(f"Email invitation {reminder_id} sent successfully")
            else:
                reminder.status_email = EventReminder.FAILED
                reminder.last_error_email = result.message
                reminder.retry_count_email += 1

                # Schedule retry if appropriate
                if result.retry_after and reminder.retry_count_email < 3:
                    logger.info(
                        f"Scheduling email retry for {reminder_id} in {result.retry_after} seconds"
                    )
                    send_email_invitation.apply_async(
                        args=[reminder_id], countdown=result.retry_after
                    )
                    reminder.status_email = EventReminder.RETRYING

                logger.error(f"Email invitation {reminder_id} failed: {result.message}")

            reminder.save()

        return {
            "success": result.success,
            "message": result.message,
            "external_id": result.external_id,
        }

    except EventReminder.DoesNotExist:
        logger.error(f"EventReminder {reminder_id} not found")
        return {"success": False, "message": "Reminder not found"}

    except Exception as e:
        logger.error(f"Unexpected error in email invitation {reminder_id}: {e}")

        # Update reminder with error
        try:
            reminder = EventReminder.objects.get(rid=reminder_id)
            reminder.status_email = EventReminder.FAILED
            reminder.last_error_email = "Error inesperado"
            reminder.retry_count_email += 1
            reminder.save()
        except:
            pass

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2**self.request.retries))

        return {"success": False, "message": str(e)}


def schedule_email_invitations(email_reminders: QuerySet[EventReminder]):
    """
    Schedule email invitations with bulk processing when appropriate

    Args:
        email_reminders: QuerySet of EventReminder objects

    Returns:
        int: Number of scheduled invitations
    """
    scheduled_count = 0

    # Group reminders by event schedule for bulk processing
    event_schedules_with_pending_emails = {}

    for reminder in email_reminders:
        if reminder.is_ready_for_email_send():
            event_schedule_id = str(reminder.enrollment.event_schedule.esid)

            if event_schedule_id not in event_schedules_with_pending_emails:
                event_schedules_with_pending_emails[event_schedule_id] = []
            event_schedules_with_pending_emails[event_schedule_id].append(reminder)

    # Process event schedules with multiple pending emails in bulk
    for event_schedule_id, reminders in event_schedules_with_pending_emails.items():
        if len(reminders) > 1:
            # Use bulk processing for multiple reminders
            send_bulk_email_invitations.delay(event_schedule_id)
            scheduled_count += len(reminders)
            logger.info(
                f"Scheduled bulk email invitations for event schedule {event_schedule_id} ({len(reminders)} reminders)"
            )
        else:
            # Use individual processing for single reminders
            send_email_invitation.delay(str(reminders[0].rid))
            scheduled_count += 1
            logger.info(
                f"Scheduled individual email invitation for reminder {reminders[0].rid}"
            )

    return scheduled_count
