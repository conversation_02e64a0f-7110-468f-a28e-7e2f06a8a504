swagger: '2.0'
info:
  title: CEU Portals API v1.0 Documentation
  description: CEU Portals API Swagger Specification
  version: v1
host: localhost:8000
schemes:
- http
basePath: /
consumes:
- application/json
produces:
- application/json
securityDefinitions:
  Basic:
    type: basic
security:
- Basic: []
paths:
  /api/v1/auth/login:
    post:
      operationId: api_v1_auth_login
      description: Login user and return auth token
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/AuthToken'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Token'
      tags:
      - Auth
    parameters: []
  /api/v1/blog:
    get:
      operationId: api_v1_blog_list
      description: ''
      parameters:
      - name: page
        in: query
        description: A page number within the paginated result set.
        required: false
        type: integer
      responses:
        '200':
          description: ''
          schema:
            required:
            - count
            - results
            type: object
            properties:
              count:
                type: integer
              next:
                type: string
                format: uri
                x-nullable: true
              previous:
                type: string
                format: uri
                x-nullable: true
              results:
                type: array
                items:
                  $ref: '#/definitions/Blog'
      tags:
      - Blog
    post:
      operationId: api_v1_blog_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Blog'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Blog'
      tags:
      - Blog
    parameters: []
  /api/v1/blog-category:
    get:
      operationId: api_v1_blog-category_list
      description: ''
      parameters:
      - name: page
        in: query
        description: A page number within the paginated result set.
        required: false
        type: integer
      responses:
        '200':
          description: ''
          schema:
            required:
            - count
            - results
            type: object
            properties:
              count:
                type: integer
              next:
                type: string
                format: uri
                x-nullable: true
              previous:
                type: string
                format: uri
                x-nullable: true
              results:
                type: array
                items:
                  $ref: '#/definitions/BlogCategory'
      tags:
      - Blog Category
    post:
      operationId: api_v1_blog-category_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/BlogCategory'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/BlogCategory'
      tags:
      - Blog Category
    parameters: []
  /api/v1/blog-category/{id}:
    get:
      operationId: api_v1_blog-category_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/BlogCategory'
      tags:
      - Blog Category
    put:
      operationId: api_v1_blog-category_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/BlogCategory'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/BlogCategory'
      tags:
      - Blog Category
    patch:
      operationId: api_v1_blog-category_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/BlogCategory'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/BlogCategory'
      tags:
      - Blog Category
    delete:
      operationId: api_v1_blog-category_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - Blog Category
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this blog category.
      required: true
      type: integer
  /api/v1/blog/{id}:
    get:
      operationId: api_v1_blog_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Blog'
      tags:
      - Blog
    put:
      operationId: api_v1_blog_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Blog'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Blog'
      tags:
      - Blog
    patch:
      operationId: api_v1_blog_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Blog'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Blog'
      tags:
      - Blog
    delete:
      operationId: api_v1_blog_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - Blog
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this blog.
      required: true
      type: integer
  /api/v1/instructor:
    get:
      operationId: api_v1_instructor_list
      description: ''
      parameters:
      - name: page
        in: query
        description: A page number within the paginated result set.
        required: false
        type: integer
      - name: page_size
        in: query
        description: Number of results to return per page.
        required: false
        type: integer
      responses:
        '200':
          description: ''
          schema:
            required:
            - count
            - results
            type: object
            properties:
              count:
                type: integer
              next:
                type: string
                format: uri
                x-nullable: true
              previous:
                type: string
                format: uri
                x-nullable: true
              results:
                type: array
                items:
                  $ref: '#/definitions/Instructor'
      tags:
      - Instructor
    post:
      operationId: api_v1_instructor_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Instructor'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Instructor'
      tags:
      - Instructor
    parameters: []
  /api/v1/instructor/{id}:
    get:
      operationId: api_v1_instructor_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Instructor'
      tags:
      - Instructor
    put:
      operationId: api_v1_instructor_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Instructor'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Instructor'
      tags:
      - Instructor
    patch:
      operationId: api_v1_instructor_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Instructor'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Instructor'
      tags:
      - Instructor
    delete:
      operationId: api_v1_instructor_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - Instructor
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this instructor.
      required: true
      type: integer
  /api/v1/program:
    get:
      operationId: api_v1_program_list
      description: ''
      parameters:
      - name: page
        in: query
        description: A page number within the paginated result set.
        required: false
        type: integer
      responses:
        '200':
          description: ''
          schema:
            required:
            - count
            - results
            type: object
            properties:
              count:
                type: integer
              next:
                type: string
                format: uri
                x-nullable: true
              previous:
                type: string
                format: uri
                x-nullable: true
              results:
                type: array
                items:
                  $ref: '#/definitions/Program'
      tags:
      - Program
    post:
      operationId: api_v1_program_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Program'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Program'
      tags:
      - Program
    parameters: []
  /api/v1/program/{id}:
    get:
      operationId: api_v1_program_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Program'
      tags:
      - Program
    put:
      operationId: api_v1_program_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Program'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Program'
      tags:
      - Program
    patch:
      operationId: api_v1_program_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Program'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Program'
      tags:
      - Program
    delete:
      operationId: api_v1_program_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - Program
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this program.
      required: true
      type: integer
  /api/v1/testimonial:
    get:
      operationId: api_v1_testimonial_list
      description: ''
      parameters:
      - name: page
        in: query
        description: A page number within the paginated result set.
        required: false
        type: integer
      responses:
        '200':
          description: ''
          schema:
            required:
            - count
            - results
            type: object
            properties:
              count:
                type: integer
              next:
                type: string
                format: uri
                x-nullable: true
              previous:
                type: string
                format: uri
                x-nullable: true
              results:
                type: array
                items:
                  $ref: '#/definitions/Testimonial'
      tags:
      - Testimonial
    post:
      operationId: api_v1_testimonial_create
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Testimonial'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Testimonial'
      tags:
      - Testimonial
    parameters: []
  /api/v1/testimonial/upload-profile-photo:
    post:
      operationId: api_v1_testimonial_upload_profile_photo
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Testimonial'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Testimonial'
      consumes: []
      tags:
      - Testimonial
    parameters: []
  /api/v1/testimonial/{id}:
    get:
      operationId: api_v1_testimonial_read
      description: ''
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Testimonial'
      tags:
      - Testimonial
    put:
      operationId: api_v1_testimonial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Testimonial'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Testimonial'
      tags:
      - Testimonial
    patch:
      operationId: api_v1_testimonial_partial_update
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Testimonial'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/Testimonial'
      tags:
      - Testimonial
    delete:
      operationId: api_v1_testimonial_delete
      description: ''
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - Testimonial
    parameters:
    - name: id
      in: path
      required: true
      type: string
  /api/v1/testimonial/{id}/update-profile-photo:
    post:
      operationId: api_v1_testimonial_update_profile_photo
      description: ''
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/Testimonial'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/Testimonial'
      consumes: []
      tags:
      - Testimonial
    parameters:
    - name: id
      in: path
      required: true
      type: string
  /api/v1/user:
    get:
      operationId: api_v1_user_list
      description: A viewset for viewing and editing user instances.
      parameters:
      - name: page
        in: query
        description: A page number within the paginated result set.
        required: false
        type: integer
      responses:
        '200':
          description: ''
          schema:
            required:
            - count
            - results
            type: object
            properties:
              count:
                type: integer
              next:
                type: string
                format: uri
                x-nullable: true
              previous:
                type: string
                format: uri
                x-nullable: true
              results:
                type: array
                items:
                  $ref: '#/definitions/User'
      tags:
      - User
    post:
      operationId: api_v1_user_create
      description: A viewset for viewing and editing user instances.
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/User'
      responses:
        '201':
          description: ''
          schema:
            $ref: '#/definitions/User'
      tags:
      - User
    parameters: []
  /api/v1/user/{id}:
    get:
      operationId: api_v1_user_read
      description: A viewset for viewing and editing user instances.
      parameters: []
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/User'
      tags:
      - User
    put:
      operationId: api_v1_user_update
      description: A viewset for viewing and editing user instances.
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/User'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/User'
      tags:
      - User
    patch:
      operationId: api_v1_user_partial_update
      description: A viewset for viewing and editing user instances.
      parameters:
      - name: data
        in: body
        required: true
        schema:
          $ref: '#/definitions/User'
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/User'
      tags:
      - User
    delete:
      operationId: api_v1_user_delete
      description: A viewset for viewing and editing user instances.
      parameters: []
      responses:
        '204':
          description: ''
      tags:
      - User
    parameters:
    - name: id
      in: path
      description: A unique integer value identifying this user.
      required: true
      type: integer
definitions:
  AuthToken:
    required:
    - username
    - password
    type: object
    properties:
      username:
        title: Username
        type: string
        minLength: 1
      password:
        title: Password
        type: string
        minLength: 1
      token:
        title: Token
        type: string
        readOnly: true
        minLength: 1
  UserDetail:
    description: User details
    required:
    - username
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      username:
        title: Username
        description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
          only.
        type: string
        pattern: ^[\w.@+-]+$
        maxLength: 150
        minLength: 1
      email:
        title: Email address
        type: string
        format: email
        maxLength: 254
      first_name:
        title: First name
        type: string
        maxLength: 150
      last_name:
        title: Last name
        type: string
        maxLength: 150
  Token:
    required:
    - key
    type: object
    properties:
      key:
        title: Key
        description: Auth token
        type: string
        maxLength: 40
        minLength: 1
      user:
        $ref: '#/definitions/UserDetail'
  Blog:
    required:
    - title
    - content
    - author
    - category
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      created_at:
        title: Created At
        type: string
        format: date-time
        readOnly: true
      updated_at:
        title: Last Updated
        type: string
        format: date-time
        readOnly: true
      deleted:
        title: Deleted
        type: boolean
      title:
        title: Title
        type: string
        maxLength: 255
        minLength: 1
      summary:
        title: Summary
        type: string
      content:
        title: Content
        type: string
        minLength: 1
      author:
        title: Author
        type: string
        maxLength: 255
        minLength: 1
      publish_date:
        title: Publish Date
        type: string
        format: date-time
      tags:
        title: Tags
        type: string
        maxLength: 255
      cover_image_url:
        title: Cover Image URL
        type: string
        format: uri
        maxLength: 200
      view_count:
        title: View Count
        type: integer
        maximum: 2147483647
        minimum: -2147483648
      status:
        title: Status
        type: string
        enum:
        - Draft
        - Published
        - Archived
      category:
        title: Blog Category
        type: integer
  BlogCategory:
    required:
    - name
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      key:
        title: Key
        type: integer
        readOnly: true
      created_at:
        title: Created At
        type: string
        format: date-time
        readOnly: true
      updated_at:
        title: Last Updated
        type: string
        format: date-time
        readOnly: true
      deleted:
        title: Deleted
        type: boolean
      name:
        title: Name
        type: string
        maxLength: 255
        minLength: 1
      description:
        title: Description
        type: string
  Instructor:
    required:
    - first_name
    - last_name
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      key:
        title: Key
        type: integer
        readOnly: true
      profile_photo_file:
        title: Profile photo file
        type: string
        readOnly: true
        format: uri
      avatar_photo_file:
        title: Avatar photo file
        type: string
        readOnly: true
        format: uri
      profile_photo:
        title: Profile photo
        type: string
        format: uri
        readOnly: true
        minLength: 1
        x-nullable: true
      avatar_photo:
        title: Avatar photo
        type: string
        format: uri
        readOnly: true
        minLength: 1
        x-nullable: true
      created_at:
        title: Created At
        type: string
        format: date-time
        readOnly: true
      updated_at:
        title: Last Updated
        type: string
        format: date-time
        readOnly: true
      first_name:
        title: First Name
        type: string
        maxLength: 255
        minLength: 1
      last_name:
        title: Last Name
        type: string
        maxLength: 255
        minLength: 1
      biography:
        title: Biography
        type: string
      title:
        title: Title
        type: string
        maxLength: 255
      highlighted_info:
        title: Highlighted Info
        type: string
        maxLength: 255
      facebook_url:
        title: Facebook URL
        type: string
        format: uri
        maxLength: 200
      linkedin_url:
        title: LinkedIn URL
        type: string
        format: uri
        maxLength: 200
      instagram_url:
        title: Instagram URL
        type: string
        format: uri
        maxLength: 200
      status:
        title: Status
        type: string
        enum:
        - Active
        - Inactive
  Program:
    required:
    - name
    - type
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      created_at:
        title: Created At
        type: string
        format: date-time
        readOnly: true
      updated_at:
        title: Last Updated
        type: string
        format: date-time
        readOnly: true
      deleted:
        title: Deleted
        type: boolean
      name:
        title: Program Name
        type: string
        maxLength: 255
        minLength: 1
      start_date:
        title: Start Date
        type: string
        format: date
      end_date:
        title: End Date
        type: string
        format: date
      description:
        title: Description
        type: string
      duration:
        title: Duration
        type: string
        maxLength: 50
      modality:
        title: Modality
        type: string
        enum:
        - Remote
        - In-Person
      type:
        title: Type
        type: string
        enum:
        - Specialization
        - Preparation
      status:
        title: Status
        type: string
        enum:
        - Active
        - Inactive
        - Finished
  Testimonial:
    required:
    - author_name
    - content
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      key:
        title: Key
        type: integer
        readOnly: true
      created_at:
        title: Created At
        type: string
        format: date-time
        readOnly: true
      updated_at:
        title: Last Updated
        type: string
        format: date-time
        readOnly: true
      deleted:
        title: Deleted
        type: boolean
      author_name:
        title: Author Name
        type: string
        maxLength: 255
        minLength: 1
      author_title:
        title: Author Title
        type: string
        maxLength: 255
      content:
        title: Content
        type: string
        minLength: 1
      author_photo_url:
        title: Author Photo URL
        type: string
        format: uri
        maxLength: 200
      status:
        title: Status
        type: string
        enum:
        - Published
        - Unpublished
  User:
    required:
    - password
    - username
    type: object
    properties:
      id:
        title: ID
        type: integer
        readOnly: true
      password:
        title: Password
        type: string
        maxLength: 128
        minLength: 1
      last_login:
        title: Last login
        type: string
        format: date-time
        x-nullable: true
      is_superuser:
        title: Superuser status
        description: Designates that this user has all permissions without explicitly
          assigning them.
        type: boolean
      username:
        title: Username
        description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
          only.
        type: string
        pattern: ^[\w.@+-]+$
        maxLength: 150
        minLength: 1
      first_name:
        title: First name
        type: string
        maxLength: 150
      last_name:
        title: Last name
        type: string
        maxLength: 150
      email:
        title: Email address
        type: string
        format: email
        maxLength: 254
      is_staff:
        title: Staff status
        description: Designates whether the user can log into this admin site.
        type: boolean
      is_active:
        title: Active
        description: Designates whether this user should be treated as active. Unselect
          this instead of deleting accounts.
        type: boolean
      date_joined:
        title: Date joined
        type: string
        format: date-time
      created_at:
        title: Created At
        type: string
        format: date-time
        readOnly: true
      updated_at:
        title: Last Updated
        type: string
        format: date-time
        readOnly: true
      deleted:
        title: Deleted
        type: boolean
      date_of_birth:
        title: Date of birth
        type: string
        format: date
        x-nullable: true
      groups:
        description: The groups this user belongs to. A user will get all permissions
          granted to each of their groups.
        type: array
        items:
          description: The groups this user belongs to. A user will get all permissions
            granted to each of their groups.
          type: integer
        uniqueItems: true
      user_permissions:
        description: Specific permissions for this user.
        type: array
        items:
          description: Specific permissions for this user.
          type: integer
        uniqueItems: true
