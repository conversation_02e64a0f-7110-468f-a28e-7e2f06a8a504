# Generated by Django 5.0.6 on 2024-12-03 13:35

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "uid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        help_text="Email address", max_length=254, unique=True
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        help_text="Phone number & country code",
                        max_length=40,
                        null=True,
                    ),
                ),
                (
                    "id_number",
                    models.CharField(
                        blank=True,
                        help_text="ID number or passport number",
                        max_length=40,
                        null=True,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="BlogCategory",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "bcid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Blog Categories",
            },
        ),
        migrations.CreateModel(
            name="Blog",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "bid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "slug",
                    models.SlugField(max_length=255, unique=True, verbose_name="Slug"),
                ),
                ("title", models.CharField(max_length=255, verbose_name="Title")),
                ("summary", models.TextField(blank=True, verbose_name="Summary")),
                ("content", models.TextField(verbose_name="Content")),
                ("author", models.CharField(max_length=255, verbose_name="Author")),
                (
                    "publish_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Publish Date"
                    ),
                ),
                (
                    "tags",
                    models.CharField(
                        blank=True, default="", max_length=255, verbose_name="Tags"
                    ),
                ),
                (
                    "cover_image_url",
                    models.URLField(blank=True, verbose_name="Cover Image URL"),
                ),
                (
                    "view_count",
                    models.IntegerField(default=0, verbose_name="View Count"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Draft", "Draft"),
                            ("Published", "Published"),
                            ("Archived", "Archived"),
                        ],
                        default="Draft",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="blogs",
                        to="core.blogcategory",
                        verbose_name="Blog Category",
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog",
                "verbose_name_plural": "Blogs",
            },
        ),
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "fid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the file",
                        primary_key=True,
                        serialize=False,
                        verbose_name="File ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, help_text="Name of the file", max_length=255
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, help_text="Description of the file"),
                ),
                (
                    "bucket_name",
                    models.CharField(
                        help_text="Bucket name of the file", max_length=255
                    ),
                ),
                (
                    "object_name",
                    models.CharField(
                        help_text="Object name of the file", max_length=255
                    ),
                ),
                (
                    "is_private",
                    models.BooleanField(default=False, help_text="Is the file private"),
                ),
                (
                    "width",
                    models.IntegerField(
                        blank=True, help_text="Width of the image file", null=True
                    ),
                ),
                (
                    "height",
                    models.IntegerField(
                        blank=True, help_text="Height of the image", null=True
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name": "File",
                "verbose_name_plural": "Files",
            },
        ),
        migrations.AddField(
            model_name="user",
            name="profile_photo",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="users",
                to="core.file",
                verbose_name="Profile Photo",
            ),
        ),
        migrations.CreateModel(
            name="Instructor",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "order",
                    models.IntegerField(
                        default=0,
                        help_text="Order in the Website",
                        verbose_name="Order",
                    ),
                ),
                (
                    "iid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "full_name",
                    models.CharField(max_length=255, verbose_name="Full Name"),
                ),
                ("biography", models.TextField(blank=True, verbose_name="Biography")),
                (
                    "title",
                    models.CharField(blank=True, max_length=255, verbose_name="Title"),
                ),
                (
                    "highlighted_info",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="Highlighted Info"
                    ),
                ),
                (
                    "facebook_url",
                    models.URLField(blank=True, verbose_name="Facebook URL"),
                ),
                (
                    "linkedin_url",
                    models.URLField(blank=True, verbose_name="LinkedIn URL"),
                ),
                (
                    "instagram_url",
                    models.URLField(blank=True, verbose_name="Instagram URL"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("Published", "Published"), ("Draft", "Draft")],
                        default="Draft",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "profile_photo",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="instructors",
                        to="core.file",
                        verbose_name="Profile Photo",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="instructor",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Offering",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "oid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "slug",
                    models.SlugField(max_length=255, unique=True, verbose_name="Slug"),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Program Name")),
                (
                    "start_date",
                    models.DateField(auto_now_add=True, verbose_name="Start Date"),
                ),
                (
                    "end_date",
                    models.DateField(auto_now_add=True, verbose_name="End Date"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "duration",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        verbose_name="Duration (e.g. 3 months, 6 weeks, etc.)",
                    ),
                ),
                (
                    "frecuency",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        verbose_name="Frecuency: (e.g. Weekly, Monthly, etc.)",
                    ),
                ),
                ("hours", models.IntegerField(blank=True, verbose_name="Hours")),
                (
                    "schedule",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        verbose_name="Schedule. (e.g. 8:00 am - 10:00 am)",
                    ),
                ),
                (
                    "modality",
                    models.CharField(
                        choices=[("REMOTE", "Remote"), ("IN_PERSON", "In-Person")],
                        default="REMOTE",
                        max_length=20,
                        verbose_name="Modality",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("SPECIALIZATION", "Specialization"),
                            ("PREPARATION", "Preparation"),
                            ("REVIEW_WORKSHOP", "Review Workshop"),
                        ],
                        default="SPECIALIZATION",
                        max_length=20,
                        verbose_name="Type",
                    ),
                ),
                (
                    "stage",
                    models.CharField(
                        choices=[
                            ("PLANNING", "Planning"),
                            ("LAUNCHED", "Launched"),
                            ("ENROLLMENT", "Enrollment"),
                            ("ENROLLMENT_CLOSED", "Enrollment Closed"),
                            ("FINISHED", "Finished"),
                        ],
                        default="PLANNING",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "format",
                    models.CharField(
                        choices=[("LIVE", "Live"), ("ASYNCHRONOUS", "Asynchronous")],
                        default="LIVE",
                        max_length=20,
                        verbose_name="Format",
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Base Price"
                    ),
                ),
                (
                    "discount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        verbose_name="Discount percentage",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "thumbnail",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="programs",
                        to="core.file",
                        verbose_name="Thumbnail",
                    ),
                ),
            ],
            options={
                "verbose_name": "Academic Offering",
                "verbose_name_plural": "Academic Offerings",
            },
        ),
        migrations.CreateModel(
            name="Event",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Workshop Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Start Date"
                    ),
                ),
                (
                    "end_date",
                    models.DateTimeField(
                        blank=True,
                        default=django.utils.timezone.now,
                        verbose_name="End Date",
                    ),
                ),
                (
                    "duration",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="Duration"
                    ),
                ),
                (
                    "stage",
                    models.CharField(
                        choices=[
                            ("PLANNING", "Planning"),
                            ("LAUNCHED", "Launched"),
                            ("ENROLLMENT", "Enrollment"),
                            ("ENROLLMENT_CLOSED", "Enrollment Closed"),
                            ("FINISHED", "Finished"),
                        ],
                        default="PLANNING",
                        max_length=24,
                        verbose_name="Stage",
                    ),
                ),
                (
                    "modality",
                    models.CharField(
                        choices=[("REMOTE", "Remote"), ("IN_PERSON", "In-Person")],
                        default="REMOTE",
                        max_length=24,
                        verbose_name="Modality",
                    ),
                ),
                (
                    "format",
                    models.CharField(
                        choices=[("LIVE", "Live"), ("ASYNCHRONOUS", "Asynchronous")],
                        default="LIVE",
                        max_length=24,
                        verbose_name="Format",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("WORKSHOP", "Workshop"),
                            ("WEBINAR", "Webinar"),
                            ("HANDS_OF_WORKSHOP", "Hands-on Workshop"),
                        ],
                        default="WORKSHOP",
                        max_length=24,
                        verbose_name="Type",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Price",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "thumbnail",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="events",
                        to="core.file",
                        verbose_name="Thumbnail",
                    ),
                ),
                (
                    "instructor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="events",
                        to="core.instructor",
                        verbose_name="Instructor",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="events",
                        to="core.offering",
                        verbose_name="Offering",
                    ),
                ),
            ],
            options={
                "verbose_name": "Event",
                "verbose_name_plural": "Events",
            },
        ),
        migrations.CreateModel(
            name="Order",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "oid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("PAID", "Paid"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PENDING",
                        max_length=50,
                    ),
                ),
                (
                    "mp_payment_id",
                    models.CharField(
                        blank=True,
                        max_length=128,
                        null=True,
                        verbose_name="MP Payment ID",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="orders",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Owner",
                    ),
                ),
            ],
            options={
                "verbose_name": "Order",
                "verbose_name_plural": "Orders",
            },
        ),
        migrations.CreateModel(
            name="Student",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "sid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "ocupation",
                    models.CharField(
                        blank=True,
                        help_text="Current ocupation",
                        max_length=40,
                        null=True,
                    ),
                ),
                (
                    "major",
                    models.CharField(
                        blank=True,
                        help_text="Major or field of study",
                        max_length=40,
                        null=True,
                    ),
                ),
                (
                    "term",
                    models.CharField(
                        blank=True,
                        help_text="Current term or semester",
                        max_length=40,
                        null=True,
                    ),
                ),
                (
                    "university",
                    models.CharField(
                        blank=True,
                        help_text="University or institution",
                        max_length=40,
                        null=True,
                    ),
                ),
                (
                    "date_of_birth",
                    models.DateField(blank=True, help_text="Date of birth", null=True),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student",
                "verbose_name_plural": "Students",
            },
        ),
        migrations.CreateModel(
            name="Testimonial",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "order",
                    models.IntegerField(
                        default=0,
                        help_text="Order in the Website",
                        verbose_name="Order",
                    ),
                ),
                (
                    "tid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "author_name",
                    models.CharField(max_length=255, verbose_name="Author Name"),
                ),
                (
                    "author_title",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="Author Title"
                    ),
                ),
                ("content", models.TextField(verbose_name="Content")),
                (
                    "status",
                    models.CharField(
                        choices=[("Published", "Published"), ("Draft", "Draft")],
                        default="Draft",
                        max_length=12,
                        verbose_name="Status",
                    ),
                ),
                (
                    "author_photo",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="testimonials",
                        to="core.file",
                        verbose_name="Author Photo",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Testimonial",
                "verbose_name_plural": "Testimonials",
            },
        ),
        migrations.CreateModel(
            name="OrderItem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "oiid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "quantity",
                    models.PositiveIntegerField(default=1, verbose_name="Quantity"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "offering",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="order_items",
                        to="core.offering",
                        verbose_name="Offering",
                    ),
                ),
                (
                    "order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="core.order",
                        verbose_name="Order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Order Item",
                "verbose_name_plural": "Order Items",
                "unique_together": {("order", "offering")},
            },
        ),
    ]
