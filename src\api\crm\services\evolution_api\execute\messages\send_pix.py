"""
Send PIX payment message via Evolution API
"""

import logging
from typing import Dict, Any, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

PixKeyType = Literal["cpf", "cnpj", "email", "phone", "random"]


def send_pix(
    instance_name: str, remote_jid: str, name: str, key_type: PixKeyType, key: str
) -> Dict[str, Any]:
    """
    Send PIX payment message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        name: Name of the PIX recipient
        key_type: Type of PIX key (cpf, cnpj, email, phone, random)
        key: PIX key value

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If PIX parameters are invalid
    """
    try:
        # Validate PIX key type
        valid_key_types = ["cpf", "cnpj", "email", "phone", "random"]
        if key_type not in valid_key_types:
            raise ValueError(f"Invalid PIX key type. Must be one of: {valid_key_types}")

        # Build request body
        body = {
            "number": remote_jid,
            "buttons": [
                {"type": "pix", "pix": {"name": name, "keyType": key_type, "key": key}}
            ],
        }

        # Make the request
        response = evolution_request(
            uri=f"/message/sendPix/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"PIX message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send PIX message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid PIX parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending PIX message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
