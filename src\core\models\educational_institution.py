import uuid
import shortuuid
from django.db import models
from .base import AuditBaseModel
from django.utils.translation import gettext_lazy as _


class EducationalInstitution(AuditBaseModel):
    UNIVERSITY_PUBLIC = "university_public"
    UNIVERSITY_PRIVATE = "university_private"
    INSTITUTE_PUBLIC = "institute_public"
    INSTITUTE_PRIVATE = "institute_private"
    COLLEGE_PUBLIC = "college_public"
    COLLEGE_PRIVATE = "college_private"

    INSTITUTION_TYPE_CHOICES = [
        (UNIVERSITY_PUBLIC, "Public University"),
        (UNIVERSITY_PRIVATE, "Private University"),
        (INSTITUTE_PUBLIC, "Public Institute"),
        (INSTITUTE_PRIVATE, "Private Institute"),
        (COLLEGE_PUBLIC, "Public College"),
        (COLLEGE_PRIVATE, "Private College"),
    ]

    eiid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    name = models.CharField(
        max_length=128,
        unique=True,
        help_text=_("Name of the educational institution"),
    )

    country = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        help_text=_("Country where the institution is located"),
    )

    region = models.CharField(
        max_length=128,
        null=True,
        blank=True,
        help_text=_("Region where the institution is located"),
    )

    city = models.CharField(
        max_length=128,
        null=True,
        blank=True,
        help_text=_("City where the institution is located"),
    )

    acronym = models.CharField(
        max_length=32,
        null=True,
        blank=True,
        help_text=_("Acronym of the institution"),
    )

    institution_type = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        choices=INSTITUTION_TYPE_CHOICES,
        help_text=_("Type of educational institution"),
    )

    class Meta:
        verbose_name = "Educational Institution"
        verbose_name_plural = "Educational Institutions"


class Partnership(AuditBaseModel):
    """Partnership model for educational institutions"""

    pid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_("Unique identifier for the partnership"),
    )

    short_pid = models.CharField(
        max_length=8,
        unique=True,
        blank=False,
        null=False,
        editable=False,
        verbose_name="Short Partnership ID",
        help_text=_("Short unique identifier for URLs and display"),
    )

    name = models.CharField(
        max_length=128,
        help_text=_("Name of the partnership"),
    )

    long_name = models.CharField(
        max_length=256,
        null=True,
        blank=True,
        help_text=_("Long name of the partnership"),
    )

    description = models.TextField(
        null=True,
        blank=True,
        help_text=_("Description of the partnership"),
    )

    institution = models.ForeignKey(
        EducationalInstitution,
        on_delete=models.CASCADE,
        related_name="partnerships",
        help_text=_("Educational institution involved in the partnership"),
    )
    delegate = models.ForeignKey(
        "User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="partnerships",
        help_text=_("User who is the delegate for the partnership"),
    )

    def save(self, *args, **kwargs):
        if not self.short_pid:
            # Generate a unique short_pid of 8 characters
            # Use an alphabet without confusing characters (0, O, 1, I, l)
            shortuuid.set_alphabet("23456789abcdefghjkmnpqrstuvwxyz")
            self.short_pid = shortuuid.ShortUUID().random(length=8)

            # Check uniqueness and regenerate if necessary
            while Partnership.objects.filter(short_pid=self.short_pid).exists():
                self.short_pid = shortuuid.ShortUUID().random(length=8)

        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Partnership"
        verbose_name_plural = "Partnerships"
        indexes = [
            models.Index(fields=["short_pid"], name="partnership_short_pid_idx"),
        ]
