from rest_framework import viewsets
from core.models import Instructor
from api.mixins import SwaggerTagMixin
from api.website.serializers.instructor import (
    WebsiteInstructorSerializer,
)
from api.paginations import StandardResultsPagination


class WebsiteInstructorViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = Instructor
    queryset = Instructor.objects.filter(
        deleted=False, status=Instructor.PUBLISHED_STATUS
    )
    serializer_class = WebsiteInstructorSerializer
    pagination_class = StandardResultsPagination

    swagger_tags = ["Instructor"]
