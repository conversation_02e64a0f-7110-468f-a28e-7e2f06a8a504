from django_filters import rest_framework as filters
from core.models import Offering


class WebsiteOfferingFilter(filters.FilterSet):
    modality = filters.MultipleChoiceFilter(
        choices=Offering.MODALITY_CHOICES,
        field_name="modality",
    )
    type = filters.MultipleChoiceFilter(
        choices=Offering.TYPE_CHOICES,
        field_name="type",
    )
    format = filters.MultipleChoiceFilter(
        choices=Offering.FORMAT_CHOICES,
        field_name="format",
    )

    class Meta:
        model = Offering
        fields = [
            "modality",
            "type",
            "format",
        ]
