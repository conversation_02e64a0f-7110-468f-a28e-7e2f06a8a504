"""
Update group participants via Evolution API
"""

import logging
from typing import Dict, Any, List, Union, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

ParticipantAction = Literal["add", "remove", "promote", "demote"]


def update_participants(
    instance_name: str,
    group_jid: str,
    action: ParticipantAction,
    participants: Union[List[str], str],
) -> Dict[str, Any]:
    """
    Update group participants via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        group_jid: Group JID identifier
        action: Action to perform (add, remove, promote, demote)
        participants: List of phone numbers or comma-separated string

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If parameters are invalid
    """
    try:
        # Validate action
        valid_actions = ["add", "remove", "promote", "demote"]
        if action not in valid_actions:
            raise ValueError(f"Invalid action. Must be one of: {valid_actions}")

        # Handle participants input
        if isinstance(participants, str):
            participants_list = [
                p.strip() for p in participants.split(",") if p.strip()
            ]
        else:
            participants_list = participants

        if not participants_list:
            raise ValueError("At least one participant is required")

        # Build request body
        body = {
            "action": action,
            "participants": participants_list,
        }

        # Make the request
        response = evolution_request(
            uri=f"/group/updateParticipant/{instance_name}?groupJid={group_jid}",
            method="POST",
            data=body,
        )

        logger.info(
            f"Group participants updated successfully in group {group_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to update group participants: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for update_participants: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error updating group participants: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
