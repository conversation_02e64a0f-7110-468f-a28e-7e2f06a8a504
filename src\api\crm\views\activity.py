from core.models import Activity
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from api.crm.serializers.activity import (
    CrmActivityBaseSerializer,
    CrmActivityListItemSerializer,
    CrmActivityRetrieveSerializer,
    CrmActivityCreateSerializer,
    CrmActivityUpdateSerializer,
)
from api.crm.filters.activity import CrmActivity<PERSON>ilter
from api.mixins import AuditMixin, SwaggerTagMixin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser, IsOwnerOrReadOnly
from django.utils import timezone
from rest_framework.permissions import DjangoModelPermissions


class CrmActivityViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing activities in the CRM system.

    Provides CRUD operations for activities with support for:
    - Filtering by status, order, responsible user, and deadline status
    - Search across title and description
    - Ordering by various fields
    - Pagination
    - Custom actions for bulk operations
    """

    model_class = Activity
    queryset = Activity.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmActivityBaseSerializer

    authentication_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated & IsStaffUser & IsOwnerOrReadOnly & DjangoModelPermissions
    ]
    pagination_class = StandardResultsPagination

    filterset_class = CrmActivityFilter
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = [
        "created_at",
        "updated_at",
        "deadline",
        "title",
        "status",
    ]
    filterset_fields = [
        "status",
        "order",
        "responsible",
        "overdue",
        "has_deadline",
        "deadline_from",
        "deadline_to",
        "deadline_date",
        "deadline_year",
        "deadline_month",
        "deadline_day",
    ]
    search_fields = [
        "title",
        "description",
        "responsible__first_name",
        "responsible__last_name",
        "responsible__email",
        "order__owner__first_name",
        "order__owner__last_name",
        "order__owner__email",
    ]

    swagger_tags = ["Activities"]

    def get_serializer(self, *args, **kwargs):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return CrmActivityCreateSerializer(*args, **kwargs)
        elif self.action == "update" or self.action == "partial_update":
            return CrmActivityUpdateSerializer(*args, **kwargs)
        elif self.action == "retrieve":
            return CrmActivityRetrieveSerializer(*args, **kwargs)
        elif self.action == "list":
            return CrmActivityListItemSerializer(*args, **kwargs)
        return CrmActivityBaseSerializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Create a new activity"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmActivityRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(
        operation_description="Mark activity as completed",
        responses={
            200: CrmActivityRetrieveSerializer,
            404: openapi.Response(
                description="Activity not found",
                examples={"application/json": {"detail": "Not found."}},
            ),
        },
    )
    @action(detail=True, methods=["POST"], url_path="mark-completed")
    def mark_completed(self, request, pk=None):
        """Mark an activity as completed"""
        activity = self.get_object()
        activity.status = Activity.COMPLETED_STATUS
        activity.save()

        serializer = CrmActivityRetrieveSerializer(activity)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description="Mark activity as in progress",
        responses={
            200: CrmActivityRetrieveSerializer,
            404: openapi.Response(
                description="Activity not found",
                examples={"application/json": {"detail": "Not found."}},
            ),
        },
    )
    @action(detail=True, methods=["POST"], url_path="mark-in-progress")
    def mark_in_progress(self, request, pk=None):
        """Mark an activity as in progress"""
        activity = self.get_object()
        activity.status = Activity.IN_PROGRESS_STATUS
        activity.save()

        serializer = CrmActivityRetrieveSerializer(activity)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description="Mark activity as pending",
        responses={
            200: CrmActivityRetrieveSerializer,
            404: openapi.Response(
                description="Activity not found",
                examples={"application/json": {"detail": "Not found."}},
            ),
        },
    )
    @action(detail=True, methods=["POST"], url_path="mark-pending")
    def mark_pending(self, request, pk=None):
        """Mark an activity as pending"""
        activity = self.get_object()
        activity.status = Activity.PENDING_STATUS
        activity.save()

        serializer = CrmActivityRetrieveSerializer(activity)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description="Get overdue activities",
        responses={
            200: CrmActivityListItemSerializer(many=True),
        },
    )
    @action(detail=False, methods=["GET"], url_path="overdue")
    def get_overdue(self, request):
        """Get all overdue activities"""
        overdue_activities = self.get_queryset().filter(
            deadline__lt=timezone.now(),
            status__in=[Activity.PENDING_STATUS, Activity.IN_PROGRESS_STATUS],
        )

        page = self.paginate_queryset(overdue_activities)
        if page is not None:
            serializer = CrmActivityListItemSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = CrmActivityListItemSerializer(overdue_activities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description="Get activities statistics",
        responses={
            200: openapi.Response(
                description="Activities statistics",
                examples={
                    "application/json": {
                        "total": 100,
                        "completed": 60,
                        "pending": 25,
                        "in_progress": 15,
                        "overdue": 5,
                        "completion_rate": 60.0,
                    }
                },
            ),
        },
    )
    @action(detail=False, methods=["GET"], url_path="statistics")
    def get_statistics(self, request):
        """Get activities statistics"""
        queryset = self.get_queryset()

        total = queryset.count()
        completed = queryset.filter(status=Activity.COMPLETED_STATUS).count()
        pending = queryset.filter(status=Activity.PENDING_STATUS).count()
        in_progress = queryset.filter(status=Activity.IN_PROGRESS_STATUS).count()

        overdue = queryset.filter(
            deadline__lt=timezone.now(),
            status__in=[Activity.PENDING_STATUS, Activity.IN_PROGRESS_STATUS],
        ).count()

        completion_rate = (completed / total * 100) if total > 0 else 0

        statistics = {
            "total": total,
            "completed": completed,
            "pending": pending,
            "in_progress": in_progress,
            "overdue": overdue,
            "completion_rate": round(completion_rate, 2),
        }

        return Response(statistics, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_description="Bulk update activity status",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["activity_ids", "status"],
            properties={
                "activity_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING, format="uuid"),
                    description="List of activity IDs to update",
                ),
                "status": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=[choice[0] for choice in Activity.STATUS_CHOICES],
                    description="New status for the activities",
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Bulk update successful",
                examples={
                    "application/json": {
                        "updated_count": 5,
                        "message": "Successfully updated 5 activities",
                    }
                },
            ),
            400: openapi.Response(
                description="Bad Request",
                examples={
                    "application/json": {"error": "Invalid status or activity IDs"}
                },
            ),
        },
    )
    @action(detail=False, methods=["POST"], url_path="bulk-update-status")
    def bulk_update_status(self, request):
        """Bulk update status for multiple activities"""
        activity_ids = request.data.get("activity_ids", [])
        new_status = request.data.get("status")

        if not activity_ids:
            return Response(
                {"error": "activity_ids is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not new_status:
            return Response(
                {"error": "status is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        valid_statuses = [choice[0] for choice in Activity.STATUS_CHOICES]
        if new_status not in valid_statuses:
            return Response(
                {
                    "error": (
                        f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Update activities
        updated_count = (
            self.get_queryset()
            .filter(aid__in=activity_ids)
            .update(status=new_status, updated_at=timezone.now())
        )

        return Response(
            {
                "updated_count": updated_count,
                "message": f"Successfully updated {updated_count} activities",
            },
            status=status.HTTP_200_OK,
        )
