"""
Send poll message via Evolution API
"""

import logging
from typing import Dict, Any, List, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def send_poll(
    instance_name: str,
    remote_jid: str,
    poll_title: str,
    options: List[str],
    delay: Optional[int] = None,
    quoted_message_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Send poll message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        poll_title: Title/question of the poll
        options: List of poll options (strings)
        delay: Delay in milliseconds before sending
        quoted_message_id: ID of message to quote/reply to

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If poll options are invalid
    """
    try:
        # Validate poll options
        if not options or not isinstance(options, list) or len(options) < 1:
            raise ValueError("Poll must have at least one option")

        # Build request body
        body = {
            "number": remote_jid,
            "pollMessage": {
                "name": poll_title,
                "options": options,
                "selectableCount": 1,  # Single choice poll
            },
        }

        # Add optional delay
        if delay is not None:
            body["delay"] = delay

        # Add quoted message
        if quoted_message_id:
            body["quoted"] = {
                "key": {
                    "id": quoted_message_id,
                }
            }

        # Make the request
        response = evolution_request(
            uri=f"/message/sendPoll/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Poll message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send poll message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid poll parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending poll message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
