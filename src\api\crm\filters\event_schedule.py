from django_filters import rest_framework as filters
from core.models import EventSchedule
from django.db.models import Q, Value, CharField
from django.db.models.functions import Concat


class CrmEventScheduleFilter(filters.FilterSet):
    SPECIFIC = "specific"
    INSTITUTION = "institution"

    PARTNERSHIP_TYPE_CHOICES = [
        (SPECIFIC, "Specific"),
        (INSTITUTION, "Institution"),
    ]

    stage = filters.CharFilter(method="filter_by_stage")
    is_general = filters.BooleanFilter(field_name="is_general")

    partnership_filter_type = filters.ChoiceFilter(
        method="filter_by_partnerships",
        help_text="Filter type for partnerships by specific allianses or by institution",
        choices=PARTNERSHIP_TYPE_CHOICES,
    )

    partnerships = filters.CharFilter(
        method="filter_by_partnerships",
        help_text="Filter by partnerships (comma-separated list of UUIDs)",
    )

    instructor = filters.UUIDFilter(field_name="instructor")
    event = filters.UUIDFilter(field_name="event")
    modality = filters.Char<PERSON>ilter(field_name="modality")

    search = filters.Char<PERSON>ilter(
        method="filter_search",
        help_text="Search by event schedule name, description, event name, or instructor name.",
    )

    start_date = filters.DateFilter(
        method="filter_by_date_range",
        help_text="Start date for filtering (YYYY-MM-DD format). Must be used with end_date and filter_date_by.",
    )

    end_date = filters.DateFilter(
        method="filter_by_date_range",
        help_text="End date for filtering (YYYY-MM-DD format). Must be used with start_date and filter_date_by.",
    )

    filter_date_by = filters.CharFilter(
        method="filter_by_date_range",
        help_text="Comma-separated list of date fields to filter by. "
        "Available options: created_at,scheduled_date,start_date,end_date,scheduled_datetime_email,scheduled_datetime_whatsapp. "
        "Note: 'scheduled_date' filters by event schedule start/end date range overlap.",
    )

    class Meta:
        model = EventSchedule
        fields = [
            "stage",
            "is_general",
            "partnerships",
            "instructor",
            "event",
            "modality",
            "search",
            "start_date",
            "end_date",
            "filter_date_by",
        ]

    def filter_by_stage(self, queryset, name, value):
        """
        Permite filtrar por uno o varios stages:
        - stage=planning
        - stage=launched
        - stage=planning,launched,enrollment_closed (múltiples)
        """
        if not value:
            return queryset

        stages = [stage.strip() for stage in value.split(",")]
        return queryset.filter(stage__in=stages)

    def filter_by_partnerships(self, queryset, name, value):
        """
        Filtra por uno o varios partnerships (UUIDs separados por coma)
        - partnerships=uuid1
        - partnerships=uuid1,uuid2,uuid3
        """
        params = self.request.GET
        value = params.get("partnerships", None)

        if not value:
            return queryset

        filter_type = params.get("partnership_filter_type", self.SPECIFIC)

        if filter_type == self.INSTITUTION:
            # Filter by partnership__institution__eiid
            institution_ids = [pid.strip() for pid in value.split(",")]
            return queryset.filter(
                partnerships__institution__in=institution_ids
            ).distinct()

        partnership_ids = [pid.strip() for pid in value.split(",")]
        return queryset.filter(partnerships__in=partnership_ids).distinct()

    def filter_search(self, queryset, name, value):
        """
        Search by event schedule name, description, event name, or instructor name.
        """
        if not value:
            return queryset

        return queryset.filter(
            Q(name__icontains=value)
            | Q(description__icontains=value)
            | Q(event__name__icontains=value)
            | Q(event__description__icontains=value)
            | Q(location__icontains=value)
        ).distinct()

    def filter_by_date_range(self, queryset, name, value):
        """
        Filter event schedules by date range for specific date fields.
        Requires start_date, end_date, and filter_date_by parameters.

        Example: ?start_date=2025-08-05&end_date=2025-08-06&filter_date_by=created_at,scheduled_date
        """
        # Get all filter parameters from the request
        params = self.request.GET
        start_date = params.get("start_date")
        end_date = params.get("end_date")
        filter_date_by = params.get("filter_date_by")

        # All three parameters are required for date filtering
        if not all([start_date, end_date, filter_date_by]):
            return queryset

        if filter_date_by == "created_at":
            return queryset.filter(
                created_at__date__gte=start_date, created_at__date__lte=end_date
            )

        elif filter_date_by == "scheduled_date":
            return queryset.filter(
                Q(start_date__date__gte=start_date, start_date__date__lte=end_date)
                | Q(end_date__date__gte=start_date, end_date__date__lte=end_date)
            )

        return queryset
