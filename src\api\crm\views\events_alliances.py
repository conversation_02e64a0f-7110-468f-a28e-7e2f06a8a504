from rest_framework.views import APIView
from rest_framework.response import Response
from api.crm.services.events_alliances import (
    get_event_registrations_alliances,
    get_events_alliances,
    get_current_events_alliances,
)


class GetEventsAlliancesView(APIView):
    def get(self, request):
        events = get_events_alliances()
        return Response(events)


class GetCurrentEventsAlliancesView(APIView):
    def get(self, request):
        events = get_current_events_alliances()
        return Response(events)


class GetEventRegistrationsAlliancesView(APIView):
    def get(self, request, event_id):
        if not event_id:
            return []
        event_registrations = get_event_registrations_alliances(event_id)
        return Response(event_registrations)
