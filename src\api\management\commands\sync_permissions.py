from django.core.management.base import BaseCommand
from api.management.sync_custom_permissions import SyncPermissions


class Command(BaseCommand):
    help = "Sync custom permissions for all modules"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force sync even if permissions already exist",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes",
        )

    def handle(self, *args, **options):
        if options["dry_run"]:
            self.stdout.write("🔍 DRY RUN - No changes will be made")

        try:
            # Mostrar permisos que se van a sincronizar
            permissions = SyncPermissions.get_permissions_definition()
            self.stdout.write(f"📋 Found {len(permissions)} permissions to sync")

            if options["dry_run"]:
                for codename, name in permissions:
                    self.stdout.write(f"  - {codename}: {name}")
                return

            result = SyncPermissions.sync_permissions(
                force=options["force"],
                verbosity=options.get("verbosity", 1),
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Permissions synced successfully: "
                    f'{result["created"]} created, {result["updated"]} updated '
                    f'({result["total"]} total)'
                )
            )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error syncing permissions: {e}"))
            raise
