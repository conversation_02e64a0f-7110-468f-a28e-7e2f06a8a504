from rest_framework import routers
from api.website.views import payment as payment_views
from api.website.views import instructor as instructor_views
from api.website.views import offering as offering_views
from api.website.views import testimonial as testimonial_views
from api.website.views import auth as auth_views
from api.website.views import blog as blog_views
from api.website.views import blog_category as blog_category_views
from api.website.views import blog_tag as blog_tag_views
from api.website.views import event as event_views


router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"payments",
    payment_views.PaymentViewSet,
    basename="website-payments",
)

router.register(
    r"instructors",
    instructor_views.WebsiteInstructorViewSet,
    basename="website-instructors",
)
router.register(
    r"testimonials",
    testimonial_views.WebsiteTestimonialViewSet,
    basename="website-testimonials",
)

router.register(
    r"offerings",
    offering_views.WebsiteOfferingViewSet,
    basename="website-offerings",
)

router.register(
    r"auth",
    auth_views.WebsiteAuthViewSet,
    basename="website-auth",
)

router.register(
    r"blogs",
    blog_views.WebsiteBlogPostViewSet,
    basename="website-blogs",
)

router.register(
    r"blog/categories",
    blog_category_views.WebsiteBlogCategoryViewSet,
    basename="website-blog-categories",
)

router.register(
    r"blog/tags",
    blog_tag_views.WebsiteBlogTagViewSet,
    basename="website-blog-tags",
)
router.register(
    r"events",
    event_views.WebsiteEventViewSet,
    basename="website-events",
)

urlpatterns = router.urls
