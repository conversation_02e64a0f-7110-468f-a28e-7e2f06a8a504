"""
Configure instance settings via Evolution API
"""

import logging
from typing import Dict, Any, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def instance_settings(
    instance_name: str,
    reject_call: bool = False,
    msg_call: Optional[str] = None,
    groups_ignore: bool = False,
    always_online: bool = False,
    read_messages: bool = False,
    sync_full_history: bool = False,
    read_status: bool = False
) -> Dict[str, Any]:
    """
    Configure settings for a WhatsApp instance
    
    Args:
        instance_name: Name of the instance
        reject_call: Whether to reject incoming calls
        msg_call: Message to send when rejecting calls
        groups_ignore: Whether to ignore group messages
        always_online: Whether to always appear online
        read_messages: Whether to automatically read messages
        sync_full_history: Whether to sync full message history
        read_status: Whether to read status messages
        
    Returns:
        Dict containing the API response
        
    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "rejectCall": reject_call,
            "msgCall": msg_call or "",
            "groupsIgnore": groups_ignore,
            "alwaysOnline": always_online,
            "readMessages": read_messages,
            "syncFullHistory": sync_full_history,
            "readStatus": read_status,
        }
        
        # If reject_call is enabled, ensure msg_call is set
        if reject_call:
            body["msgCall"] = msg_call or ""
        
        # Make the request
        response = evolution_request(
            uri=f"/settings/set/{instance_name}",
            method="POST",
            data=body
        )
        
        logger.info(f"Settings configured successfully for instance '{instance_name}'")
        return response
        
    except EvolutionAPIError as e:
        logger.error(f"Failed to configure settings for instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error configuring instance settings: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
