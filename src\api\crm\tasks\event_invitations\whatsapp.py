"""
WhatsApp invitation tasks
"""

import logging
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from core.models import EventReminder
from api.crm.services.invitations import WhatsAppInvitationService
from .utils import (
    get_random_delay,
    get_whatsapp_delay_range,
    calculate_delay_since_last_sent,
)
from django.db.models import QuerySet

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def send_whatsapp_invitation(self, reminder_id: str):
    """
    Send WhatsApp invitation for a specific reminder

    Args:
        reminder_id: EventReminder UUID
    """
    try:
        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)
        reminder = EventReminder.objects.select_related(
            "enrollment__event_schedule",
            "enrollment__user",
            "enrollment__event_schedule__whatsapp_template",
        ).get(rid=reminder_id)

        # Check if already sent or cancelled
        if reminder.status_whatsapp not in [
            EventReminder.PENDING,
            EventReminder.RETRYING,
        ]:
            logger.info(
                f"WhatsApp invitation {reminder_id} already processed with status: {reminder.status_whatsapp}"
            )
            return

        # Update status to retrying
        reminder.status_whatsapp = EventReminder.RETRYING
        reminder.save(update_fields=["status_whatsapp"])

        # Initialize service
        whatsapp_service = WhatsAppInvitationService(
            reminder.enrollment.event_schedule.whatsapp_template
        )

        # Send invitation
        result = whatsapp_service.send_invitation(
            enrollment=reminder.enrollment,
            template=reminder.enrollment.event_schedule.whatsapp_template,
            variables=None,  # Variables are no longer stored in reminder
        )

        # Update reminder based on result
        with transaction.atomic():
            reminder.refresh_from_db()

            if result.success:
                reminder.status_whatsapp = EventReminder.SENT
                reminder.sent_at_whatsapp = now
                reminder.last_error_whatsapp = None
                logger.info(f"WhatsApp invitation {reminder_id} sent successfully")
            else:
                reminder.status_whatsapp = EventReminder.FAILED
                reminder.last_error_whatsapp = result.message
                reminder.retry_count_whatsapp += 1

                # Schedule retry if appropriate
                if result.retry_after and reminder.retry_count_whatsapp < 3:
                    logger.info(
                        f"Scheduling WhatsApp retry for {reminder_id} in {result.retry_after} seconds"
                    )
                    send_whatsapp_invitation.apply_async(
                        args=[reminder_id], countdown=result.retry_after
                    )
                    reminder.status_whatsapp = EventReminder.RETRYING

                logger.error(
                    f"WhatsApp invitation {reminder_id} failed: {result.message}"
                )

            reminder.save()

        return {
            "success": result.success,
            "message": result.message,
            "external_id": result.external_id,
        }

    except EventReminder.DoesNotExist:
        logger.error(f"EventReminder {reminder_id} not found")
        return {"success": False, "message": "Reminder not found"}

    except Exception as e:
        logger.error(f"Unexpected error in WhatsApp invitation {reminder_id}: {e}")

        # Update reminder with error
        try:
            reminder = EventReminder.objects.get(rid=reminder_id)
            reminder.status_whatsapp = EventReminder.FAILED
            reminder.last_error_whatsapp = "Error inesperado"
            reminder.retry_count_whatsapp += 1
            reminder.save()
        except:
            pass

        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2**self.request.retries))

        return {"success": False, "message": str(e)}


def schedule_whatsapp_invitations(whatsapp_reminders: QuerySet[EventReminder]):
    """
    Schedule WhatsApp invitations with dynamic delays to prevent Meta bans
    Uses sent_at_whatsapp to calculate delays based on actual send times

    Args:
        whatsapp_reminders: QuerySet of EventReminder objects

    Returns:
        int: Number of scheduled invitations
    """
    from django.utils import timezone

    scheduled_count = 0
    local_tz = timezone.get_current_timezone()
    now = timezone.now().astimezone(local_tz)

    # Group reminders by event schedule for better delay calculation
    reminders_by_event = {}
    for reminder in whatsapp_reminders:
        if reminder.is_ready_for_whatsapp_send():
            event_id = reminder.enrollment.event_schedule.esid
            if event_id not in reminders_by_event:
                reminders_by_event[event_id] = []
            reminders_by_event[event_id].append(reminder)

    # Process each event schedule separately
    for event_id, event_reminders in reminders_by_event.items():
        event_schedule = event_reminders[0].enrollment.event_schedule

        # Calculate initial delay based on last sent message for this event
        base_delay = calculate_delay_since_last_sent(event_schedule)
        if base_delay is None:
            # Not past scheduled time, use traditional cumulative delay
            base_delay = 0

        # Get delay configuration for subsequent messages
        min_delay, max_delay = get_whatsapp_delay_range(event_schedule)
        if min_delay is None:
            min_delay = 5  # Default minimum delay

        # Schedule reminders for this event with cumulative delays
        cumulative_delay = base_delay

        for index, reminder in enumerate(event_reminders):
            if index == 0:
                delay_seconds = base_delay
            else:
                # Add random delay for subsequent messages
                additional_delay = get_random_delay(min_delay, max_delay)
                cumulative_delay += additional_delay
                delay_seconds = cumulative_delay

            send_whatsapp_invitation.apply_async(
                args=[str(reminder.rid)], countdown=delay_seconds
            )

            scheduled_count += 1

            if delay_seconds == 0:
                logger.info(
                    f"Scheduled immediate WhatsApp invitation for reminder {reminder.rid}"
                )
            else:
                logger.info(
                    f"Scheduled WhatsApp invitation for reminder {reminder.rid} "
                    f"with {delay_seconds}s delay (base: {base_delay}s, cumulative: {cumulative_delay}s)"
                )

    return scheduled_count
