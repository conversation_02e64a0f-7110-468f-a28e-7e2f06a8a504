from django.core.management.base import BaseCommand
from core.seed.registry import execute_all_seeds, get_registered_seeds


class Command(BaseCommand):
    help = "Execute all registered seeds in order"

    def add_arguments(self, parser):
        parser.add_argument(
            "--list",
            action="store_true",
            help="List all registered seeds without executing them",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Show detailed output for each seed",
        )

    def handle(self, *args, **options):
        if options["list"]:
            self.list_seeds()
            return

        self.stdout.write(
            self.style.SUCCESS("Starting execution of all registered seeds...")
        )

        try:
            result = execute_all_seeds()

            if options["verbose"]:
                self.show_detailed_results(result)
            else:
                self.show_summary_results(result)

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error executing seeds: {str(e)}"))
            raise

    def list_seeds(self):
        """List all registered seeds"""
        seeds = get_registered_seeds()

        if not seeds:
            self.stdout.write(self.style.WARNING("No seeds registered"))
            return

        self.stdout.write(self.style.SUCCESS(f"Registered seeds ({len(seeds)}):"))

        for seed in seeds:
            self.stdout.write(
                f"  {seed['order']:3d}. {seed['name']} - {seed['description']}"
            )

    def show_summary_results(self, result):
        """Show summary of execution results"""
        if result["success"]:
            self.stdout.write(
                self.style.SUCCESS(
                    f"✓ All seeds executed successfully!\n"
                    f"Total created: {result['total_created']}\n"
                    f"Total updated: {result['total_updated']}\n"
                    f"Seeds executed: {len(result['executed_seeds'])}"
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(
                    f"✗ Some seeds failed!\n"
                    f"Successful: {len(result['executed_seeds'])}\n"
                    f"Failed: {len(result['failed_seeds'])}\n"
                    f"Total created: {result['total_created']}\n"
                    f"Total updated: {result['total_updated']}"
                )
            )

            # Show failed seeds
            for failed_seed in result["failed_seeds"]:
                self.stdout.write(
                    self.style.ERROR(
                        f"  ✗ {failed_seed['name']}: {failed_seed['error']}"
                    )
                )

    def show_detailed_results(self, result):
        """Show detailed results for each seed"""
        self.stdout.write(self.style.SUCCESS("Detailed execution results:"))

        # Show successful seeds
        for seed in result["executed_seeds"]:
            self.stdout.write(
                self.style.SUCCESS(
                    f"{seed['order']}. {seed['name']}\n"
                    f"  Created: {seed['created']}, Updated: {seed['updated']}"
                )
            )

        # Show failed seeds
        for seed in result["failed_seeds"]:
            self.stdout.write(
                self.style.ERROR(
                    f"✗ {seed['name']} - {seed['description']}\n"
                    f"  Error: {seed['error']}"
                )
            )

        # Show summary
        self.stdout.write(
            self.style.SUCCESS(
                f"\nSummary:\n"
                f"Total created: {result['total_created']}\n"
                f"Total updated: {result['total_updated']}\n"
                f"Successful seeds: {len(result['executed_seeds'])}\n"
                f"Failed seeds: {len(result['failed_seeds'])}"
            )
        )
