import requests
from django.conf import settings


"""
    Sends an HTTP request to the specified API endpoint.

    Args:
        url (str): API URI (e.g., /subscriber/get_by_phone/{phone}/).
        method (str): HTTP method (GET, POST, PUT, DELETE).
        headers (dict): Additional headers for the request.
        data (dict, optional): Data to send in the request body (for POST/PUT). Defaults to None.
    Returns:
        dict: JSON response from the API if the request is successful.
        None: If the request fails or an error occurs.

    Note:
        For more information about the API endpoints, see https://docs.tokechat.net/
"""


def fetcher(url, method="GET", headers={}, data=None):
    api_key = getattr(settings, "TOKECHAT_API_KEY", "")

    init_headers = {"Access-Token": api_key, "Content-Type": "application/json"}

    try:
        # Prepare request parameters
        request_params = {
            "method": method,
            "url": f"https://backend.tokechat.net/api{url}",
            "headers": {**init_headers, **headers},
        }

        # Handle different request types
        if method in ["POST", "PUT"]:
            request_params["json"] = data

        # Send the HTTP request
        response = requests.request(**request_params)

        print(response)

        if response.status_code >= 200 and response.status_code < 300:
            return {"success": True, "response": response.json()}
        else:
            print(f"Error: {response.status_code} - {response.text}")
            return {"success": False, "error_code": response.status_code}
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None
