"""
Fetch privacy settings via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def fetch_privacy_settings(instance_name: str) -> Dict[str, Any]:
    """
    Fetch WhatsApp privacy settings

    Args:
        instance_name: Name of the WhatsApp instance

    Returns:
        Dict containing the API response with privacy settings

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/chat/fetchPrivacySettings/{instance_name}", method="GET"
        )

        logger.info(
            f"Privacy settings fetched successfully for instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch privacy settings: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching privacy settings: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
