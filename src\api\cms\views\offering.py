from rest_framework import viewsets
from core.models import Offering
from api.cms.serializers.offering import (
    CmsOfferingSerializer,
    CmsCreateOfferingSerializer,
    CmsUpdateOfferingSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON>ica<PERSON>, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin
from api.paginations import StandardResultsPagination


class CmsOfferingViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Offering
    queryset = Offering.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CmsOfferingSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]
    pagination_class = StandardResultsPagination

    swagger_tags = ["Offering"]

    def get_serializer_class(self):

        if self.action == "create":
            return CmsCreateOfferingSerializer
        if self.action in ["update", "partial_update"]:
            return CmsUpdateOfferingSerializer
        return super().get_serializer_class()
