from typing import Optional
from core.appsheet import appsheet_client


def get_sales_by_status(
    status: str,
    offering_id: Optional[int] = None,
    page_size: Optional[int] = None,
    page: Optional[int] = None,
):
    """
    Obtiene ventas filtradas por estado con paginación.

    :param status: Estado de la venta a filtrar
    :param offering_id: ID del curso a filtrar (opcional)
    :param page_size: Tamaño de la página
    :param page: Número de página
    :return: Diccionario con ventas filtradas por estado y metadata de paginación
    """
    # Construir expresión de filtro
    filter_expression = f'[Estado de venta] = "{status}"'
    if offering_id:
        filter_expression = f' AND ([Curso] = "{offering_id}", {filter_expression})'

    # Obtener conteo total para metadata de paginación
    total_count = appsheet_client.get_total_count("Venta", filter_expression)

    # si es 0
    if total_count == 0:
        return {
            "data": [],
            "meta": {"total": 0, "page": 1, "pageSize": 0, "totalPages": 0},
        }
    # si no se especifica el tamaño de la página, se obtienen todos los registros
    if not page_size:
        page_size = total_count
        page = 1
    if not page:
        page = 1

    # Obtener registros paginados
    response = appsheet_client.find_items(
        table_name="Venta",
        filter_expression=filter_expression,
        page_size=page_size,
        page=page,
        order_direction="DESC",
    )

    sales = []
    for sale in response:
        # si nombre completo o whatsapp no existe, se omite el registro
        if not sale.get("Nombre completo") or not sale.get("WhatsApp"):
            continue
        sales.append(
            {
                "ID Venta": sale.get("ID Venta", ""),
                "Estado De Venta": sale.get("Estado de venta", ""),
                "Curso": sale.get("Curso", ""),
                "Nombre completo": sale.get("Nombre completo", ""),
                "WhatsApp": sale.get("WhatsApp", ""),
            }
        )

    # Añadir metadata de paginación
    meta = {
        "total": total_count,
        "page": page,
        "pageSize": page_size,
        "totalPages": (total_count + page_size - 1) // page_size,
    }

    return {"data": sales, "meta": meta}
