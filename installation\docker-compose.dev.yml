services:
  api:
    build:
      context: ../src
      dockerfile: Dockerfile.dev
    container_name: portals-api
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ../src:/app
    ports:
      - "8001:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - DJANGO_DEBUG=1
    depends_on:
      db:
        condition: service_started
      redis:
        condition: service_started
      minio:
        condition: service_started
      celery:
        condition: service_started
    env_file:
      - ../.env
    restart: unless-stopped

  db:
    image: postgres:15.1-alpine
    container_name: portals-psql
    restart: unless-stopped
    environment:
      POSTGRES_DB: portals-db
      POSTGRES_USER: portals
      POSTGRES_PASSWORD: portals
    ports:
      - 5432:5432
    volumes:
      - db:/var/lib/postgresql/data

  minio:
    image: minio/minio:RELEASE.2025-02-18T16-25-55Z
    container_name: portals-storage
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data
    restart: unless-stopped
  
  redis:
    image: redis:7-alpine
    container_name: portals-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

  celery:
    build:
      context: ..
      dockerfile: installation/celery/Dockerfile
    container_name: portals-celery
    command: watchmedo auto-restart --directory=/app --pattern=*.py --recursive -- celery -A core worker --loglevel=info
    volumes:
      - ../src:/app
    depends_on:
      redis:
        condition: service_started
      db:
        condition: service_started
    env_file:
      - ../.env

  celery-beat:
    build:
      context: ..
      dockerfile: installation/celery/Dockerfile
    container_name: portals-celery-beat
    command: watchmedo auto-restart --directory=/app --pattern=*.py --recursive -- celery -A core beat --loglevel=info
    volumes:
      - ../src:/app
    depends_on:
      redis:
        condition: service_started
      db:
        condition: service_started
      celery:
        condition: service_started
    env_file:
      - ../.env

volumes:
  redis-data:
  minio-data:
  db:
    driver: local
