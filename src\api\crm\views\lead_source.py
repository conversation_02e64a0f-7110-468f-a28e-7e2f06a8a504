from rest_framework import viewsets
from core.models import LeadSource
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.lead_source import (
    CrmLeadSourceSerializer,
)
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmLeadSourceViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = LeadSource
    queryset = LeadSource.objects.filter(deleted=False).order_by("created_at")
    swagger_tags = ["Lead Source"]
    serializer_class = CrmLeadSourceSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
