from django_filters import rest_framework as filters
from django.db.models import Q
from core.models import Event


class CrmEventFilter(filters.FilterSet):
    """Filter for Event model"""

    search = filters.CharFilter(
        method="filter_search",
        help_text="Filter by event name, description, or location.",
    )

    offering = filters.CharFilter(
        method="filter_offering",
        help_text="Filter by offering, comma separated list of offering IDs.",
    )

    type = filters.CharFilter(
        method="filter_type",
        help_text="Filter by event type, comma separated list of type IDs.",
    )

    modality = filters.CharFilter(
        help_text="Filter by event modality.",
        field_name="modality",
        lookup_expr="exact",
    )

    instructor = filters.Char<PERSON>ilter(
        help_text="Filter by instructor.",
        field_name="instructor__iid",
        lookup_expr="exact",
    )

    created_at = filters.DateFromToRangeFilter()

    class Meta:
        model = Event
        fields = ["search", "offering", "type", "modality", "instructor", "created_at"]

    def filter_search(self, queryset, name, value):
        """
        Filter by event name, description, or location.
        """
        if not value:
            return queryset

        return queryset.filter(
            Q(name__icontains=value)
            | Q(description__icontains=value)
            | Q(location__icontains=value)
        )

    def filter_offering(self, queryset, name, value):
        """
        Filter by offering, comma separated list of offering IDs.
        """
        if not value:
            return queryset

        offering_ids = [oid.strip() for oid in value.split(",") if oid.strip()]

        if not offering_ids:
            return queryset

        return queryset.filter(offering__oid__in=offering_ids)

    def filter_type(self, queryset, name, value):
        """
        Filter by event type, comma separated list of type IDs.
        """
        if not value:
            return queryset

        types = [t.strip() for t in value.split(",") if t.strip()]

        if not types:
            return queryset

        return queryset.filter(type__in=types)
