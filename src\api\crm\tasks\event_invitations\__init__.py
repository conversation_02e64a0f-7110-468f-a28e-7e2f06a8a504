"""
Event invitations tasks package

This package contains all Celery tasks related to event invitation management,
organized by responsibility and invitation type.
"""

# Import all tasks to make them available when the package is imported
from .core import schedule_pending_invitations, process_new_enrollment_invitations
from .whatsapp import send_whatsapp_invitation, schedule_delayed_whatsapp_batch
from .email import send_email_invitation, send_bulk_email_invitations
from .retry import retry_failed_invitations

__all__ = [
    'schedule_pending_invitations',
    'process_new_enrollment_invitations',
    'send_whatsapp_invitation',
    'schedule_delayed_whatsapp_batch',
    'send_email_invitation',
    'send_bulk_email_invitations',
    'retry_failed_invitations',
]
