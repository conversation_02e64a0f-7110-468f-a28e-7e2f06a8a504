"""
Join group via invite code using Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def join_group(instance_name: str, invite_code: str) -> Dict[str, Any]:
    """
    Join a WhatsApp group using invite code

    Args:
        instance_name: Name of the WhatsApp instance
        invite_code: Group invite code

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/group/acceptInviteCode/{instance_name}?inviteCode={invite_code}",
            method="GET",
        )

        logger.info(
            f"Successfully joined group via invite code using instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to join group: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error joining group: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
