"""
Leave group via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def leave_group(instance_name: str, group_jid: str) -> Dict[str, Any]:
    """
    Leave a WhatsApp group

    Args:
        instance_name: Name of the WhatsApp instance
        group_jid: Group JID identifier

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/group/leaveGroup/{instance_name}?groupJid={group_jid}",
            method="DELETE",
        )

        logger.info(
            f"Successfully left group {group_jid} using instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to leave group: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error leaving group: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
