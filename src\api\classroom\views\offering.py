from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from core.models import Offering
from api.classroom.serializers.offering import (
    ClassroomOfferingBaseSerializer,
    ClassroomOfferingDetailedSerializer,
)


class ClassroomOfferingViewset(viewsets.ModelViewSet):
    model_class = Offering
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return ClassroomOfferingDetailedSerializer
        return ClassroomOfferingBaseSerializer

    def get_queryset(self):
        return Offering.objects.filter(
            deleted=False, offering_enrollments__user=self.request.user
        ).distinct()
