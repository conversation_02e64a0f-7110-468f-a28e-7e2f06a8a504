from rest_framework import serializers
from core.models import LeadSource


class CrmLeadSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeadSource
        fields = (
            "lsid",
            "name",
            "description",
            "created_at",
            "updated_at",
        )


class CrmCreateLeadSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeadSource
        fields = "__all__"
