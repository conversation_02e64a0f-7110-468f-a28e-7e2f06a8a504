from rest_framework import viewsets
from core.models import Term
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.term import CrmTermSerializer
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmTermViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Term
    queryset = Term.objects.filter(deleted=False).order_by("created_at")
    serializer_class = CrmTermSerializer
    pagination_class = StandardResultsPagination
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    swagger_tags = ["Terms"]
