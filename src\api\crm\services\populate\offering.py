import json
from datetime import datetime, timedelta
from core.models import Offering

POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"

offerings_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/6_offerings.json"


def populate_offering_data():
    default_date = (datetime.now() - timedelta(days=730)).date()

    with open(offerings_json_file_path) as f:
        data = json.load(f)

        existing_oids = set(Offering.objects.values_list("oid", flat=True))

        # Offerings to create (those not in existing_oids and not marked as already_exists)
        offerings_to_create = [
            Offering(
                oid=item["oid"],
                slug=item["slug"],
                name=item["name"],
                long_name=item["name"],
                code_name=item["code_name"],
                start_date=(
                    datetime.strptime(item["start_date"], "%Y-%m-%d").date()
                    if item["start_date"]
                    else default_date
                ),
                end_date=(
                    datetime.strptime(item["end_date"], "%Y-%m-%d").date()
                    if item["end_date"]
                    else default_date
                ),
                description=item.get("description", ""),
                duration=item.get("duration", ""),
                frequency=item.get("frequency", ""),
                hours=item.get("hours", 1),
                schedule=item.get("schedule", ""),
                type=item["type"],
                stage=Offering.FINISHED_STAGE,
                base_price=0.0,
                foreign_base_price=0.0,
                discount=0.0,
                ext_reference=item.get("classroom_code", None),
            )
            for item in data
            if (
                item["oid"] not in existing_oids
                and not item.get("already_exists", False)
            )
        ]
        # Offerings to update (those already in existing_oids)
        offerings_to_update = [item for item in data if item["oid"] in existing_oids]

        # First, create the new offerings
        Offering.objects.bulk_create(offerings_to_create)

        # Now rest 6 months to each offering's created_at & updated_at
        for offering in Offering.objects.filter(
            oid__in=[o.oid for o in offerings_to_create]
        ):
            offering.created_at = datetime.now() - timedelta(days=180)
            offering.updated_at = datetime.now() - timedelta(days=180)
            offering.save()

        # Then, update the existing offerings
        for offering_data in offerings_to_update:
            try:
                offering = Offering.objects.get(oid=offering_data["oid"])
                if not offering.code_name and offering_data.get("code_name"):
                    offering.code_name = offering_data["code_name"]

                if not offering.long_name and offering_data.get("name"):
                    offering.long_name = offering_data["name"]
                offering.save()
            except Offering.DoesNotExist:
                continue
