"""
Send reaction to message via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def send_reaction(
    instance_name: str, remote_jid: str, message_id: str, from_me: bool, reaction: str
) -> Dict[str, Any]:
    """
    Send reaction to a message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID where the message is
        message_id: ID of the message to react to
        from_me: Whether the message was sent by me
        reaction: Emoji reaction to send

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If reaction is invalid
    """
    try:
        # Validate reaction
        if not reaction or not reaction.strip():
            raise ValueError("Reaction emoji is required")

        # Build request body
        body = {
            "key": {
                "remoteJid": remote_jid,
                "fromMe": from_me,
                "id": message_id,
            },
            "reaction": reaction.strip(),
        }

        # Make the request
        response = evolution_request(
            uri=f"/message/sendReaction/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Reaction '{reaction}' sent successfully to message {message_id} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send reaction: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid reaction parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending reaction: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
