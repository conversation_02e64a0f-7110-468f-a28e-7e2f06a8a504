# Generated by Django 5.0.6 on 2025-08-02 16:24

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0016_activity_created_by_activity_updated_by_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="activity",
            options={
                "permissions": [
                    ("can_manage_any_activities", "Can manage any activities"),
                    ("can_manage_own_activities", "Can manage own activities"),
                ],
                "verbose_name": "Activity",
                "verbose_name_plural": "Activities",
            },
        ),
        migrations.AlterModelOptions(
            name="order",
            options={
                "permissions": [
                    ("modify_sold_order_stage", "Can modify stage of sold orders")
                ],
                "verbose_name": "Order",
                "verbose_name_plural": "Orders",
            },
        ),
        migrations.AlterModelOptions(
            name="user",
            options={"permissions": [("manage_staff_users", "Can manage staff users")]},
        ),
    ]
