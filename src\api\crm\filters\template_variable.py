import django_filters
from core.models.template import TemplateVariable, TemplateType
from django.db.models import Q


class CrmTemplateVariableFilter(django_filters.FilterSet):
    """
    Filter for TemplateVariable model
    """

    # Filter by variable name (case-insensitive partial match)
    search = django_filters.CharFilter(
        field_name="name",
        lookup_expr="icontains",
        help_text="Filter by variable name or description (case-insensitive partial match)",
    )

    # Filter by template type (exact match)
    template_type = django_filters.ModelChoiceFilter(
        field_name="template_type",
        queryset=TemplateType.objects.filter(is_active=True),
        help_text="Filter by template type (exact match)",
    )

    class Meta:
        model = TemplateVariable
        fields = [
            "search",
            "template_type",
        ]
