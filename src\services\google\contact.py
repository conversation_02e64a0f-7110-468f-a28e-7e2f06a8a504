from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from django.conf import settings


class GoogleContactsManager:
    """
    Clase para gestionar contactos de Google utilizando la API de Google People.
    """

    def __init__(self, credentials_file=None):
        """
        Inicializa el gestor de contactos con las credenciales proporcionadas.

        Args:
            credentials_file (str): Ruta al archivo JSON de la Service Account
        """

        if credentials_file is None:
            credentials_file = f"{settings.SECRET_FILES_DIR}/credentials.json"

        self.SCOPES = [
            "https://www.googleapis.com/auth/contacts",
            "https://www.googleapis.com/auth/contacts.readonly",
        ]

        self.credentials = service_account.Credentials.from_service_account_file(
            credentials_file,
            scopes=self.SCOPES,
            subject="<EMAIL>",
        )
        self.service = build("people", "v1", credentials=self.credentials)
        self.contacts_service = self.service.people()

    def list_contact_groups(self):
        """Lista todos los grupos de contactos disponibles."""
        try:
            results = self.service.contactGroups().list(pageSize=100).execute()
            return results.get("contactGroups", [])
        except HttpError as error:
            print(f"Error al listar grupos de contactos: {error}")
            return []

    def list_contacts(self, page_size=100):
        """
        Obtiene la lista de contactos.

        Args:
            page_size (int): Número máximo de contactos a recuperar

        Returns:
            list: Lista de contactos
        """
        try:
            results = (
                self.contacts_service.connections()
                .list(
                    resourceName="people/me",
                    pageSize=page_size,
                    personFields="names,emailAddresses,phoneNumbers,addresses",
                )
                .execute()
            )

            return results.get("connections", [])
        except HttpError as error:
            print(f"Error al listar contactos: {error}")
            return []

    def create_contact(self, first_name, last_name, email, phone, address=None):
        """
        Crea un nuevo contacto.

        Args:
            first_name (str): Nombre del contacto
            last_name (str): Apellido del contacto
            email (str, optional): Correo electrónico del contacto
            phone (str, optional): Número telefónico del contacto
            address (str, optional): Dirección del contacto

        Returns:
            dict: Datos del contacto creado o None si hay error
        """
        try:
            contact_body = {
                "names": [
                    {
                        "givenName": first_name,
                        "familyName": last_name,
                    }
                ]
            }

            if email:
                contact_body["emailAddresses"] = [
                    {
                        "value": email,
                        "type": "home",
                    }
                ]

            if phone:
                contact_body["phoneNumbers"] = [
                    {
                        "value": phone,
                        "type": "mobile",
                    }
                ]

            if address:
                contact_body["addresses"] = [
                    {
                        "formattedValue": address,
                        "type": "home",
                    }
                ]

            result = self.contacts_service.createContact(body=contact_body).execute()

            return result
        except HttpError as error:
            print(f"Error al crear contacto: {error}")
            return None

    def get_contact(self, resource_name):
        """
        Obtiene los detalles de un contacto específico.

        Args:
            resource_name (str): ID del recurso del contacto (e.g., "people/c123456789")

        Returns:
            dict: Datos del contacto o None si hay error
        """
        try:
            result = self.contacts_service.get(
                resourceName=resource_name,
                personFields="names,emailAddresses,phoneNumbers,addresses",
            ).execute()

            return result
        except HttpError as error:
            print(f"Error al obtener contacto: {error}")
            return None

    def update_contact(
        self,
        resource_name,
        first_name=None,
        last_name=None,
        email=None,
        phone=None,
        address=None,
    ):
        """
        Actualiza un contacto existente.

        Args:
            resource_name (str): ID del recurso del contacto (e.g., "people/c123456789")
            first_name (str, optional): Nuevo nombre del contacto
            last_name (str, optional): Nuevo apellido del contacto
            email (str, optional): Nuevo correo electrónico
            phone (str, optional): Nuevo número telefónico
            address (str, optional): Nueva dirección

        Returns:
            dict: Datos del contacto actualizado o None si hay error
        """
        try:
            # Primero obtenemos el etag del contacto, necesario para la actualización
            contact = self.get_contact(resource_name)
            if not contact:
                return None

            update_body = {"etag": contact.get("etag", "")}

            # Solo agregamos los campos que queremos actualizar
            update_person_fields = []

            if first_name:
                update_body["names"] = [{"givenName": first_name}]
                update_person_fields.append("names")

            if first_name and last_name:
                update_body["names"] = [
                    {"givenName": first_name, "familyName": last_name}
                ]
                update_person_fields.append("names")

            if email:
                update_body["emailAddresses"] = [{"value": email, "type": "home"}]
                update_person_fields.append("emailAddresses")

            if phone:
                update_body["phoneNumbers"] = [{"value": phone, "type": "mobile"}]
                update_person_fields.append("phoneNumbers")

            if address:
                update_body["addresses"] = [{"formattedValue": address, "type": "home"}]
                update_person_fields.append("addresses")

            # Si no hay campos para actualizar, devolvemos el contacto original
            if not update_person_fields:
                return contact

            result = self.contacts_service.updateContact(
                resourceName=resource_name,
                updatePersonFields=",".join(update_person_fields),
                body=update_body,
            ).execute()

            return result
        except HttpError as error:
            print(f"Error al actualizar contacto: {error}")
            return None

    def delete_contact(self, resource_name):
        """
        Elimina un contacto.

        Args:
            resource_name (str): ID del recurso del contacto (e.g., "people/c123456789")

        Returns:
            bool: True si la eliminación fue exitosa, False en caso contrario
        """
        try:
            self.contacts_service.deleteContact(resourceName=resource_name).execute()
            return True
        except HttpError as error:
            print(f"Error al eliminar contacto: {error}")
            return False

    def search_contacts(self, query, page_size=30):
        """
        Busca contactos que coincidan con la consulta.

        Args:
            query (str): Texto a buscar en los contactos
            page_size (int): Número máximo de resultados a devolver

        Returns:
            list: Lista de contactos que coinciden con la búsqueda
        """
        try:
            results = self.contacts_service.searchContacts(
                query=query,
                pageSize=page_size,
                readMask="names,emailAddresses,phoneNumbers",
            ).execute()

            return results.get("results", [])
        except HttpError as error:
            print(f"Error al buscar contactos: {error}")
            return []
