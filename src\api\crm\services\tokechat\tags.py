from api.crm.services.tokechat.api_request import fetcher


def get_tags():
    try:
        tags = fetcher(url="/tags/")
        if tags.get("success"):
            return tags.get("response")["data"]

        return None
    except Exception as e:
        print(f"An error occurred: {e}")


def get_tag_by_name(name: str):
    try:
        tags = get_tags()
        if len(tags) > 0:
            return next((item for item in tags if item["name"] == name), None)

        return None
    except Exception as e:
        print(f"An error occurred: {e}")
