from rest_framework import serializers
from core.models import Benefit


class CrmBenefitSerializer(serializers.ModelSerializer):
    class Meta:
        model = Benefit
        fields = (
            "bid",
            "name",
            "description",
            "created_at",
            "updated_at",
        )


class CrmCreateBenefitSerializer(serializers.ModelSerializer):
    class Meta:
        model = Benefit
        fields = "__all__"
