from rest_framework import viewsets
from core.models import PaymentMethod
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.payment_method import CrmPaymentMethodSerializer
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmPaymentMethodViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = PaymentMethod
    queryset = PaymentMethod.objects.filter(deleted=False).order_by("created_at")
    swagger_tags = ["Payment Methods"]
    serializer_class = CrmPaymentMethodSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
