"""
Send document message via Evolution API
"""

import base64
import logging
from typing import Dict, Any, Optional, List
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def _is_base64(s: str) -> bool:
    """Check if string is valid base64"""
    try:
        return base64.b64encode(base64.b64decode(s)).decode() == s
    except Exception:
        return False


def send_document(
    instance_name: str,
    remote_jid: str,
    media: str,
    mimetype: str = "application/pdf",
    caption: str = "",
    file_name: str = "document.pdf",
    delay: Optional[int] = None,
    quoted_message_id: Optional[str] = None,
    mentions_everyone: Optional[bool] = None,
    mentioned_numbers: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Send a document message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        media: Document data (URL or base64)
        mimetype: MIME type of the document (default: application/pdf)
        caption: Caption text for the document
        file_name: Name of the document file (default: document.pdf)
        delay: Delay in milliseconds before sending
        quoted_message_id: ID of message to quote/reply to
        mentions_everyone: Whether to mention everyone in group
        mentioned_numbers: List of phone numbers to mention

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If media format is invalid
    """
    try:
        # Validate media format
        if not (
            media.startswith("http") or media.startswith("data:") or _is_base64(media)
        ):
            raise ValueError(
                "Invalid media format. Document must be a valid URL or base64 string"
            )

        # Build request body
        body = {
            "number": remote_jid,
            "mediatype": "document",
            "media": media,
            "mimetype": mimetype or "application/pdf",
            "caption": caption or "",
            "fileName": file_name or "document.pdf",
        }

        # Add optional delay
        if delay is not None:
            body["delay"] = delay

        # Add quoted message
        if quoted_message_id:
            body["quoted"] = {
                "key": {
                    "id": quoted_message_id,
                }
            }

        # Handle mentions
        if mentions_everyone:
            body["mentionsEveryOne"] = True
        elif mentioned_numbers:
            # Ensure numbers have WhatsApp format
            formatted_numbers = []
            for num in mentioned_numbers:
                num = num.strip()
                if "@s.whatsapp.net" not in num:
                    num = f"{num}@s.whatsapp.net"
                formatted_numbers.append(num)
            body["mentioned"] = formatted_numbers

        # Make the request
        response = evolution_request(
            uri=f"/message/sendMedia/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Document message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send document message: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid document parameters: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending document message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
