"""
Block/unblock contact via Evolution API
"""

import logging
from typing import Dict, Any, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

BlockStatus = Literal["block", "unblock"]


def block_contact(
    instance_name: str, remote_jid: str, status: BlockStatus
) -> Dict[str, Any]:
    """
    Block or unblock a contact

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number to block/unblock
        status: Action to perform (block or unblock)

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If status is invalid
    """
    try:
        # Validate status
        valid_statuses = ["block", "unblock"]
        if status not in valid_statuses:
            raise ValueError(f"Invalid status. Must be one of: {valid_statuses}")

        # Build request body
        body = {
            "number": remote_jid,
            "status": status,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/updateBlockStatus/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Contact {remote_jid} {status}ed successfully via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to {status} contact: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for block_contact: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error managing contact block status: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
