from rest_framework import serializers
from core.models import <PERSON><PERSON><PERSON><PERSON>, Blog<PERSON>ate<PERSON><PERSON>, Blog<PERSON>ag, Instructor, File
from api.shared.serializers.file import FileSerializer
from django.utils.text import slugify
from django.utils import timezone
from .blog_category import CmsBlogCategorySerializer
from .blog_tag import CmsBlogTagSerializer
from core.models import User


class CmsBlogAuthorSerializer(serializers.ModelSerializer):
    profile_photo = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Instructor
        fields = ["iid", "full_name", "profile_photo"]


class CmsBlogCreatorSearializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["uid", "username", "email"]


class CmsBlogPostBaseSerializer(serializers.ModelSerializer):
    authors = CmsBlogAuthorSerializer(many=True, read_only=True)
    categories = CmsBlogCategorySerializer(many=True, read_only=True)
    tags = CmsBlogTagSerializer(many=True, read_only=True)
    created_by = CmsBlogCreatorSearializer(read_only=True)


class CmsBlogPostListSerializer(CmsBlogPostBaseSerializer):
    class Meta:
        model = BlogPost
        fields = [
            "bid",
            "title",
            "slug",
            "status",
            "created_at",
            "updated_at",
            "published_at",
            "featured",
            "reading_time",
            "featured_order",
            "view_count",
            "authors",
            "categories",
            "tags",
            "created_by",
        ]


class CmsBlogPostDetailSerializer(CmsBlogPostBaseSerializer):
    cover_image = FileSerializer(read_only=True, allow_null=True)
    thumbnail = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = BlogPost
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]


class CmsCreateBlogPostSerializer(serializers.ModelSerializer):
    cover_image_id = serializers.UUIDField(write_only=True, required=False)
    thumbnail_id = serializers.UUIDField(write_only=True, required=False)
    authors = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )
    categories = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )
    tags = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )

    class Meta:
        model = BlogPost
        fields = [
            "bid",
            "title",
            "slug",
            "summary",
            "content",
            "cover_image_id",
            "thumbnail_id",
            "reading_time",
            "status",
            "featured",
            "featured_order",
            "meta_title",
            "meta_description",
            "meta_keywords",
            "authors",
            "categories",
            "tags",
        ]

    def create(self, validated_data):
        # Extract M2M fields
        authors = validated_data.pop("authors", [])
        categories = validated_data.pop("categories", [])
        tags = validated_data.pop("tags", [])

        # Extract file IDs
        cover_image_id = validated_data.pop("cover_image_id", None)
        thumbnail_id = validated_data.pop("thumbnail_id", None)

        # Generate slug if not provided
        if not validated_data.get("slug"):
            title = validated_data.get("title", "")
            slug = slugify(title)
            slug = slug if slug else "new-blog-post"

            # Check if slug exists and make it unique if needed
            if BlogPost.objects.filter(slug=slug).exists():
                count = 1
                while BlogPost.objects.filter(slug=f"{slug}-{count}").exists():
                    count += 1
                slug = f"{slug}-{count}"

            validated_data["slug"] = slug

        # Set created_by to current user
        request = self.context.get("request")
        if request and hasattr(request, "user"):
            validated_data["created_by"] = request.user

        # Create the blog post
        blog_post = BlogPost.objects.create(**validated_data)

        # Process cover image if provided
        if cover_image_id:
            try:
                cover_image = File.objects.get(fid=cover_image_id)
                blog_post.cover_image = cover_image
                # Mark the file as used
                cover_image.is_used = True
                cover_image.save()
            except File.DoesNotExist:
                pass

        # Process thumbnail if provided
        if thumbnail_id:
            try:
                thumbnail = File.objects.get(fid=thumbnail_id)
                blog_post.thumbnail = thumbnail
                # Mark the file as used
                thumbnail.is_used = True
                thumbnail.save()
            except File.DoesNotExist:
                pass

        # Add M2M relationships
        if authors:
            authors = Instructor.objects.filter(iid__in=authors)
            blog_post.authors.set(authors)

        if categories:
            categories = BlogCategory.objects.filter(bcid__in=categories)
            blog_post.categories.set(categories)

        if tags:
            tags = BlogTag.objects.filter(btid__in=tags)
            blog_post.tags.set(tags)

        # Update published_at if status is PUBLISHED
        if blog_post.status == BlogPost.PUBLISHED and not blog_post.published_at:
            blog_post.published_at = timezone.now()

        blog_post.save()
        return blog_post


class CmsUpdateBlogPostSerializer(serializers.ModelSerializer):
    cover_image_id = serializers.UUIDField(
        write_only=True, required=False, allow_null=True
    )
    thumbnail_id = serializers.UUIDField(
        write_only=True, required=False, allow_null=True
    )
    authors = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )
    categories = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )
    tags = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )

    class Meta:
        model = BlogPost
        fields = [
            "title",
            "slug",
            "summary",
            "content",
            "cover_image_id",
            "thumbnail_id",
            "reading_time",
            "status",
            "featured",
            "featured_order",
            "meta_title",
            "meta_description",
            "meta_keywords",
            "authors",
            "categories",
            "tags",
        ]

    def update(self, instance, validated_data):
        # Extract M2M fields
        authors = validated_data.pop("authors", None)
        categories = validated_data.pop("categories", None)
        tags = validated_data.pop("tags", None)

        # Extract file IDs
        cover_image_id = validated_data.pop("cover_image_id", None)
        thumbnail_id = validated_data.pop("thumbnail_id", None)

        # Generate slug if title is updated but slug is not
        if "title" in validated_data and "slug" not in validated_data:
            title = validated_data.get("title")
            slug = slugify(title)
            slug = slug if slug else "new-blog-post"
            # Check if slug exists and make it unique if needed
            if BlogPost.objects.filter(slug=slug).exclude(bid=instance.bid).exists():
                count = 1
                while (
                    BlogPost.objects.filter(slug=f"{slug}-{count}")
                    .exclude(bid=instance.bid)
                    .exists()
                ):
                    count += 1
                slug = f"{slug}-{count}"

            validated_data["slug"] = slug

        # Check if status is changing to PUBLISHED
        was_published = instance.status == BlogPost.PUBLISHED
        will_be_published = validated_data.get("status") == BlogPost.PUBLISHED

        if not was_published and will_be_published:
            validated_data["published_at"] = timezone.now()

        # Update the instance with validated data
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if cover_image_id and (
            not instance.cover_image or instance.cover_image.fid != cover_image_id
        ):
            # Si hay un nuevo cover_image_id y es diferente al actual
            # Eliminar la imagen anterior si existe
            if instance.cover_image:
                old_cover = instance.cover_image
                old_cover.delete()

            # Asignar la nueva imagen
            try:
                cover_image = File.objects.get(fid=cover_image_id)
                instance.cover_image = cover_image
                # Marcar como usada
                cover_image.is_used = True
                cover_image.save()
            except File.DoesNotExist:
                pass

        # Process thumbnail
        if thumbnail_id and (
            not instance.thumbnail or instance.thumbnail.fid != thumbnail_id
        ):
            # Si hay un nuevo thumbnail_id y es diferente al actual
            # Eliminar la imagen anterior si existe
            if instance.thumbnail:
                old_thumbnail = instance.thumbnail
                old_thumbnail.delete()

            # Asignar la nueva imagen
            try:
                thumbnail = File.objects.get(fid=thumbnail_id)
                instance.thumbnail = thumbnail
                # Marcar como usada
                thumbnail.is_used = True
                thumbnail.save()
            except File.DoesNotExist:
                pass

        # Update M2M relationships if provided
        if authors is not None:
            authors = Instructor.objects.filter(iid__in=authors)
            instance.authors.set(authors)

        if categories is not None:
            categories = BlogCategory.objects.filter(bcid__in=categories)
            instance.categories.set(categories)

        if tags is not None:
            tags = BlogTag.objects.filter(btid__in=tags)
            instance.tags.set(tags)

        instance.save()
        return instance

    def validate(self, attrs):
        """
        Check if a posts is available for publishing.
        """

        status = attrs.get("status", getattr(self.instance, "status", None))
        is_publishing = status == BlogPost.PUBLISHED

        title = attrs.get("title", getattr(self.instance, "title", None))
        summary = attrs.get("summary", getattr(self.instance, "summary", None))
        thumbnail_id = attrs.get(
            "thumbnail_id", getattr(self.instance, "thumbnail_id", None)
        )
        authors = attrs.get("authors", None)
        categories = attrs.get("categories", None)
        tags = attrs.get("tags", None)

        if self.instance:
            if authors is None:
                authors = list(self.instance.authors.values_list("iid", flat=True))
            if categories is None:
                categories = list(
                    self.instance.categories.values_list("bcid", flat=True)
                )
            if tags is None:
                tags = list(self.instance.tags.values_list("btid", flat=True))
            if not thumbnail_id and self.instance.thumbnail:
                thumbnail_id = self.instance.thumbnail.fid

        if is_publishing:
            errors = {}
            if not title:
                errors["title"] = "El título es obligatorio para publicar."
            if not summary:
                errors["summary"] = "El resumen es obligatorio para publicar."
            if not authors or len(authors) == 0:
                errors["authors"] = "Debe haber al menos un autor."
            if not categories or len(categories) == 0:
                errors["categories"] = "Debe seleccionar al menos una categoría."
            if not tags or len(tags) == 0:
                errors["tags"] = "Debe seleccionar al menos una etiqueta."
            if not thumbnail_id:
                errors["thumbnail"] = (
                    "No se ha seleccionado una imagen para la miniatura."
                )
            if errors:
                raise serializers.ValidationError(errors)
        return attrs


class CmsDeleteImageSerializer(serializers.Serializer):
    type = serializers.ChoiceField(
        choices=[("cover", "Cover"), ("thumbnail", "Thumbnail")], required=True
    )
