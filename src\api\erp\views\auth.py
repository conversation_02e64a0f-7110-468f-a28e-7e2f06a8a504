from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.authtoken.models import Token
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.erp.serializers.auth import (
    ErpAuthLoginSerializer,
    ErpTokenSerializer,
)
from api.exceptions import InvalidCredentials
from api.mixins import SwaggerTagMixin
from core.models import User
from drf_yasg.utils import swagger_auto_schema


class ErpAuthViewSet(viewsets.GenericViewSet, SwaggerTagMixin):
    """
    Authentication ViewSet
    """

    queryset = User.objects.filter(deleted=False)
    serializer_class = ErpAuthLoginSerializer

    swagger_tags = ["Auth"]

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: ErpTokenSerializer(),
        },
    )
    @action(detail=False, methods=["POST"])
    def login(self, request, *args, **kwargs):
        """
        Login user and return auth token
        """
        serializer = self.get_serializer(
            data=request.data, context={"request": request}
        )

        try:
            serializer.is_valid(raise_exception=True)
            user = serializer.validated_data["user"]
        except Exception:
            raise InvalidCredentials()

        token, _ = Token.objects.get_or_create(user=user)
        return Response(ErpTokenSerializer(token).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: ErpTokenSerializer(),
        },
    )
    @action(
        detail=False,
        methods=["POST"],
        url_path="check-token",
        authentication_classes=[TokenAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def check_token(self, request, *args, **kwargs):
        """
        Check if the provided token is valid
        Token should be sent in the Authorization header as 'Token <token_key>'
        """
        user = request.user
        token_obj = Token.objects.get(user=user)

        return Response(ErpTokenSerializer(token_obj).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_204_NO_CONTENT: "Token invalidated successfully.",
            status.HTTP_401_UNAUTHORIZED: "Invalid token or user not authenticated.",
        },
    )
    @action(
        detail=False,
        methods=["POST"],
        url_path="logout",
        authentication_classes=[TokenAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def logout(self, request, *args, **kwargs):
        """
        Logout user by invalidating the token
        Token should be sent in the Authorization header as 'Token <token_key>'
        """
        user = request.user
        token_obj = Token.objects.get(user=user)
        token_obj.delete()

        return Response(status=status.HTTP_204_NO_CONTENT)
