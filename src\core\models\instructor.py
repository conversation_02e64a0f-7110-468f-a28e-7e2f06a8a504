import uuid
from django.db import models
from core.models.base import (
    AuditBaseModel,
    OrderBaseModel,
)
from core.models.file import File


class Instructor(AuditBaseModel, OrderBaseModel):
    PUBLISHED_STATUS = "Published"
    DRAFT_STATUS = "Draft"

    STATUS_CHOICES = [
        (PUBLISHED_STATUS, "Published"),
        (DRAFT_STATUS, "Draft"),
    ]
    iid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    full_name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Full Name",
    )
    biography = models.TextField(
        blank=True,
        verbose_name="Biography",
    )
    title = models.Char<PERSON>ield(
        max_length=255,
        blank=True,
        verbose_name="Title",
    )
    highlighted_info = models.CharField(
        max_length=255, blank=True, verbose_name="Highlighted Info"
    )
    facebook_url = models.URLField(
        blank=True,
        verbose_name="Facebook URL",
    )
    linkedin_url = models.URL<PERSON>ield(
        blank=True,
        verbose_name="LinkedIn URL",
    )
    instagram_url = models.URLField(
        blank=True,
        verbose_name="Instagram URL",
    )
    profile_photo = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        related_name="instructors",
        blank=True,
        null=True,
        verbose_name="Profile Photo",
    )
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=DRAFT_STATUS,
        verbose_name="Status",
    )
    user = models.OneToOneField(
        "User",
        on_delete=models.SET_NULL,
        related_name="instructor",
        blank=True,
        null=True,
        verbose_name="User",
    )

    def __str__(self):
        return f"{self.full_name}"


class Attachment(AuditBaseModel):
    """
    A model for handle instructor atachments for the website.
    This would be shown in the website as Teachers
    """

    aid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    instructor = models.ForeignKey(
        "core.Instructor",
        on_delete=models.CASCADE,
        related_name="offerings",
        verbose_name="Instructor of the Attachment",
    )

    offering = models.ForeignKey(
        "Offering",
        on_delete=models.CASCADE,
        related_name="offering_attachments",
        verbose_name="Offering of the Attachment",
    )
