from rest_framework import viewsets
from core.models import Testimonial
from api.mixins import SwaggerTagMixin
from api.website.serializers.testimonial import (
    WebsiteTestimonialSerializer,
)
from api.paginations import StandardResultsPagination


class WebsiteTestimonialViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = Testimonial
    queryset = Testimonial.objects.filter(
        deleted=False, status=Testimonial.PUBLISHED_STATUS
    )
    serializer_class = WebsiteTestimonialSerializer
    pagination_class = StandardResultsPagination

    swagger_tags = ["Testimonial"]
