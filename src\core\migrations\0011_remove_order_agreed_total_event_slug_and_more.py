# Generated by Django 5.0.6 on 2025-06-30 05:02

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0010_remove_offering_short_name_remove_order_paid_at_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="order",
            name="agreed_total",
        ),
        migrations.AddField(
            model_name="event",
            name="slug",
            field=models.SlugField(
                default=django.utils.timezone.now,
                help_text="Unique slug for the event, used in URLs",
                max_length=255,
                unique=True,
                verbose_name="Slug",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="email",
            field=models.EmailField(
                blank=True,
                help_text="User's email address, if available",
                max_length=128,
                null=True,
                verbose_name="Email",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="first_name",
            field=models.CharField(
                blank=True,
                help_text="User's first name, if available",
                max_length=127,
                null=True,
                verbose_name="First Name",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="last_name",
            field=models.CharField(
                blank=True,
                help_text="User's last name, if available",
                max_length=128,
                null=True,
                verbose_name="Last Name",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="major",
            field=models.CharField(
                blank=True,
                help_text="User's university major, if available",
                max_length=128,
                null=True,
                verbose_name="University Major",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="occupation",
            field=models.CharField(
                blank=True,
                help_text="User's occupation, if available",
                max_length=64,
                null=True,
                verbose_name="Occupation",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="partnership",
            field=models.ForeignKey(
                blank=True,
                help_text="Partnership associated with the enrollment, if any",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="event_schedule_enrollments",
                to="core.partnership",
                verbose_name="Partnership",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="phone_number",
            field=models.CharField(
                blank=True,
                help_text="User's phone number, if available",
                max_length=32,
                null=True,
                verbose_name="Phone Number",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="term",
            field=models.CharField(
                blank=True,
                help_text="User's academic term, if available",
                max_length=32,
                null=True,
                verbose_name="Term",
            ),
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="university",
            field=models.CharField(
                blank=True,
                help_text="User's university, if available",
                max_length=128,
                null=True,
                verbose_name="University",
            ),
        ),
        migrations.AddField(
            model_name="partnership",
            name="long_name",
            field=models.CharField(
                blank=True,
                help_text="Long name of the partnership",
                max_length=256,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="educationalinstitution",
            name="country",
            field=models.CharField(
                blank=True,
                help_text="Country where the institution is located",
                max_length=64,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="eventscheduleenrollment",
            name="diffusion_channel",
            field=models.CharField(
                blank=True,
                help_text="The channel through which the user learned about the event schedule",
                max_length=64,
                null=True,
                verbose_name="Diffusion Channel",
            ),
        ),
        migrations.AlterField(
            model_name="eventscheduleenrollment",
            name="user",
            field=models.ForeignKey(
                blank=True,
                help_text="The user who is enrolled in the event schedule",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="event_schedule_enrollments",
                to=settings.AUTH_USER_MODEL,
                verbose_name="User",
            ),
        ),
        migrations.AlterField(
            model_name="payment",
            name="payment_date",
            field=models.DateTimeField(
                auto_now_add=True, null=True, verbose_name="Payment Date"
            ),
        ),
        migrations.CreateModel(
            name="Activity",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "aid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Title"
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the activity. It will be markdown formatted.",
                        null=True,
                        verbose_name="Description",
                    ),
                ),
                (
                    "deadline",
                    models.DateTimeField(
                        blank=True,
                        help_text="Deadline for the activity",
                        null=True,
                        verbose_name="Deadline",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("completed", "Completed"),
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                        ],
                        default="pending",
                        max_length=50,
                        verbose_name="Status",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "order",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="activities",
                        to="core.order",
                        verbose_name="Order",
                    ),
                ),
                (
                    "responsible",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="activities_responsible",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Responsible",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
