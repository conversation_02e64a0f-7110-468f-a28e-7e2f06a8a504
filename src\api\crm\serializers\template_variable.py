from rest_framework import serializers
from django.contrib.contenttypes.models import ContentType
from core.models.template import TemplateVariable, TemplateType


class CrmTemplateVariableSerializer(serializers.ModelSerializer):
    class Meta:
        model = TemplateVariable
        fields = [
            "tvid",
            "name",
            "example",
            "description",
            "data_type",
            "data_format",
            "created_at",
            "updated_at",
        ]


class CrmTemplateVariableListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing template variables (for frontend selects)
    """

    class Meta:
        model = TemplateVariable
        fields = CrmTemplateVariableSerializer.Meta.fields
