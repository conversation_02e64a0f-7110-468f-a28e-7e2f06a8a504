import json
import uuid
from datetime import datetime, timezone as dt_timezone
from django.utils import timezone
from core.models import (
    Benefit,
    PaymentMethod,
    LeadSource,
    Order,
    OrderItem,
    User,
    Offering,
)
import phonenumbers

POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"

benefits_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/7_benefits.json"
payment_methods_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/8_payment_methods.json"
lead_sources_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/9_lead_sources.json"
orders_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/11_orders.json"
order_items_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/10_order_items.json"


def populate_benefit_data():
    with open(benefits_json_file_path) as f:
        data = json.load(f)

        benefits = [
            Benefit(
                bid=item["bid"],
                name=item["name"],
            )
            for item in data
        ]

        Benefit.objects.bulk_create(benefits)


def populate_payment_method_data():
    with open(payment_methods_json_file_path) as f:
        data = json.load(f)

        payment_methods = [
            PaymentMethod(
                pmid=item["pmid"],
                name=item["name"],
            )
            for item in data
        ]

        PaymentMethod.objects.bulk_create(payment_methods)


def populate_lead_source_data():
    with open(lead_sources_json_file_path) as f:
        data = json.load(f)

        lead_sources = [
            LeadSource(
                lsid=item["lsid"],
                name=item["name"],
            )
            for item in data
        ]

        LeadSource.objects.bulk_create(lead_sources)


def populate_order_data():
    with open(orders_json_file_path) as f:
        data = json.load(f)
        orders = []

        for item in data:
            # Convert timestamps to timezone-aware datetime objects if they exist
            prospect_at = None
            if item.get("prospect_at"):
                prospect_at = datetime.fromtimestamp(
                    item["prospect_at"] / 1000, tz=dt_timezone.utc
                )

            interested_at = None
            if item.get("interested_at"):
                interested_at = datetime.fromtimestamp(
                    item["interested_at"] / 1000, tz=dt_timezone.utc
                )

            to_pay_at = None
            if item.get("to_pay_at"):
                to_pay_at = datetime.fromtimestamp(
                    item["to_pay_at"] / 1000, tz=dt_timezone.utc
                )

            sold_at = None
            if item.get("sold_at"):
                sold_at = datetime.fromtimestamp(
                    item["sold_at"] / 1000, tz=dt_timezone.utc
                )

            lost_at = None
            if item.get("lost_at"):
                lost_at = datetime.fromtimestamp(
                    item["lost_at"] / 1000, tz=dt_timezone.utc
                )

            # Verify owner exists
            owner = None
            if item.get("owner"):
                try:
                    owner = User.objects.get(pk=item["owner"])
                except User.DoesNotExist:
                    print(
                        f"Warning: Owner with ID {item['owner']} not found "
                        f"for order {item['oid']}"
                    )
                    continue  # Skip this order if owner doesn't exist

            # Verify sales agent exists (optional field)
            sales_agent = None
            if item.get("agent"):
                try:
                    sales_agent = User.objects.get(pk=item["agent"])
                except User.DoesNotExist:
                    print(
                        f"Warning: Sales agent with ID {item['agent']} not found "
                        f"for order {item['oid']}"
                    )
                    # Don't skip the order, just set sales_agent to None

            # Determine if order is international
            is_international = item.get("is_international", False)

            # If is_international is False, try to determine from owner's phone number
            if not is_international and owner and owner.phone_number:
                try:
                    # Parse the phone number
                    parsed_number = phonenumbers.parse(owner.phone_number, None)

                    # Check if it's a valid number
                    if phonenumbers.is_valid_number(parsed_number):
                        # Get the country code - assume Peru (PE) as domestic
                        # You might want to adjust this based on your business logic
                        country_code = phonenumbers.region_code_for_number(
                            parsed_number
                        )
                        # Consider international if not Peruvian phone number
                        is_international = country_code != "PE"
                except (phonenumbers.NumberParseException, Exception) as e:
                    # If we can't parse the number, keep the original value
                    print(
                        f"Warning: Could not parse phone number {owner.phone_number} for order {item['oid']}: {e}"
                    )
                    pass

            # Create Order object with correct fields
            order = Order(
                oid=item["oid"],
                stage=item.get("stage", Order.PROSPECT_STAGE),
                prospect_at=prospect_at,
                interested_at=interested_at,
                to_pay_at=to_pay_at,
                sold_at=sold_at,
                lost_at=lost_at,
                owner=owner,
                sales_agent=sales_agent,
                is_international=is_international,
                has_full_scholarship=item.get("has_full_scholarship", False),
            )
            orders.append(order)

        # Bulk create orders
        Order.objects.bulk_create(orders)

        # Handle many-to-many relationships after creation
        for item in data:
            order = Order.objects.get(oid=item["oid"])

            # Add benefits if they exist
            if item.get("benefits"):
                benefit_ids = item["benefits"].split(",")
                benefits = Benefit.objects.filter(bid__in=benefit_ids)
                order.benefits.set(benefits)

            # Add lead sources if they exist
            if item.get("lead_sources"):
                lead_source_ids = item["lead_sources"].split(",")
                lead_sources = LeadSource.objects.filter(lsid__in=lead_source_ids)
                order.lead_sources.set(lead_sources)


def populate_order_item_data():
    """
    Populate OrderItem data from JSON file.
    Expected JSON structure for order items:
    {
        "order_id": "uuid-string",
        "offering_id": "uuid-string",
        "quantity": 1,
        "custom_amount": null or decimal
    }
    """
    with open(order_items_json_file_path) as f:
        data = json.load(f)
        order_items = []

        for item in data:
            # Verify order exists
            order = None
            if item.get("order_id"):
                try:
                    order = Order.objects.get(pk=item["order_id"])
                except Order.DoesNotExist:
                    print(
                        f"Warning: Order with ID {item['order_id']} not found "
                        f"for order item"
                    )
                    continue  # Skip this order item if order doesn't exist

            # Verify offering exists
            offering = None
            if item.get("offering_id"):
                try:
                    offering = Offering.objects.get(pk=item["offering_id"])
                except Offering.DoesNotExist:
                    print(
                        f"Warning: Offering with ID {item['offering_id']} not found "
                        f"for order item"
                    )
                    continue  # Skip this order item if offering doesn't exist

            order_item = OrderItem(
                order=order,
                offering=offering,
                quantity=item.get("quantity", 1),
                custom_amount=item.get("custom_amount"),
            )
            order_items.append(order_item)

        OrderItem.objects.bulk_create(order_items)
