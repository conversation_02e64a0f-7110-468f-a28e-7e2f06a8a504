"""
Contact Dashboard Views for CRM
Provides analytics endpoints for contact dashboard
"""

from django.db.models import Count
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, IsAdminUser

from core.models import User as Contact
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.contact import CrmDashboardContactFilter
from api.crm.serializers.dashboard.contact import CrmDashboardContactsSerializer
from datetime import datetime
from api.crm.serializers.contact import CrmContactListItemSerializer
from django.db.models import Q
import calendar


class CrmDashboardContactViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Contact Dashboard Analytics
    Provides various endpoints for dashboard statistics and charts
    """

    model_class = Contact
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]
    filterset_class = CrmDashboardContactFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardContactsSerializer

    def get_serializer_class(self):
        if self.action == "invalidate_cache":
            return None
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_contact")
        self.cache_timeout = 120  # 2 minutos - timeout unificado

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for contacts (non-staff users only)
        """
        return Contact.objects.filter(deleted=False, is_staff=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "search": self.request.GET.get("search", ""),
            "created_at_after": self.request.GET.get("created_at_after", ""),
            "created_at_before": self.request.GET.get("created_at_before", ""),
            "ocupation": self.request.GET.get("ocupation", ""),
            "active": self.request.GET.get("active", ""),
            "country": self.request.GET.get("country", ""),
            "months": self.request.GET.get("months", "10"),
        }

    # ==== Utilities ==============

    def _get_report_dates(self):
        """
        Get current and previous period dates based on created_at filters
        If no filters provided, defaults to current month vs previous month
        """

        created_at_after = self.request.GET.get("created_at_after")  # YYYY-MM-DD
        created_at_before = self.request.GET.get("created_at_before")  # YYYY-MM-DD

        def get_current_week():
            # current week from monday to sunday
            current_end = timezone.now()
            current_start = current_end - timezone.timedelta(
                days=current_end.weekday()
            )  # Lunes de esta semana
            current_start = current_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )

            # Previous week (7 days before)
            previous_end = current_start - timezone.timedelta(days=1)
            previous_start = previous_end - timezone.timedelta(days=6)  # 7 días atrás
            previous_start = previous_start.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            previous_end = previous_end.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )
            return current_start, current_end, previous_start, previous_end

        if created_at_after and created_at_before:
            try:
                # Parse dates from filters
                current_start = timezone.datetime.strptime(created_at_after, "%Y-%m-%d")
                current_end = timezone.datetime.strptime(created_at_before, "%Y-%m-%d")

                # Make timezone aware
                current_start = timezone.make_aware(
                    current_start.replace(hour=0, minute=0, second=0, microsecond=0)
                )
                current_end = timezone.make_aware(
                    current_end.replace(
                        hour=23, minute=59, second=59, microsecond=999999
                    )
                )

                # Calculate period duration in days
                period_duration = (current_end.date() - current_start.date()).days + 1

                # Calculate previous period (same duration, ending the day before current start)
                previous_end = current_start - timezone.timedelta(days=1)
                previous_start = previous_end - timezone.timedelta(
                    days=period_duration - 1
                )
                previous_start = previous_start.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                previous_end = previous_end.replace(
                    hour=23, minute=59, second=59, microsecond=999999
                )

            except (ValueError, TypeError):
                # Fallback to current week if invalid format
                current_start, current_end, previous_start, previous_end = (
                    get_current_week()
                )
        else:
            # No date filters provided - default to current week vs previous week
            current_start, current_end, previous_start, previous_end = (
                get_current_week()
            )

        return {
            "current_start": current_start,
            "current_end": current_end,
            "previous_start": previous_start,
            "previous_end": previous_end,
            "period_days": (current_end.date() - current_start.date()).days + 1,
            "current_month": current_start.strftime(
                "%Y-%m"
            ),  # use current start to calculate current month
        }

    def _build_opportunities_filter(self, start_date, end_date):
        """
        Build Q filter for opportunities based on date range across multiple status fields
        """
        opportunity_fields = [
            "orders__prospect_at",
            "orders__interested_at",
            "orders__to_pay_at",
            "orders__sold_at",
        ]

        q_filter = Q()
        for field in opportunity_fields:
            q_filter |= Q(**{f"{field}__gte": start_date, f"{field}__lte": end_date})

        return q_filter

    # === Build dynamic filter options ===
    def _get_queryset_excluding_filters(self, exclude_fields):
        """
        Get queryset, applies all filters EXCEPT field
        """
        queryset = self.get_queryset()
        filter_params = self.request.GET.copy()

        if isinstance(exclude_fields, str):
            exclude_fields = [exclude_fields]

        for field in exclude_fields:
            if field == "created_at":
                # Remove date range filters
                filter_params.pop("created_at_after", None)
                filter_params.pop("created_at_before", None)
            else:
                filter_params.pop(field, None)

        # Apply remaining filters
        filterset = self.filterset_class(filter_params, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    # === GRANULAR CALCULATION FUNCTIONS ===

    def calculate_general_stats(self):
        """
        Calculate general dashboard statistics with dynamic period comparison
        """
        dates = self._get_report_dates()

        # All contacts queryset
        queryset = self.get_queryset()
        total_contacts = queryset.count()

        # Single filtered queryset for all calculations
        filtered_queryset = self.get_filtered_queryset()
        filtered_total = filtered_queryset.count()

        # Current period dates
        current_start = dates["current_start"]
        current_end = dates["current_end"]
        previous_start = dates["previous_start"]
        previous_end = dates["previous_end"]

        # New contacts in current period
        new_current_period = filtered_queryset.filter(
            created_at__gte=current_start, created_at__lte=current_end
        ).count()

        # New contacts in previous period (same duration)
        new_previous_period = queryset.filter(
            created_at__gte=previous_start, created_at__lte=previous_end
        ).count()

        # Calculate percentage change and tendency for new contacts
        new_contacts_percentage = 0
        new_contacts_tendency = "flat"

        if new_previous_period > 0:
            new_contacts_percentage = (
                (new_current_period - new_previous_period) / new_previous_period
            ) * 100
            if new_contacts_percentage > 0:
                new_contacts_tendency = "up"
            elif new_contacts_percentage < 0:
                new_contacts_tendency = "down"
        elif new_current_period > 0:
            new_contacts_tendency = "up"
            new_contacts_percentage = 100

        # Active percentage (from total filtered contacts)
        active_contacts = filtered_queryset.filter(is_active=True).count()
        active_percentage = (
            (active_contacts / filtered_total * 100) if filtered_total > 0 else 0
        )

        # ===== CONVERSION RATE =====
        """
           Conversion rate based on sales oportunities in a date range

            * Numerador: Contactos convertidos en el periodod (sold_at)
            * Denominador: Oportunidades activas en el periodo
             -  Un contacto es una oportunidad activa si alguna de sus órdenes 
                entró en un estado clave durante el rango de fechas.
            => prospect_at, interested_at, to_pay_at, sold_at
        """
        # Excluimos filtro de fecha de creación del contacto, aplican los demás filtros
        conversion_base_queryset = self._get_queryset_excluding_filters("created_at")

        # Contactos converidos en periodo seleccionado (rango de fechas)
        current_period_converted = (
            conversion_base_queryset.filter(
                orders__sold_at__gte=current_start, orders__sold_at__lte=current_end
            )
            .distinct()
            .count()
        )

        # Oportunidades activas en el periodo, usando Q filters
        current_period_oportunities = (
            conversion_base_queryset.filter(
                self._build_opportunities_filter(current_start, current_end)
            )
            .distinct()
            .count()
        )

        current_conversion_rate = (
            (current_period_converted / current_period_oportunities * 100)
            if current_period_oportunities > 0
            else 0
        )

        # Contactos convertidos en periodo anterior (mismo rango en fecha anterior)
        previous_period_converted = (
            conversion_base_queryset.filter(
                orders__sold_at__gte=previous_start, orders__sold_at__lte=previous_end
            )
            .distinct()
            .count()
        )

        previous_period_oportunities = (
            conversion_base_queryset.filter(
                self._build_opportunities_filter(previous_start, previous_end)
            )
            .distinct()
            .count()
        )

        previous_conversion_rate = (
            (previous_period_converted / previous_period_oportunities * 100)
            if previous_period_oportunities > 0
            else 0
        )

        # Calculate percentage change and tendency for conversion rate
        conversion_rate_percentage = 0
        conversion_rate_tendency = "flat"

        if previous_conversion_rate > 0:
            conversion_rate_percentage = (
                (current_conversion_rate - previous_conversion_rate)
                / previous_conversion_rate
            ) * 100
            if conversion_rate_percentage > 0:
                conversion_rate_tendency = "up"
            elif conversion_rate_percentage < 0:
                conversion_rate_tendency = "down"
        elif current_conversion_rate > 0:
            conversion_rate_tendency = "up"
            conversion_rate_percentage = 100

        # Contacts by status (from total filtered)
        contacts_by_status = [
            {"name": "Activos", "value": active_contacts},
            {"name": "Inactivos", "value": filtered_total - active_contacts},
        ]

        return {
            "total_contacts": total_contacts,
            "new_this_month": {
                "value": new_current_period,
                "percentage": f"{abs(new_contacts_percentage):.2f}%",
                "tendency": new_contacts_tendency,
            },
            "active_percentage": round(active_percentage, 2),
            "conversion_rate": {
                "value": round(current_conversion_rate, 2),
                "percentage": f"{abs(conversion_rate_percentage):.2f}%",
                "tendency": conversion_rate_tendency,
            },
            "contacts_by_status": contacts_by_status,
        }

    def calculate_contacts_by_month(self, months=10):
        """
        Calculate contacts created by month, ending with the filtered period
        """
        # dates = self.get_report_dates()
        base_date = timezone.now()

        month_names = [
            "Enero",
            "Febrero",
            "Marzo",
            "Abril",
            "Mayo",
            "Junio",
            "Julio",
            "Agosto",
            "Septiembre",
            "Octubre",
            "Noviembre",
            "Diciembre",
        ]

        data = []
        total_accumulated = 0

        base_query_set = self._get_queryset_excluding_filters("created_at")

        for i in range(months):
            year = base_date.year
            month = base_date.month - (months - 1 - i)

            while month <= 0:
                month += 12
                year -= 1

            month_start = timezone.make_aware(datetime(year, month, 1, 0, 0, 0, 0))

            last_day = calendar.monthrange(year, month)[1]
            month_end = timezone.make_aware(
                datetime(year, month, last_day, 23, 59, 59, 999999)
            )

            month_contacts = base_query_set.filter(
                created_at__gte=month_start, created_at__lte=month_end
            ).count()

            total_accumulated += month_contacts

            data.append(
                {
                    "name": month_names[month - 1],
                    "contacts": month_contacts,
                    "totalAccumulated": total_accumulated,
                }
            )

        return data

    def calculate_contacts_by_country(self, queryset):
        """
        Calculate distribution of contacts by country
        """
        country_counts = (
            queryset.filter(country__isnull=False)
            .values("country")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"country": item["country"], "contacts": item["count"]}
            for item in country_counts
        ]

    def calculate_contacts_by_occupation(self, queryset):
        """
        Calculate distribution of contacts by occupation
        """
        occupation_display = {
            Contact.STUDENT_OCUPATION: "Estudiantes",
            Contact.EMPLOYEE_OCUPATION: "Empleados",
            Contact.INDEPENDENT_OCUPATION: "Independientes",
        }

        occupation_counts = (
            queryset.values("ocupation").annotate(count=Count("uid")).order_by("-count")
        )

        data = []
        for item in occupation_counts:
            if item["ocupation"]:
                display_name = occupation_display.get(
                    item["ocupation"], item["ocupation"]
                )
                data.append({"name": display_name, "value": item["count"]})

        return data

    def calculate_students_by_major(self, queryset):
        """
        Calculate distribution of students by major
        """
        student_queryset = queryset.filter(
            ocupation=Contact.STUDENT_OCUPATION, major__isnull=False
        )

        major_counts = (
            student_queryset.values("major__name")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["major__name"], "value": item["count"]}
            for item in major_counts
        ]

    def calculate_students_by_term(self, queryset):
        """
        Calculate distribution of students by academic term
        """
        student_queryset = queryset.filter(
            ocupation=Contact.STUDENT_OCUPATION, term__isnull=False
        )

        term_counts = (
            student_queryset.values("term__name")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["term__name"], "value": item["count"]} for item in term_counts
        ]

    def calculate_students_by_university(self, queryset):
        """
        Calculate distribution of students by university
        """
        student_queryset = queryset.filter(
            ocupation=Contact.STUDENT_OCUPATION, educational_institution__isnull=False
        )

        university_counts = (
            student_queryset.values("educational_institution__name")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["educational_institution__name"], "value": item["count"]}
            for item in university_counts
        ]

    def calculate_employees_by_company(self, queryset):
        """
        Calculate distribution of employees by company
        """
        employee_queryset = queryset.filter(
            ocupation=Contact.EMPLOYEE_OCUPATION, company__isnull=False
        )

        company_counts = (
            employee_queryset.values("company")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["company"], "value": item["count"]} for item in company_counts
        ]

    # === CONSOLIDATED ENDPOINT ===

    def list(self, request):
        """
        Get all dashboard data in a single consolidated response
        Optimized for frontend dashboard loading with single cache entry
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("list", **cache_params)
        # info = self.cache_manager.debug_cache_info("list", **cache_params)
        # print(f"🔄 Cache info: {info}")

        if cached_data:
            return Response(cached_data)

        # calculate filter options except for same field, eg: country_options = query set filtered but not by country
        country_options = (
            self._get_queryset_excluding_filters("country")
            .filter(country__isnull=False)
            .values_list("country", flat=True)
            .distinct()
        )

        filtered_queryset = self.get_filtered_queryset()
        months = int(request.GET.get("months", 10))

        recent_contacts = filtered_queryset.order_by("-created_at")[:20]
        serialized_recent_contacts = CrmContactListItemSerializer(
            recent_contacts, many=True
        ).data

        # Calculate all data using granular functions
        data = {
            "stats": self.calculate_general_stats(),
            "contacts_by_month": self.calculate_contacts_by_month(months),
            "contacts_by_country": self.calculate_contacts_by_country(
                filtered_queryset
            ),
            "contacts_by_occupation": self.calculate_contacts_by_occupation(
                filtered_queryset
            ),
            "students_by_major": self.calculate_students_by_major(filtered_queryset),
            "students_by_term": self.calculate_students_by_term(filtered_queryset),
            "students_by_university": self.calculate_students_by_university(
                filtered_queryset
            ),
            "employees_by_company": self.calculate_employees_by_company(
                filtered_queryset
            ),
            "recent_activity_contacts": serialized_recent_contacts,
            "filter_options": {
                "countries": country_options,
            },
        }

        # Cache the consolidated result
        self.cache_manager.set("list", data, timeout=self.cache_timeout, **cache_params)

        return Response(data)

    @action(detail=False, methods=["post"], url_path="invalidate-cache")
    def invalidate_cache(self, _):
        self.cache_manager.invalidate()
        self._cache_invalidated = True
        return Response({"message": "Cache invalidated successfully"})
