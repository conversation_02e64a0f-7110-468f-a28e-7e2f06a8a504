from rest_framework import serializers
from core.models import Testimonial
from api.shared.serializers.file import FileSerializer


class WebsiteTestimonialSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="tid", read_only=True)
    author_photo = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Testimonial
        fields = [
            "tid",
            "key",
            "author_name",
            "author_title",
            "content",
            "order",
            "author_photo",
        ]
        read_only_fields = fields
