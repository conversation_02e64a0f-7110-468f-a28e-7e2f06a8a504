"""
Fetch group invite code via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def fetch_invite_code(instance_name: str, group_jid: str) -> Dict[str, Any]:
    """
    Fetch invite code for a WhatsApp group

    Args:
        instance_name: Name of the WhatsApp instance
        group_jid: Group JID identifier

    Returns:
        Dict containing the API response with invite code

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/group/inviteCode/{instance_name}?groupJid={group_jid}", method="GET"
        )

        logger.info(
            f"Invite code fetched successfully for group {group_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch invite code: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching invite code: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
