from rest_framework import serializers
from core.models import Instructor
from api.shared.serializers.file import FileSerializer


class WebsiteInstructorSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="iid", read_only=True)
    profile_photo = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Instructor
        fields = [
            "iid",
            "key",
            "full_name",
            "biography",
            "title",
            "profile_photo",
            "highlighted_info",
            "facebook_url",
            "linkedin_url",
            "instagram_url",
            "order",
        ]
        read_only_fields = fields
