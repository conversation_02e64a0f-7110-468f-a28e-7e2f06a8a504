import enum
from rest_framework import serializers
from core.models import User
from rest_framework.authtoken.models import Token


class UserRoles(str, enum.Enum):
    APP_USER = "APP_USER"
    STAFF = "STAFF"
    SUPERUSER = "SUPERUSER"


class UserDetailSerializer(serializers.ModelSerializer):
    role = serializers.SerializerMethodField(
        help_text="User role",
    )

    class Meta:
        model = User
        fields = [
            "uid",
            "username",
            "email",
            "first_name",
            "last_name",
            "role",
        ]

    def get_role(self, obj):
        if obj.is_superuser:
            return UserRoles.SUPERUSER
        if obj.is_staff:
            return UserRoles.STAFF
        return UserRoles.APP_USER


class TokenSerializer(serializers.ModelSerializer):
    user = UserDetailSerializer(
        read_only=True,
        help_text="User details",
    )
    key = serializers.CharField(
        max_length=40,
        help_text="Auth token",
    )

    class Meta:
        model = Token
        fields = [
            "key",
            "user",
        ]
