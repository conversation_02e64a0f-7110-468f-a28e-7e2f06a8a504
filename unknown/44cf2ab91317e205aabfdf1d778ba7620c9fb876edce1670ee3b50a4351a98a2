from django.conf import settings
import requests
import json
from typing import Any, Dict, List
import requests
import json
from typing import Any, Dict, List

class AppSheetClient:
    def __init__(self, app_id: str, access_key: str, region: str = "www"):
        self.app_id = app_id
        self.access_key = access_key
        self.base_url = f"https://{region}.appsheet.com/api/v2/apps/{app_id}"
        self.headers = {"Content-Type": "application/json"}
    
    def find_items(
        self,
        table_name: str,
        filter_expression: str = "",
        page_size: int = None,
        page: int = None,
        order_by: str = "_RowNumber",
        order_direction: str = "ASC"
    ) -> Dict[str, Any]:
        url = f"{self.base_url}/tables/{table_name}/Action"
        query_params = {"applicationAccessKey": self.access_key}
        
        if filter_expression:
            base_filter = f"Filter({table_name}, {filter_expression})"
        else:
            base_filter = f"Filter({table_name}, true)"
        
        if page_size and page:
            offset = (page - 1) * page_size
            selector = (
                f"TOP({base_filter}, {offset + page_size}) - "
                f"TOP({base_filter}, {offset})"
            )
        else:
            selector = base_filter  # Sin paginación, devuelve todos los datos
        print(selector)
        payload = {
            "Action": "Find",
            "Properties": {
                "Locale": "es-ES",
                "Selector": selector,
                "Timezone": "America/Lima"
            },
            "Rows": []
        }
        
        try:
            response = requests.post(url, headers=self.headers, params=query_params, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except (requests.RequestException, json.JSONDecodeError):
            return []
    def get_total_count(self, table_name: str, filter_expression: str = "") -> int:
        url = f"{self.base_url}/tables/{table_name}/Action"
        query_params = {"applicationAccessKey": self.access_key}
        
        if filter_expression:
            selector = f"Filter({table_name}, {filter_expression})"
        else:
            selector = f"Filter({table_name}, true)"
        
        payload = {
            "Action": "Find",
            "Properties": {
                "Locale": "es-ES",
                "Selector": selector,
                "Timezone": "America/Santiago"
            },
            "Rows": []
        }
        
        try:
            response = requests.post(url, headers=self.headers, params=query_params, data=json.dumps(payload))
            response.raise_for_status()
            
            try:
                result = response.json()
                if isinstance(result, list):
                    return len(result)
                else:
                    return 0
            except json.JSONDecodeError:
                return 0
        except requests.RequestException:
            return 0

appsheet_client = AppSheetClient(app_id=settings.APPSHEET_APP_ID, access_key=settings.APPSHEET_API_KEY)
