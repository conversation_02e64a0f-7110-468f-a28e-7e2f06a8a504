"""
Mark messages as read via Evolution API
"""

import logging
from typing import Dict, Any, List, TypedDict
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


class MessageToRead(TypedDict):
    """Message to mark as read structure"""

    remote_jid: str
    from_me: bool
    message_id: str


def read_messages(instance_name: str, messages: List[MessageToRead]) -> Dict[str, Any]:
    """
    Mark messages as read

    Args:
        instance_name: Name of the WhatsApp instance
        messages: List of messages to mark as read

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If no messages are provided
    """
    try:
        if not messages:
            raise ValueError("At least one message is required")

        # Build messages list
        read_messages_list = []
        for msg in messages:
            read_messages_list.append(
                {
                    "remoteJid": msg["remote_jid"],
                    "fromMe": msg["from_me"],
                    "id": msg["message_id"],
                }
            )

        # Build request body
        body = {
            "readMessages": read_messages_list,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/markMessageAsRead/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Messages marked as read successfully via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to mark messages as read: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for read_messages: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error marking messages as read: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def read_single_message(
    instance_name: str, remote_jid: str, from_me: bool, message_id: str
) -> Dict[str, Any]:
    """
    Mark a single message as read

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID
        from_me: Whether the message was sent by me
        message_id: ID of the message to mark as read

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    return read_messages(
        instance_name,
        [{"remote_jid": remote_jid, "from_me": from_me, "message_id": message_id}],
    )
