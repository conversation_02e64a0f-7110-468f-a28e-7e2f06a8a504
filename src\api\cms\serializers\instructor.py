from rest_framework import serializers
from core.models import Instructor, User
from core.constants import (
    INSTRUCTOR_PROFILE_PHOTO_WIDTH,
    INSTRUCTOR_PROFILE_PHOTO_HEIGHT,
)
from django.utils import timezone
from api.utils import (
    perform_create_image_file,
)
from api.shared.serializers.file import FileSerializer


class CmsInstructorSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="iid", read_only=True)
    profile_photo = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Instructor
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]


class CmsCreateInstructorSerializer(CmsInstructorSerializer):
    profile_photo_file = serializers.FileField(write_only=True, required=False)

    def create(self, validated_data):
        profile_photo_file = validated_data.pop("profile_photo_file", None)
        instructor = Instructor.objects.create(**validated_data)

        if profile_photo_file:
            profile_photo = perform_create_image_file(
                profile_photo_file,
                INSTRUCTOR_PROFILE_PHOTO_WIDTH,
                INSTRUCTOR_PROFILE_PHOTO_HEIGHT,
            )
            instructor.profile_photo = profile_photo

        instructor.save()
        return instructor


class CmsUpdateInstrutorSerializer(CmsInstructorSerializer):
    profile_photo_file = serializers.FileField(write_only=True, required=False)
    delete_profile_photo = serializers.BooleanField(write_only=True, required=False)

    def update(self, instance, validated_data):
        profile_photo_file = validated_data.pop("profile_photo_file", None)
        delete_profile_photo = validated_data.pop("delete_profile_photo", False)

        instructor = super().update(instance, validated_data)

        if delete_profile_photo:
            self.__perform_delete_profile_photo(
                instructor, self.context["request"].user
            )

        if profile_photo_file:
            if instructor.profile_photo:
                self.__perform_delete_profile_photo(
                    instructor, self.context["request"].user
                )

            profile_photo = perform_create_image_file(
                profile_photo_file,
                INSTRUCTOR_PROFILE_PHOTO_WIDTH,
                INSTRUCTOR_PROFILE_PHOTO_HEIGHT,
            )
            instructor.profile_photo = profile_photo

        instructor.save()
        return instructor

    def __perform_delete_profile_photo(
        self,
        instructor: Instructor,
        user: User,
    ):
        instructor.profile_photo.deleted = True
        instructor.profile_photo.deleted_at = timezone.now()
        instructor.profile_photo.deleted_by = user
        instructor.profile_photo.save()
        instructor.profile_photo = None
