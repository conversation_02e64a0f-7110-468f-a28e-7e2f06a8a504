from rest_framework import viewsets
from core.models import BlogPost, BlogCategory
from api.mixins import SwaggerTagMixin
from api.website.serializers.blog import WebsiteBlogCategorySerializer
from api.paginations import StandardResultsPagination

class WebsiteBlogCategoryViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    """
    ViewSet for blog categories.
    Only returns categories that have published blog posts.
    """

    model_class = BlogCategory
    pagination_class = StandardResultsPagination

    queryset = BlogCategory.objects.filter(
        blog_posts__status=BlogPost.PUBLISHED,
        blog_posts__deleted=False,
    ).distinct()

    serializer_class = WebsiteBlogCategorySerializer
    lookup_field = "slug"

    swagger_tags = ["Blog Categories"]
