services:
  redis:
    image: redis:7-alpine
    container_name: portals-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - ceu-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  redis_data:

networks:
  ceu-network:
    external: true
