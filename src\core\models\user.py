import uuid
from django.db import models
from django.contrib.auth.models import AbstractUser
from core.models.base import AuditBaseModel
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class User(AbstractUser, AuditBaseModel):
    """
    User model. This model would be used to store user information, but in CRM
    this would be used to store the Contact information.
    """

    STUDENT_OCUPATION = "student"
    EMPLOYEE_OCUPATION = "employee"
    INDEPENDENT_OCUPATION = "independent"

    OCUPATION_CHOICES = (
        (STUDENT_OCUPATION, _("Student")),
        (EMPLOYEE_OCUPATION, _("Employee")),
        (INDEPENDENT_OCUPATION, _("Independent")),
    )

    uid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    student_id = models.CharField(
        null=False,
        blank=False,
        unique=True,
        max_length=9,
        help_text=_("Generated Sequential Student ID"),
        db_index=True,
    )

    email = models.EmailField(
        null=True,
        blank=True,
        unique=True,
        max_length=128,
        db_index=True,
        help_text=_("Email address"),
    )

    phone_number = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        unique=True,
        db_index=True,
        help_text=_("Phone number & country code"),
    )
    id_number = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        help_text=_("ID number or passport number"),
    )
    profile_photo = models.ForeignKey(
        "core.File",
        on_delete=models.SET_NULL,
        related_name="users",
        blank=True,
        null=True,
        verbose_name=_("Profile Photo"),
    )

    ocupation = models.CharField(
        max_length=52,
        blank=True,
        null=True,
        choices=OCUPATION_CHOICES,
        help_text=_("Ocupation"),
    )
    company = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("Company"),
    )
    role = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("Role"),
    )
    educational_institution = models.ForeignKey(
        "core.EducationalInstitution",
        on_delete=models.SET_NULL,
        related_name="students",
        blank=True,
        null=True,
        verbose_name=_("EducationalInstitution"),
    )
    city = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("City"),
    )
    country = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        help_text=_("Country"),
    )
    major = models.ForeignKey(
        "core.Major",
        on_delete=models.SET_NULL,
        related_name="students",
        blank=True,
        null=True,
        verbose_name=_("Major"),
    )
    term = models.ForeignKey(
        "core.Term",
        on_delete=models.SET_NULL,
        related_name="students",
        blank=True,
        null=True,
        verbose_name=_("Term"),
    )

    google_contact_id = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        help_text=_("Google Contact ID"),
    )
    last_google_sync = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_("Last Google Sync"),
    )

    def __str__(self):
        if not self.first_name and not self.last_name:
            return self.username
        return self.first_name + " " + self.last_name

    def save(self, *args, **kwargs):
        if not self.student_id:
            year = self.created_at.year if self.created_at else timezone.now().year
            # Get the latest student_id for this year
            last_user = (
                User.objects.filter(student_id__startswith=str(year))
                .order_by("-student_id")
                .first()
            )

            if last_user:
                # Extract the sequential number from the last student_id and add 1
                last_number = int(last_user.student_id[4:])
                next_number = last_number + 1
            else:
                # If there are no users for this year, start with 1
                next_number = 1

            # Create the new student_id with uniqueness validation
            student_id = f"{year}{next_number:05d}"

            # Ensure uniqueness in case of race conditions or data inconsistencies
            while User.objects.filter(student_id=student_id).exists():
                next_number += 1
                student_id = f"{year}{next_number:05d}"

            self.student_id = student_id

        super().save(*args, **kwargs)

    class Meta:
        permissions = [
            ("manage_staff_users", "Can manage staff users"),
        ]


class Major(AuditBaseModel):
    """
    Major model. This model would be used to store major information.
    """

    mid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=128,
        help_text=_("Name"),
    )


class Term(AuditBaseModel):
    """ "
    Term model. This model would be used to store term information.
    """

    tid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=32,
        help_text=_("Name"),
    )
