from django.contrib import admin
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django_json_widget.widgets import JSONEditorWidget
from core.models import (
    Offering,
    OfferingModule,
    ModuleCourse,
    Topic,
)


class TopicInline(admin.TabularInline):
    model = Topic
    extra = 1
    fields = ["title"]


class ModuleCourseInline(admin.TabularInline):
    model = ModuleCourse
    extra = 1
    fields = ["title"]
    inlines = [TopicInline]


class OfferingModuleInline(admin.TabularInline):
    model = OfferingModule
    extra = 1
    fields = ["title"]
    inlines = [ModuleCourseInline]


@admin.register(Offering)
class OfferingAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "name",
        "start_date",
        "type",
        "created_at",
        "stage",
    ]
    list_filter = (
        "deleted",
        "stage",
    )
    search_fields = ("name",)

    inlines = [OfferingModuleInline]

    formfield_overrides = {
        JSONField: {
            "widget": JSONEditorWidget,
        }
    }


@admin.register(OfferingModule)
class OfferingModuleAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]
    inlines = [ModuleCourseInline]


@admin.register(ModuleCourse)
class ModuleCourseAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]
    inlines = [TopicInline]


@admin.register(Topic)
class TopicAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]
