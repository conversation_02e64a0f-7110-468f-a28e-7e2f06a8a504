from rest_framework import viewsets
from core.models.template import TemplateVariable
from api.crm.serializers.template_variable import (
    CrmTemplateVariableSerializer,
    CrmTemplateVariableListSerializer,
)
from api.crm.filters.template_variable import (
    CrmTemplateVariableFilter,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from api.paginations import StandardResultsPagination


class CrmTemplateVariableViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing template variables
    """

    swagger_tags = ["Templates"]

    serializer_class = CrmTemplateVariableSerializer
    pagination_class = StandardResultsPagination

    queryset = TemplateVariable.objects.filter(deleted=False).order_by("name")
    lookup_field = "tvid"

    filterset_class = CrmTemplateVariableFilter
    ordering_fields = ["name", "created_at", "updated_at"]

    http_method_names = ["get"]

    # List only is there are template type
    def get_queryset(self):
        queryset = super().get_queryset()

        if self.request.GET.get("template_type"):
            queryset = queryset.filter(
                template_type=self.request.GET.get("template_type")
            )
            return queryset
        return queryset.none()
