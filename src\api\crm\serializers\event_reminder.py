from rest_framework import serializers
from core.models import EventReminder, EventScheduleEnrollment, Template
from api.crm.serializers.event_schedule import CrmEventScheduleEnrollmentUserSerializer


class CrmEventReminderEnrollmentSerializer(serializers.ModelSerializer):
    """Simplified serializer for enrollment in event reminders"""

    full_name = serializers.SerializerMethodField()
    user = CrmEventScheduleEnrollmentUserSerializer(read_only=True)

    class Meta:
        model = EventScheduleEnrollment
        fields = [
            "id",
            "full_name",
            "email",
            "phone_number",
            "user",
        ]

    def get_full_name(self, obj):
        """Get full name from user or enrollment data"""
        if obj.user:
            return (
                obj.user.get_full_name()
                if obj.user.get_full_name() != ""
                else "Sin nombre"
            )
        elif obj.first_name or obj.last_name:
            return f"{obj.first_name or ''} {obj.last_name or ''}".strip()
        return "Sin nombre"


class CrmEventReminderTemplateSerializer(serializers.ModelSerializer):
    """Simplified serializer for WhatsApp template in event reminders"""

    class Meta:
        model = Template
        fields = [
            "tid",
            "name",
            "ext_reference",
        ]


class CrmEventReminderSerializer(serializers.ModelSerializer):
    """Serializer for listing event reminders"""

    # Get template and scheduling info from event schedule
    whatsapp_template = serializers.SerializerMethodField()
    scheduled_datetime_whatsapp = serializers.SerializerMethodField()
    scheduled_datetime_email = serializers.SerializerMethodField()
    enrollment = serializers.SerializerMethodField()

    def get_enrollment(self, obj):
        """Get simplified enrollment data"""
        if obj.enrollment:
            return CrmEventReminderEnrollmentSerializer(obj.enrollment).data
        return None

    def get_whatsapp_template(self, obj):
        """Get simplified WhatsApp template from event schedule"""
        if (
            obj.enrollment
            and obj.enrollment.event_schedule
            and obj.enrollment.event_schedule.whatsapp_template
        ):
            template = obj.enrollment.event_schedule.whatsapp_template
            return CrmEventReminderTemplateSerializer(template).data
        return None

    def get_scheduled_datetime_whatsapp(self, obj):
        """Get WhatsApp scheduled datetime from event schedule"""
        if obj.enrollment and obj.enrollment.event_schedule:
            return obj.enrollment.event_schedule.scheduled_datetime_whatsapp
        return None

    def get_scheduled_datetime_email(self, obj):
        """Get email scheduled datetime from event schedule"""
        if obj.enrollment and obj.enrollment.event_schedule:
            return obj.enrollment.event_schedule.scheduled_datetime_email
        return None

    class Meta:
        model = EventReminder
        fields = [
            "rid",
            "enrollment",
            "whatsapp_template",
            "scheduled_datetime_whatsapp",
            "scheduled_datetime_email",
            "status_whatsapp",
            "status_email",
            "sent_at_whatsapp",
            "sent_at_email",
            "last_error_whatsapp",
            "last_error_email",
            "retry_count_whatsapp",
            "retry_count_email",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "rid",
            "whatsapp_template",
            "scheduled_datetime_whatsapp",
            "scheduled_datetime_email",
            "sent_at_whatsapp",
            "sent_at_email",
            "last_error_whatsapp",
            "last_error_email",
            "retry_count_whatsapp",
            "retry_count_email",
            "created_at",
            "updated_at",
        ]


class CrmCreateEventReminderSerializer(serializers.ModelSerializer):
    """Serializer for creating event reminders"""

    class Meta:
        model = EventReminder
        fields = [
            "enrollment",
        ]

    def validate(self, data):
        """Validate that enrollment has an event schedule with WhatsApp template"""
        enrollment = data.get("enrollment")
        if (
            enrollment
            and enrollment.event_schedule
            and not enrollment.event_schedule.whatsapp_template
        ):
            raise serializers.ValidationError(
                "Event schedule must have a WhatsApp template configured"
            )
        return data


class CrmEventReminderMetricsSerializer(serializers.Serializer):
    """Serializer for event reminder sending metrics"""

    total_reminders = serializers.IntegerField(
        help_text="Total number of event reminders"
    )
    total_pending = serializers.IntegerField(
        help_text="Total pending invitations (WhatsApp + Email)"
    )
    total_sent = serializers.IntegerField(
        help_text="Total sent invitations (WhatsApp + Email)"
    )
    total_failed = serializers.IntegerField(
        help_text="Total failed invitations (WhatsApp + Email)"
    )
    whatsapp = serializers.DictField(help_text="WhatsApp specific metrics")
    email = serializers.DictField(help_text="Email specific metrics")


class CrmEventReminderInvitationTypeMetricsSerializer(serializers.Serializer):
    """Serializer for invitation type specific metrics (WhatsApp or Email)"""

    total_pending = serializers.IntegerField(
        help_text="Total pending invitations for this type"
    )
    total_sent = serializers.IntegerField(
        help_text="Total sent invitations for this type"
    )
    total_failed = serializers.IntegerField(
        help_text="Total failed invitations for this type"
    )


class CrmUpdateEventReminderSerializer(serializers.ModelSerializer):
    """Serializer for updating event reminders - only status fields can be updated"""

    class Meta:
        model = EventReminder
        fields = [
            "status_whatsapp",
            "status_email",
        ]
