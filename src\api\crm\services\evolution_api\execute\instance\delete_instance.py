"""
Delete WhatsApp instance via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def delete_instance(instance_name: str) -> Dict[str, Any]:
    """
    Delete a WhatsApp instance
    
    Args:
        instance_name: Name of the instance to delete
        
    Returns:
        Dict containing the API response
        
    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/instance/delete/{instance_name}",
            method="DELETE"
        )
        
        logger.info(f"Instance '{instance_name}' deleted successfully")
        return response
        
    except EvolutionAPIError as e:
        logger.error(f"Failed to delete instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting instance '{instance_name}': {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
