from rest_framework import viewsets
from core.models import Offering
from api.crm.serializers.offering import CrmOfferingSerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters

class CrmOfferingViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = Offering
    queryset = Offering.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmOfferingSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    pagination_class = StandardResultsPagination

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at", "name"]
    search_fields = [
        "name",
        "long_name",
        "code_name",
    ]

    swagger_tags = ["Offering"]
