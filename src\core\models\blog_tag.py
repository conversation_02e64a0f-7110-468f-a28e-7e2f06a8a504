import uuid
from django.db import models
from core.models.base import AuditBaseModel
from django.core.validators import RegexValidator

class BlogTag(AuditBaseModel):
    btid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Name",
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        verbose_name="Slug",
    )

    badge_color = models.CharField(
        max_length=7,
        blank=True,
        null=True,
        default="#FFFFFF",
        verbose_name="Badge Color",
        help_text="Hexadecimal color code for the badge (e.g., #FFFFFF).",
        validators=[
            RegexValidator(
                regex=r"^#(?:[0-9a-fA-F]{3}){1,2}$",
                message="Enter a valid hexadecimal color code (e.g., #FFFFFF or #FFF).",
            )
        ]
    )
    
    description = models.TextField(blank=True, null=True, verbose_name="Description")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Blog Tag"
        verbose_name_plural = "Blog Tags"
