"""
Permisos personalizados
=======================

Define permisos específicos del CRM que no están asociados a modelos de Django.
Ejemplos: acceso a dashboard granular, configuraciones específicas, exportación de datos, etc.

Convención:
-----------
- Codename: <action>_<module>_<resource> (ej: "crm_view_dashboard")
- Nombre: "<MODULE> | Can <action> <resource>" (ej: "CRM | Can view dashboard")

Uso:
----
- Se asignan a usuarios/grupos desde el admin de Django
- Se sincronizan ejecutando el comando make sync-permissions
"""

CUSTOM_PERMISSIONS_DEFINITION = [
    # Dashboard
    ("view_crm_dashboard_contacts", "CRM | Can view contacts dashboard	"),
    ("view_crm_dashboard_sales", "CRM | Can view sales dashboard"),
    ("view_crm_dashboard_payments", "CRM | Can view payments dashboard"),
    ("view_crm_dashboard_events", "CRM | Can view events dashboard"),
]
