"""
Set Chatwoot integration via Evolution API
"""

import logging
from typing import Dict, Any, Optional, List
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def set_chatwoot(
    instance_name: str,
    account_id: str,
    token: str,
    url: str,
    sign_msg: bool = False,
    reopen_conversation: bool = False,
    conversation_pending: bool = False,
    name_inbox: str = "",
    merge_brazil_contacts: bool = False,
    import_contacts: bool = False,
    import_messages: bool = False,
    days_limit_import_messages: int = 0,
    auto_create: bool = False,
    organization: str = "",
    logo: str = "",
    ignore_jids: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Set Chatwoot integration for WhatsApp instance

    Args:
        instance_name: Name of the WhatsApp instance
        account_id: Chatwoot account ID
        token: Chatwoot API token
        url: Chatwoot server URL
        sign_msg: Whether to sign messages
        reopen_conversation: Whether to reopen conversations
        conversation_pending: Whether to set conversations as pending
        name_inbox: Name for the inbox
        merge_brazil_contacts: Whether to merge Brazil contacts
        import_contacts: Whether to import contacts
        import_messages: Whether to import messages
        days_limit_import_messages: Days limit for importing messages
        auto_create: Whether to auto-create conversations
        organization: Organization name
        logo: Logo URL
        ignore_jids: List of JIDs to ignore

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "enabled": True,
            "accountId": account_id,
            "token": token,
            "url": url,
            "signMsg": sign_msg,
            "reopenConversation": reopen_conversation,
            "conversationPending": conversation_pending,
            "nameInbox": name_inbox,
            "mergeBrazilContacts": merge_brazil_contacts,
            "importContacts": import_contacts,
            "importMessages": import_messages,
            "daysLimitImportMessages": days_limit_import_messages,
            "signDelimiter": "\n",
            "autoCreate": auto_create,
            "organization": organization,
            "logo": logo,
            "ignoreJids": ignore_jids or [""],
        }

        # Make the request
        response = evolution_request(
            uri=f"/chatwoot/set/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Chatwoot integration configured successfully for instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(
            f"Failed to set Chatwoot integration for instance '{instance_name}': {e.message}"
        )
        raise
    except Exception as e:
        logger.error(f"Unexpected error setting Chatwoot integration: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def find_chatwoot(instance_name: str) -> Dict[str, Any]:
    """
    Find Chatwoot integration configuration for WhatsApp instance

    Args:
        instance_name: Name of the WhatsApp instance

    Returns:
        Dict containing the API response with Chatwoot configuration

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/chatwoot/find/{instance_name}", method="GET"
        )

        logger.info(f"Chatwoot configuration retrieved for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(
            f"Failed to find Chatwoot configuration for instance '{instance_name}': {e.message}"
        )
        raise
    except Exception as e:
        logger.error(f"Unexpected error finding Chatwoot configuration: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
