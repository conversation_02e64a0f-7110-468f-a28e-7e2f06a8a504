# src/api/classroom/views/auth.py

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema

from core.models import User, EducationalInstitution, Major, Term
from api.classroom.serializers.auth import (
    ProfileSerializer,
    UpdateProfileSerializer,
    InstitutionSelectSerializer,
    MajorSelectSerializer,
    TermSelectSerializer,
)
from api.mixins import SwaggerTagMixin
from rest_framework import serializers
from rest_framework.authentication import TokenAuthentication


class ProfileSelectsSerializer(serializers.Serializer):
    institutions = InstitutionSelectSerializer(many=True)
    majors = MajorSelectSerializer(many=True)
    terms = TermSelectSerializer(many=True)


class ClassroomAuthViewSet(viewsets.GenericViewSet, SwaggerTagMixin):
    """
    ViewSet para autenticación y perfil de usuario en Classroom
    """

    queryset = User.objects.filter(deleted=False)
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [TokenAuthentication]
    swagger_tags = ["Auth"]

    @swagger_auto_schema(
        methods=["GET"],
        responses={status.HTTP_200_OK: ProfileSerializer()},
    )
    @action(detail=False, methods=["GET"], url_path="profile")
    def get_profile(self, request):
        serializer = ProfileSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["PATCH"],
        request_body=UpdateProfileSerializer,
        responses={200: "Perfil actualizado correctamente."},
    )
    @action(detail=False, methods=["PATCH"], url_path="profile/update")
    def update_profile(self, request):
        serializer = UpdateProfileSerializer(
            instance=request.user,
            data=request.data,
            partial=True,
            context={"request": request},
        )

        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "Perfil actualizado correctamente."},
                status=status.HTTP_200_OK,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        methods=["GET"],
        responses={status.HTTP_200_OK: ProfileSelectsSerializer()},
        operation_description="Devuelve listas de opciones para instituciones, carreras y ciclos.",
    )
    @action(detail=False, methods=["GET"], url_path="profile/selects")
    def profile_selects(self, request):
        institutions = EducationalInstitution.objects.all()
        majors = Major.objects.all()
        terms = Term.objects.all()

        data = {
            "institutions": InstitutionSelectSerializer(institutions, many=True).data,
            "majors": MajorSelectSerializer(majors, many=True).data,
            "terms": TermSelectSerializer(terms, many=True).data,
        }
        return Response(data, status=status.HTTP_200_OK)
