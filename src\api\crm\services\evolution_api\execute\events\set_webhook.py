"""
Set webhook configuration via Evolution API
"""

import logging
from typing import Dict, Any, Optional, List
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def set_webhook(
    instance_name: str,
    enabled: bool,
    webhook_url: str,
    webhook_by_events: bool = False,
    webhook_base64: bool = False,
    webhook_events: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Set webhook configuration for WhatsApp instance

    Args:
        instance_name: Name of the WhatsApp instance
        enabled: Whether webhook is enabled
        webhook_url: URL to receive webhook events
        webhook_by_events: Whether to send events separately
        webhook_base64: Whether to encode media in base64
        webhook_events: List of events to listen for

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "webhook": {
                "enabled": enabled,
                "url": webhook_url or "vazio",
                "webhookByEvents": webhook_by_events,
                "webhookBase64": webhook_base64,
                "events": webhook_events or [],
            }
        }

        # Make the request
        response = evolution_request(
            uri=f"/webhook/set/{instance_name}", method="POST", data=body
        )

        logger.info(f"Webhook configured successfully for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(
            f"Failed to set webhook for instance '{instance_name}': {e.message}"
        )
        raise
    except Exception as e:
        logger.error(f"Unexpected error setting webhook: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def find_webhook(instance_name: str) -> Dict[str, Any]:
    """
    Find webhook configuration for WhatsApp instance

    Args:
        instance_name: Name of the WhatsApp instance

    Returns:
        Dict containing the API response with webhook configuration

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(uri=f"/webhook/find/{instance_name}", method="GET")

        logger.info(f"Webhook configuration retrieved for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(
            f"Failed to find webhook for instance '{instance_name}': {e.message}"
        )
        raise
    except Exception as e:
        logger.error(f"Unexpected error finding webhook: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
