"""
Send presence status via Evolution API
"""

import logging
from typing import Dict, Any, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

PresenceType = Literal["available", "unavailable", "composing", "recording", "paused"]


def send_presence(
    instance_name: str, remote_jid: str, presence: PresenceType, delay: int = 0
) -> Dict[str, Any]:
    """
    Send presence status to a chat

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send presence to
        presence: Presence status (available, unavailable, composing, recording, paused)
        delay: Delay in milliseconds (default: 0)

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If presence type is invalid
    """
    try:
        # Validate presence
        valid_presences = [
            "available",
            "unavailable",
            "composing",
            "recording",
            "paused",
        ]
        if presence not in valid_presences:
            raise ValueError(
                f"Invalid presence type. Must be one of: {valid_presences}"
            )

        # Build request body
        body = {
            "number": remote_jid,
            "presence": presence,
            "delay": delay,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/sendPresence/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Presence '{presence}' sent to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send presence: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for send_presence: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error sending presence: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
