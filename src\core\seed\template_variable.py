import json
import logging
from typing import <PERSON>ple
from django.contrib.contenttypes.models import ContentType
from core.models.template import TemplateVariable, TemplateType
from core.models.event import EventScheduleEnrollment

logger = logging.getLogger(__name__)

SEED_ASSETS_DIR = "core/seed/assets"
template_variables_json_file_path = f"{SEED_ASSETS_DIR}/template_variables.json"


def populate_template_variable_data() -> Tuple[int, int]:
    """
    Populate template variables from JSON file

    Returns:
        Tuple of (created_count, updated_count)
    """
    logger.info("Starting template variables population...")

    with open(template_variables_json_file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    total_created = 0
    total_updated = 0

    # First, create template types
    template_types_created, template_types_updated = _create_template_types(
        data.get("template_types", [])
    )
    total_created += template_types_created
    total_updated += template_types_updated

    # Then, create template variables
    variables_created, variables_updated = _create_template_variables(
        data.get("template_variables", [])
    )
    total_created += variables_created
    total_updated += variables_updated

    logger.info(
        f"Template variables population completed. "
        f"Created: {total_created}, Updated: {total_updated}"
    )

    return total_created, total_updated


def _create_template_types(template_types_data: list) -> Tuple[int, int]:
    """
    Create template types from JSON data

    Args:
        template_types_data: List of template type definitions

    Returns:
        Tuple of (created_count, updated_count)
    """
    created_count = 0
    updated_count = 0

    for tt_data in template_types_data:
        content_type_model = tt_data["content_type_model"]

        # Get the content type based on model name
        if content_type_model == "EventScheduleEnrollment":
            content_type = ContentType.objects.get_for_model(EventScheduleEnrollment)
        else:
            logger.warning(f"Unknown content type model: {content_type_model}")
            continue

        template_type, created = TemplateType.objects.get_or_create(
            content_type=content_type, defaults={"name": tt_data["name"]}
        )

        if created:
            created_count += 1
            logger.debug(f"Created template type: {tt_data['name']}")
        else:
            # Update existing template type
            template_type.name = tt_data["name"]
            template_type.save()
            updated_count += 1
            logger.debug(f"Updated template type: {tt_data['name']}")

    return created_count, updated_count


def _create_template_variables(template_variables_data: list) -> Tuple[int, int]:
    """
    Create template variables from JSON data

    Args:
        template_variables_data: List of template variable definitions

    Returns:
        Tuple of (created_count, updated_count)
    """
    created_count = 0
    updated_count = 0

    for var_data in template_variables_data:
        content_type_model = var_data["template_type_content_type"]

        # Get the content type and template type
        if content_type_model == "EventScheduleEnrollment":
            content_type = ContentType.objects.get_for_model(EventScheduleEnrollment)
        else:
            logger.warning(f"Unknown content type model: {content_type_model}")
            continue

        try:
            template_type = TemplateType.objects.get(content_type=content_type)
        except TemplateType.DoesNotExist:
            logger.error(
                f"Template type not found for content type: {content_type_model}"
            )
            continue

        # Map data types from JSON to model constants
        data_type_mapping = {
            "STRING": TemplateVariable.STRING,
            "ARRAY": TemplateVariable.ARRAY,
            "DATETIME": TemplateVariable.DATETIME,
        }

        data_format_mapping = {
            "TIME_12H": TemplateVariable.TIME_12H,
            "DATE_WEEKDAY_MEDIUM": TemplateVariable.DATE_WEEKDAY_MEDIUM,
        }

        defaults = {
            "path": var_data["path"],
            "example": var_data["example"],
            "description": var_data["description"],
            "data_type": data_type_mapping.get(
                var_data["data_type"], TemplateVariable.STRING
            ),
            "data_format": data_format_mapping.get(var_data.get("data_format")),
        }

        template_var, created = TemplateVariable.objects.get_or_create(
            name=var_data["name"],
            template_type=template_type,
            defaults=defaults,
        )

        if created:
            created_count += 1
            logger.debug(f"Created template variable: {var_data['name']}")
        else:
            # Update existing variable
            for key, value in defaults.items():
                setattr(template_var, key, value)
            template_var.save()
            updated_count += 1
            logger.debug(f"Updated template variable: {var_data['name']}")

    return created_count, updated_count
