# Generated by Django 5.0.6 on 2025-06-01 00:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0009_remove_student_deleted_by_remove_student_user_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="offering",
            name="short_name",
        ),
        migrations.RemoveField(
            model_name="order",
            name="paid_at",
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="ext_event_reference",
            field=models.CharField(
                blank=True,
                help_text="Reference for the external event schedule",
                max_length=255,
                null=True,
                verbose_name="External Event Reference",
            ),
        ),
        migrations.AddField(
            model_name="offering",
            name="long_name",
            field=models.CharField(
                blank=True,
                default=None,
                help_text="Long name of the academic offering.",
                max_length=128,
                null=True,
                verbose_name="Long Name",
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="order",
            name="sold_at",
            field=models.DateTimeField(blank=True, null=True, verbose_name="Sold At"),
        ),
        migrations.AddField(
            model_name="partnership",
            name="delegate",
            field=models.ForeignKey(
                blank=True,
                help_text="User who is the delegate for the partnership",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="partnerships",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="payment",
            name="is_first_payment",
            field=models.BooleanField(default=False, verbose_name="Is First Payment"),
        ),
        migrations.AddField(
            model_name="payment",
            name="is_refund",
            field=models.BooleanField(default=False, verbose_name="Is Refund"),
        ),
        migrations.AddField(
            model_name="payment",
            name="observations",
            field=models.TextField(blank=True, null=True, verbose_name="Observations"),
        ),
        migrations.AddField(
            model_name="payment",
            name="scheduled_payment_date",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Scheduled Payment Date"
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="city",
            field=models.CharField(
                blank=True, help_text="City", max_length=128, null=True
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="country",
            field=models.CharField(
                blank=True, help_text="Country", max_length=128, null=True
            ),
        ),
        migrations.AlterField(
            model_name="order",
            name="stage",
            field=models.CharField(
                choices=[
                    ("prospect", "Prospect"),
                    ("interested", "Interested"),
                    ("to_pay", "To Pay"),
                    ("sold", "Sold"),
                    ("lost", "Lost"),
                ],
                default="prospect",
                max_length=50,
                verbose_name="Stage",
            ),
        ),
    ]
