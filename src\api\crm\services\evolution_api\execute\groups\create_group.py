"""
Create WhatsApp group via Evolution API
"""

import logging
from typing import Dict, Any, List, Union
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def create_group(
    instance_name: str,
    subject: str,
    description: str,
    participants: Union[List[str], str],
) -> Dict[str, Any]:
    """
    Create a WhatsApp group

    Args:
        instance_name: Name of the WhatsApp instance
        subject: Group name/subject
        description: Group description
        participants: List of phone numbers or comma-separated string of participants

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If participants list is empty
    """
    try:
        # Handle participants input
        if isinstance(participants, str):
            participants_list = [
                p.strip() for p in participants.split(",") if p.strip()
            ]
        else:
            participants_list = participants

        if not participants_list:
            raise ValueError("At least one participant is required to create a group")

        # Build request body
        body = {
            "subject": subject,
            "description": description,
            "participants": participants_list,
        }

        # Make the request
        response = evolution_request(
            uri=f"/group/create/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Group '{subject}' created successfully in instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to create group '{subject}': {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for create_group: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error creating group '{subject}': {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
