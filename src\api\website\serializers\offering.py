from rest_framework import serializers
from core.models import (
    Offering,
    Instructor,
    OfferingModule,
    ModuleCourse,
    Topic,
)
from api.shared.serializers.file import FileSerializer


class InstructorSerializer(serializers.ModelSerializer):
    profile_photo = FileSerializer(read_only=True)

    class Meta:
        model = Instructor
        fields = [
            "iid",
            "full_name",
            "title",
            "profile_photo",
        ]


class WebsiteOfferingSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="oid", read_only=True)
    thumbnail = FileSerializer(
        read_only=True,
        allow_null=True,
    )
    foreign_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        read_only=True,
    )
    foreign_base_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        read_only=True,
    )

    start_date = serializers.SerializerMethodField()

    def get_start_date(self, obj):
        if obj.start_date:
            return f"{obj.start_date.strftime('%Y-%m-%d')}T00:00:00-05:00"
        return None

    class Meta:
        model = Offering
        fields = [
            "oid",
            "key",
            "name",
            "slug",
            "description",
            "modality",
            "type",
            "stage",
            "format",
            "base_price",
            "foreign_base_price",
            "discount",
            "price",
            "foreign_price",
            "duration",
            "start_date",
            "thumbnail",
        ]
        read_only_fields = fields


class CourseSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="mcid")
    topics = serializers.SerializerMethodField()

    class Meta:
        model = ModuleCourse
        fields = ["key", "title", "topics"]

    def get_topics(self, obj):
        return TopicSerializer(obj.topics.all(), many=True).data


class TopicSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="tid")

    class Meta:
        model = Topic
        fields = [
            "key",
            "title",
        ]


class ModuleSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="omid")
    courses = serializers.SerializerMethodField()

    class Meta:
        model = OfferingModule
        fields = ["key", "title", "courses"]

    def get_courses(self, obj):
        return CourseSerializer(obj.courses.all(), many=True).data


class WebsiteOfferingDetailedSerializer(WebsiteOfferingSerializer):
    modules = serializers.SerializerMethodField()
    instructors = InstructorSerializer(many=True)

    def get_modules(self, obj):
        return ModuleSerializer(obj.modules.all(), many=True).data

    class Meta(WebsiteOfferingSerializer.Meta):
        fields = WebsiteOfferingSerializer.Meta.fields + [
            "instructors",
            "frequency",
            "hours",
            "objectives",
            "modules",
        ]
        read_only_fields = fields
