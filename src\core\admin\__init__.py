from .blog import BlogAdmin
from .user import UserAdmin, TermAdmin, MajorAdmin, PermissionAdmin
from .instructor import InstructorAdmin
from .blog_category import BlogCategoryAdmin
from .testimonial import TestimonialAdmin
from .file import FileAdmin
from .offering import OfferingAdmin
from .order import OrderAdmin, OrderItemAdmin
from .enrollment import EnrollmentAdmin
from .blog_tag import BlogTagAdmin
from .educational_institution import EducationalInstitutionAdmin, PartnershipAdmin
from .payment import PaymentAdmin
from .event import EventAdmin, EventScheduleAdmin
from .event_reminder import EventReminderAdmin
from .template import TemplateAdmin


__all__ = [
    "UserAdmin",
    "TermAdmin",
    "MajorAdmin",
    "InstructorAdmin",
    "TestimonialAdmin",
    "FileAdmin",
    "OfferingAdmin",
    "OrderAdmin",
    "OrderItemAdmin",
    "EnrollmentAdmin",
    "BlogAdmin",
    "BlogCategoryAdmin",
    "BlogTagAdmin",
    "EducationalInstitutionAdmin",
    "PaymentAdmin",
    "EventAdmin",
    "EventScheduleAdmin",
    "PartnershipAdmin",
    "EventReminderAdmin",
    "TemplateAdmin",
    "PermissionAdmin",
]
