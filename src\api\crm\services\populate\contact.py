import json
from core.models import (
    User as Contact,
    Term,
    Major,
    EducationalInstitution,
)

POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"
majors_file_path = f"{POPULATE_CRM_ASSETS_DIR}/2_majors.json"
terms_file_path = f"{POPULATE_CRM_ASSETS_DIR}/3_terms.json"
contacts_file_path = f"{POPULATE_CRM_ASSETS_DIR}/4_contacts.json"


def populate_majors_data():
    with open(majors_file_path) as f:
        data = json.load(f)

        # Map the data to the Major model
        majors = [
            Major(
                mid=item["mid"],
                name=item["name"],
            )
            for item in data
        ]
        Major.objects.bulk_create(majors)


def populate_terms_data():
    with open(terms_file_path) as f:
        data = json.load(f)

        # Map the data to the Term model
        terms = [
            Term(
                tid=item["tid"],
                name=item["name"],
            )
            for item in data
        ]
        Term.objects.bulk_create(terms)


def populate_contact_data():
    with open(contacts_file_path) as f:
        data = json.load(f)

        # Get existing contacts by uid to check for duplicates
        # Convert to strings to match the format in the JSON
        existing_uids = set(
            str(uid) for uid in Contact.objects.values_list("uid", flat=True)
        )

        contacts_to_create = []
        contacts_to_update = []

        for item in data:
            contact_data = {
                "username": item["phone_number"],
                "first_name": item["first_name"] if item.get("first_name") else "",
                "last_name": item["last_name"] if item.get("last_name") else "",
                "uid": item["uid"],
                "email": item.get("email", None),
                "phone_number": item["phone_number"],
                "id_number": item.get("id_number", None),
                "ocupation": item.get("ocupation", None),
                "major": (
                    Major.objects.get(mid=item["major_mid"])
                    if item.get("major_mid")
                    else None
                ),
                "term": (
                    Term.objects.get(tid=item["term_tid"])
                    if item.get("term_tid")
                    else None
                ),
                "educational_institution": (
                    EducationalInstitution.objects.get(
                        eiid=item["educational_institution_id"]
                    )
                    if item.get("educational_institution_id")
                    else None
                ),
                "country": item.get("country", None),
                "google_contact_id": item.get("google_contact_id", None),
                "is_active": False,
            }

            if item["uid"] in existing_uids:
                # Contact exists, prepare for update
                contacts_to_update.append(contact_data)
            else:
                # New contact, prepare for creation
                contacts_to_create.append(Contact(**contact_data))

        # Bulk create new contacts
        if contacts_to_create:
            Contact.objects.bulk_create(contacts_to_create)

        # Update existing contacts individually (only update fields that are None/empty in DB)
        for contact_data in contacts_to_update:
            existing_contact = Contact.objects.get(uid=contact_data["uid"])

            # Prepare update data - only include fields that are currently None/empty in DB
            update_data = {}

            # Check each field and only update if it's currently None/empty in the database
            if not existing_contact.first_name and contact_data["first_name"]:
                update_data["first_name"] = contact_data["first_name"]
            if not existing_contact.last_name and contact_data["last_name"]:
                update_data["last_name"] = contact_data["last_name"]
            if not existing_contact.email and contact_data["email"]:
                update_data["email"] = contact_data["email"]
            if not existing_contact.phone_number and contact_data["phone_number"]:
                update_data["phone_number"] = contact_data["phone_number"]
            if not existing_contact.id_number and contact_data["id_number"]:
                update_data["id_number"] = contact_data["id_number"]
            if not existing_contact.ocupation and contact_data["ocupation"]:
                update_data["ocupation"] = contact_data["ocupation"]
            if not existing_contact.major and contact_data["major"]:
                update_data["major"] = contact_data["major"]
            if not existing_contact.term and contact_data["term"]:
                update_data["term"] = contact_data["term"]
            if (
                not existing_contact.educational_institution
                and contact_data["educational_institution"]
            ):
                update_data["educational_institution"] = contact_data[
                    "educational_institution"
                ]
            if not existing_contact.country and contact_data["country"]:
                update_data["country"] = contact_data["country"]
            if (
                not existing_contact.google_contact_id
                and contact_data["google_contact_id"]
            ):
                update_data["google_contact_id"] = contact_data["google_contact_id"]

            # Only update if there are fields to update
            if update_data:
                Contact.objects.filter(uid=contact_data["uid"]).update(**update_data)
