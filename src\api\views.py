from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status


class HealthCheckView(APIView):
    """
    Simple health check endpoint to verify API status
    """

    def get(self, request):
        """
        Returns a simple JSON response indicating the API is up and running
        """
        return Response({"status": "ok"}, status=status.HTTP_200_OK)
