import requests
from django.conf import settings
from core.models import Offering


class PayPal:
    def __init__(self):
        self.access_token = self.get_access_token()

    def create_order(self, items):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}",
        }
        total = 0.0
        purchase_items = []
        for item in items:
            offering = Offering.objects.get(oid=item["oid"])
            price = round(float(offering.foreign_price), 2)
            total += price
            purchase_items.append(
                {
                    "name": offering.name,
                    "quantity": 1,
                    "unit_amount": {
                        "currency_code": "USD",
                        "value": f"{price:.2f}",  # Format to 2 decimals
                    },
                }
            )

        order_data = {
            "intent": "CAPTURE",
            "purchase_units": [
                {
                    "amount": {
                        "currency_code": "USD",
                        "value": f"{total:.2f}",
                        "breakdown": {
                            "item_total": {
                                "currency_code": "USD",
                                "value": f"{total:.2f}",
                            }
                        },
                    },
                    "items": purchase_items,
                }
            ],
        }

        response = requests.post(
            f"{settings.PAYPAL_BASE_URL}/v2/checkout/orders",
            json=order_data,
            headers=headers,
        )

        return response.json()

    def get_access_token(self):
        auth_url = f"{settings.PAYPAL_BASE_URL}/v1/oauth2/token"
        client_id = settings.PAYPAL_CLIENT_ID
        client_secret = settings.PAYPAL_CLIENT_SECRET

        response = requests.post(
            auth_url,
            auth=(client_id, client_secret),
            data={"grant_type": "client_credentials"},
        )

        return response.json()["access_token"]
