BASE_DIR = src
INSTALLATION_DIR = installation
HOST_IP_ADDRESS = localhost
PORT = 8001

.phony: build
build:
	docker compose -f $(INSTALLATION_DIR)/docker-compose.dev.yml build

.phony: up
up:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml up -d

.phony: make-requirements
make-requirements:
	- cd $(BASE_DIR)/requirements && pip-compile requirements.in

.phony: install
install:
	- cd $(BASE_DIR) && python -m pip install -r requirements/requirements.txt

.phony: install-container
install-container:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python -m pip install -r requirements/requirements.txt

.phony: update-requirements
update-requirements: make-requirements install

.phony: stop
stop:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml stop

.phony: down
down:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml down

.phony: make-migrations
make-migrations:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py makemigrations

.phony: show-migrations
show-migrations:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py showmigrations

.phony: migrate-to
migrate-to:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py migrate core 0012_eventscheduleenrollment_already_lead_and_more

.phony: migrate
migrate:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py migrate

.phony: populate_crm
populate_crm:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py populate_crm

.phony: fix_activities_deadline
fix_activities_deadline:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py fix_activities_deadline

.phony: create-superuser
create-superuser:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py createsuperuser

.phony: logs
logs:
	- docker compose -f $(INSTALLATION_DIR)/docker-compose.dev.yml logs -f

.phony: api-logs
api-logs:
	- docker compose -f $(INSTALLATION_DIR)/docker-compose.dev.yml logs -f api

.phony: shell
shell:
	- docker compose -f $(INSTALLATION_DIR)/docker-compose.dev.yml exec api python manage.py shell

.phony: sync-migrations
sync-migrations:
	- docker exec -i portals-psql psql -U portals -d portals-db < portals.bkp.sql

.phony: sync-permissions
sync-permissions:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py sync_permissions

.phony: sync-permissions-dry-run
sync-permissions-dry-run:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py sync_permissions --dry-run

.phony: sync-permissions-force
sync-permissions-force:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py sync_permissions --force

.phony: db_seed
db_seed:
	- cd $(INSTALLATION_DIR) && docker compose -f docker-compose.dev.yml exec api python manage.py db_seed