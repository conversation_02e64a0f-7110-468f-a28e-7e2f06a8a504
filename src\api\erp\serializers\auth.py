import enum
from rest_framework import serializers
from core.models import User
from rest_framework.authtoken.models import Token
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _


class ErpAuthLoginSerializer(serializers.Serializer):
    username = serializers.CharField(label=_("Email or username"), write_only=True)
    password = serializers.CharField(
        label=_("Password"),
        style={"input_type": "password"},
        trim_whitespace=False,
        write_only=True,
    )
    token = serializers.CharField(label=_("Token"), read_only=True)

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")

        if username and "@" in username:
            user = (
                User.objects.filter(email=username, is_active=True).filter(
                    is_staff=True
                )
                | User.objects.filter(email=username, is_superuser=True).first()
            )

            if not user:
                msg = _("Access restricted to administrative staff only.")
                raise serializers.ValidationError(msg, code="authorization")
            else:
                username = user.username

        if username and password:
            user = authenticate(
                request=self.context.get("request"),
                username=username,
                password=password,
            )

            if not user:
                msg = _("Unable to log in with provided credentials.")
                raise serializers.ValidationError(msg, code="authorization")

            # Verificar que el usuario sea staff o superuser
            if not (user.is_staff or user.is_superuser):
                msg = _("Access restricted to administrative staff only.")
                raise serializers.ValidationError(msg, code="authorization")
        else:
            msg = _('Must include "username" and "password".')
            raise serializers.ValidationError(msg, code="authorization")

        attrs["user"] = user
        return attrs


class ErpUserDetailSerializer(serializers.ModelSerializer):
    groups = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "uid",
            "username",
            "email",
            "first_name",
            "last_name",
            "groups",
            "permissions",
        ]

    def get_permissions(self, obj):
        """Retorna la lista de permisos codename del usuario."""
        return list(obj.get_all_permissions())

    def get_groups(self, obj):
        """Get user groups as array of group names"""
        return [group.name for group in obj.groups.all()]


class ErpTokenSerializer(serializers.ModelSerializer):
    user = ErpUserDetailSerializer(
        read_only=True,
        help_text="User details",
    )
    key = serializers.CharField(
        max_length=40,
        help_text="Auth token",
    )

    class Meta:
        model = Token
        fields = [
            "key",
            "user",
        ]
