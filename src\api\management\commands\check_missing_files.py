import logging
from django.core.management.base import BaseCommand
from django.db.models import Q
from core.models import File

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Check for files in database that don't exist in storage"

    def add_arguments(self, parser):
        parser.add_argument(
            "--mark-unused",
            action="store_true",
            help="Mark missing files as unused (is_used=False)",
        )
        parser.add_argument(
            "--delete-missing",
            action="store_true",
            help="Delete file records that don't exist in storage (use with caution)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes",
        )

    def handle(self, *args, **options):
        self.stdout.write("Checking files in database against storage...")

        # Get all files that are marked as used
        files = File.objects.filter(is_used=True, deleted=False)
        total_files = files.count()

        self.stdout.write(f"Found {total_files} files to check")

        missing_files = []
        existing_files = []

        for i, file_obj in enumerate(files, 1):
            if i % 100 == 0:
                self.stdout.write(f"Checked {i}/{total_files} files...")

            if not file_obj.exists:
                missing_files.append(file_obj)
                self.stdout.write(
                    self.style.WARNING(
                        f"MISSING: {file_obj.fid} - {file_obj.bucket_name}/{file_obj.object_name}"
                    )
                )
            else:
                existing_files.append(file_obj)

        self.stdout.write(f"\nSummary:")
        self.stdout.write(f"  Total files checked: {total_files}")
        self.stdout.write(f"  Files found in storage: {len(existing_files)}")
        self.stdout.write(
            self.style.WARNING(f"  Files missing from storage: {len(missing_files)}")
        )

        if missing_files:
            if options["mark_unused"]:
                if options["dry_run"]:
                    self.stdout.write(
                        self.style.NOTICE(
                            f"DRY RUN: Would mark {len(missing_files)} files as unused"
                        )
                    )
                else:
                    File.objects.filter(fid__in=[f.fid for f in missing_files]).update(
                        is_used=False
                    )
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Marked {len(missing_files)} missing files as unused"
                        )
                    )

            elif options["delete_missing"]:
                if options["dry_run"]:
                    self.stdout.write(
                        self.style.NOTICE(
                            f"DRY RUN: Would delete {len(missing_files)} file records"
                        )
                    )
                else:
                    deleted_count = File.objects.filter(
                        fid__in=[f.fid for f in missing_files]
                    ).delete()[0]
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Deleted {deleted_count} missing file records"
                        )
                    )
            else:
                self.stdout.write(
                    self.style.NOTICE(
                        "Use --mark-unused to mark missing files as unused, "
                        "or --delete-missing to delete them"
                    )
                )
        else:
            self.stdout.write(self.style.SUCCESS("All files exist in storage!"))
