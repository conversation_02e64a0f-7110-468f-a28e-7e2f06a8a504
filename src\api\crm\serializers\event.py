from rest_framework import serializers
from core.models import Event, File, Offering, Instructor
from .offering import CrmOfferingSerializer
from api.shared.serializers.file import FileSerializer
from api.crm.services.file import upload_file_to_minio
from django.utils.text import slugify


class CrmEventInstructorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Instructor
        fields = [
            "iid",
            "full_name",
        ]


# Base: para listados
class CrmEventBaseSerializer(serializers.ModelSerializer):
    """Base serializer with common fields for all operations"""

    key = serializers.CharField(source="eid", read_only=True)

    class Meta:
        model = Event
        fields = [
            "key",
            "eid",
            "name",
            "description",
            "modality",
            "type",
            "location",
            "price",
            "offering",
            "instructor",
            "thumbnail",
            "cover_image",
            "created_at",
            "updated_at",
        ]

        extra_kwargs = {
            "eid": {"read_only": True},
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }


class CrmEventListItemSerializer(CrmEventBaseSerializer):
    """Serializer for listing events"""

    offering = CrmOfferingSerializer(read_only=True)
    instructor = CrmEventInstructorSerializer(read_only=True)

    class Meta(CrmEventBaseSerializer.Meta):
        fields = CrmEventBaseSerializer.Meta.fields


# Retrieve: para detalle
class CrmEventRetrieveSerializer(CrmEventBaseSerializer):
    """Serializer for retrieving event details with nested relationships"""

    offering = CrmOfferingSerializer(read_only=True)
    instructor = CrmEventInstructorSerializer(read_only=True)
    thumbnail = FileSerializer(read_only=True)
    cover_image = FileSerializer(read_only=True)

    class Meta(CrmEventBaseSerializer.Meta):
        fields = CrmEventBaseSerializer.Meta.fields


# Create: para creación
class CrmEventCreateSerializer(CrmEventBaseSerializer):
    """Serializer for creating new events"""

    name = serializers.CharField(max_length=255, required=True)
    description = serializers.CharField(required=False, allow_blank=True)
    modality = serializers.CharField(max_length=24, required=True)
    type = serializers.CharField(max_length=24, required=True)
    location = serializers.CharField(max_length=255, required=False, allow_null=True)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    offering = serializers.UUIDField(required=False, allow_null=True)
    instructor = serializers.UUIDField(required=False, allow_null=True)
    thumbnail = serializers.UUIDField(required=False, allow_null=True)
    cover_image = serializers.UUIDField(required=False, allow_null=True)

    class Meta(CrmEventBaseSerializer.Meta):
        fields = [
            "name",
            "description",
            "modality",
            "type",
            "location",
            "price",
            "offering",
            "instructor",
            "thumbnail",
            "cover_image",
        ]

    def validate_modality(self, value):
        valid_modalities = [choice[0] for choice in Event.MODALITY_CHOICES]
        if value not in valid_modalities:
            raise serializers.ValidationError(
                f"Invalid modality. Must be one of: {', '.join(valid_modalities)}"
            )
        return value

    def validate_type(self, value):
        valid_types = [choice[0] for choice in Event.TYPE_CHOICES]
        if value not in valid_types:
            raise serializers.ValidationError(
                f"Invalid type. Must be one of: {', '.join(valid_types)}"
            )
        return value

    def validate_price(self, value):
        if value is None:
            return 0
        if value < 0:
            raise serializers.ValidationError("Price cannot be negative")
        return value

    def validate_offering(self, value):
        if value:
            try:
                offering = Offering.objects.get(oid=value, deleted=False)
                return offering
            except Offering.DoesNotExist:
                raise serializers.ValidationError(
                    f"Offering with ID {value} does not exist"
                )
        return None

    def validate_instructor(self, value):
        if value:
            try:
                instructor = Instructor.objects.get(iid=value, deleted=False)
                return instructor
            except Instructor.DoesNotExist:
                raise serializers.ValidationError(
                    f"Instructor with ID {value} does not exist"
                )
        return None

    def validate_thumbnail(self, value):
        if value:
            try:
                thumbnail = File.objects.get(fid=value, deleted=False)
                if thumbnail:
                    thumbnail.is_used = True
                    thumbnail.save()
                return thumbnail
            except File.DoesNotExist:
                raise serializers.ValidationError(
                    f"Thumbnail with ID {value} does not exist"
                )
        return None

    def validate_cover_image(self, value):
        if value:
            try:
                cover_image = File.objects.get(fid=value, deleted=False)
                if cover_image:
                    cover_image.is_used = True
                    cover_image.save()
                return cover_image
            except File.DoesNotExist:
                raise serializers.ValidationError(
                    f"Cover image with ID {value} does not exist"
                )
        return None

    def create(self, validated_data):
        # add the slug based on the name
        name = validated_data.get("name")
        if name:
            slug = slugify(name)
            slug = slug if slug else "new-event"

            # Check if slug exists and make it unique if needed
            if Event.objects.filter(slug=slug).exists():
                count = 1
                while Event.objects.filter(slug=f"{slug}-{count}").exists():
                    count += 1
                slug = f"{slug}-{count}"

            validated_data["slug"] = slug
        return super().create(validated_data)


# Update: para actualización
class CrmEventUpdateSerializer(CrmEventBaseSerializer):
    """Serializer for updating existing events"""

    name = serializers.CharField(max_length=255, required=False)
    description = serializers.CharField(required=False, allow_blank=True)
    modality = serializers.CharField(max_length=24, required=False)
    type = serializers.CharField(max_length=24, required=False)
    location = serializers.CharField(max_length=255, required=False, allow_null=True)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    offering = serializers.PrimaryKeyRelatedField(
        queryset=Offering.objects.filter(deleted=False), required=False, allow_null=True
    )
    instructor = serializers.PrimaryKeyRelatedField(
        queryset=Instructor.objects.filter(deleted=False),
        required=False,
        allow_null=True,
    )
    thumbnail = serializers.PrimaryKeyRelatedField(
        queryset=File.objects.filter(deleted=False), required=False, allow_null=True
    )
    cover_image = serializers.PrimaryKeyRelatedField(
        queryset=File.objects.filter(deleted=False), required=False, allow_null=True
    )

    class Meta(CrmEventBaseSerializer.Meta):
        fields = CrmEventBaseSerializer.Meta.fields


class CrmEventFileSerializer(serializers.ModelSerializer):
    file = serializers.FileField(required=True, write_only=True)

    def validate_file(self, value):
        if not value.name.endswith((".jpg", ".jpeg", ".png", ".webp")):
            raise serializers.ValidationError(
                "Invalid file format. Only Images are allowed."
            )
        return value

    def create(self, validated_data):
        file_obj = validated_data.pop("file")
        fid, object_name = upload_file_to_minio(
            file_obj,
            bucket_name="public",
        )
        validated_data["is_used"] = False
        validated_data["is_private"] = False
        validated_data["name"] = object_name.split("/")[-1]
        validated_data["bucket_name"] = "public"
        validated_data["object_name"] = object_name
        validated_data["fid"] = fid

        return super().create(validated_data)

    class Meta:
        model = File
        fields = ["file"]
        extra_kwargs = {
            "file": {"required": True},
        }
