"""
Set proxy configuration via Evolution API
"""

import logging
from typing import Dict, Any, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def set_proxy(
    instance_name: str,
    enabled: bool,
    host: str,
    port: str,
    protocol: str = "",
    username: str = "",
    password: str = ""
) -> Dict[str, Any]:
    """
    Set proxy configuration for WhatsApp instance
    
    Args:
        instance_name: Name of the WhatsApp instance
        enabled: Whether proxy is enabled
        host: Proxy host address
        port: Proxy port
        protocol: Proxy protocol (http, https, socks5, etc.)
        username: Proxy username (optional)
        password: Proxy password (optional)
        
    Returns:
        Dict containing the API response
        
    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "enabled": enabled,
            "host": host,
            "port": port,
            "protocol": protocol,
            "username": username,
            "password": password,
        }
        
        # Make the request
        response = evolution_request(
            uri=f"/proxy/set/{instance_name}",
            method="POST",
            data=body
        )
        
        logger.info(f"Proxy configured successfully for instance '{instance_name}'")
        return response
        
    except EvolutionAPIError as e:
        logger.error(f"Failed to set proxy for instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error setting proxy: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def find_proxy(instance_name: str) -> Dict[str, Any]:
    """
    Find proxy configuration for WhatsApp instance
    
    Args:
        instance_name: Name of the WhatsApp instance
        
    Returns:
        Dict containing the API response with proxy configuration
        
    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/proxy/find/{instance_name}",
            method="GET"
        )
        
        logger.info(f"Proxy configuration retrieved for instance '{instance_name}'")
        return response
        
    except EvolutionAPIError as e:
        logger.error(f"Failed to find proxy for instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error finding proxy: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
