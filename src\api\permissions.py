from rest_framework import permissions


class IsSuperUser(permissions.BasePermission):
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user is a superadmin
        return request.user.is_superuser


class IsStaffUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_staff)


class IsInDevelopmentTeam(permissions.BasePermission):
    """
    Custom permission to only allow members of the development team.
    """

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user is in the development team
        return request.user.groups.filter(name="development").exists()


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    Assumes the model instance has a `created_by` field.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        if request.method in permissions.SAFE_METHODS:
            return True

        # NOTE: Check if the meta verbose_name_plural is correct and is consistent with the model plural name
        model_name = obj._meta.verbose_name_plural.replace(" ", "_").lower()
        app_label = obj.__class__._meta.app_label

        # Check if user can edit any objects of this model
        any_perm = f"{app_label}.can_manage_any_{model_name}"
        if request.user.has_perm(any_perm):
            return True

        # Check if user can edit own objects AND is the owner
        own_perm = f"{app_label}.can_manage_own_{model_name}"
        if request.user.has_perm(own_perm):
            print(f"User {request.user} has own permission {own_perm}")
            return hasattr(obj, "created_by") and obj.created_by == request.user

        return False


class CanModifySoldOrderStage(permissions.BasePermission):
    """
    Permission to control modification of order stage.
    - Si la orden está en 'sold' o 'por_pagar', solo el agente asignado puede cambiar el estado,
      a menos que el usuario tenga el permiso global 'core.modify_sold_order_stage'.
    - Otros campos pueden ser modificados normalmente.
    """

    def has_object_permission(self, request, view, obj):
        # Allow read access
        if request.method in permissions.SAFE_METHODS:
            return True

        # Solo restringir si el campo 'stage' está siendo modificado
        if (
            hasattr(obj, "stage")
            and obj.stage in ["sold", "to_pay"]
            and request.method in ["PUT", "PATCH"]
            and "stage" in request.data
        ):
            # Si tiene permiso global, permitir
            if request.user.has_perm("core.modify_sold_order_stage"):
                return True

            # Si es el owner, permitir modificar solo de 'to_pay' a 'sold'
            if hasattr(obj, "sales_agent") and obj.sales_agent == request.user:
                new_stage = request.data.get("stage")

                if obj.stage == "to_pay" and new_stage == "sold":
                    return True
                return False

            # Si no, denegar
            return False

        return True


class CanManageUsers(permissions.BasePermission):
    """
    Permission to control CRUD operations on users:
    - Staff users can manage non-staff users (contacts)
    - Only users with 'core.manage_staff_users' permission can manage staff users
    """

    def has_permission(self, request, view):
        # Allow read access to all authenticated staff users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_staff

        # For write operations, require staff status
        return request.user.is_staff

    def has_object_permission(self, request, view, obj):
        # Allow read access to all staff users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_staff

        # For write operations on staff users, require special permission
        if hasattr(obj, "is_staff") and obj.is_staff:
            return request.user.has_perm("core.manage_staff_users")

        # For write operations on non-staff users, allow all staff users
        return request.user.is_staff
