"""
Fetch WhatsApp instances via Evolution API
"""

import logging
from typing import Dict, Any, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def fetch_instances(instance_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Fetch WhatsApp instances
    
    Args:
        instance_name: Optional specific instance name to fetch. If None, fetches all instances.
        
    Returns:
        Dict containing the API response with instances data
        
    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build URI with optional instance name parameter
        uri = "/instance/fetchInstances"
        if instance_name:
            uri += f"?instanceName={instance_name}"
        
        # Make the request
        response = evolution_request(
            uri=uri,
            method="GET"
        )
        
        if instance_name:
            logger.info(f"Instance '{instance_name}' fetched successfully")
        else:
            logger.info("All instances fetched successfully")
        
        return response
        
    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch instances: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching instances: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
