from rest_framework import serializers
from core.models import Testimonial, User
from core.constants import (
    TESTIMONIAL_AUTHOR_PHOTO_WIDTH,
    TESTIMONIAL_AUTHOR_PHOTO_HEIGHT,
)
from django.utils import timezone
from api.shared.serializers.file import FileSerializer
from api.utils import (
    perform_create_image_file,
)


class CmsTestimonialSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="tid", read_only=True)
    author_photo = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Testimonial
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]


class CmsCreateTestimonialSerializer(CmsTestimonialSerializer):
    author_photo_file = serializers.FileField(write_only=True, required=False)

    def create(self, validated_data):
        author_photo_file = validated_data.pop("author_photo_file", None)
        testimonial: Testimonial = Testimonial.objects.create(**validated_data)

        if author_photo_file:
            author_photo = perform_create_image_file(
                author_photo_file,
                TESTIMONIAL_AUTHOR_PHOTO_WIDTH,
                TESTIMONIAL_AUTHOR_PHOTO_HEIGHT,
            )
            testimonial.author_photo = author_photo

        testimonial.save()
        return testimonial


class CmsUpdateTestimonialSerializer(CmsTestimonialSerializer):
    author_photo_file = serializers.FileField(write_only=True, required=False)
    delete_author_photo = serializers.BooleanField(write_only=True, required=False)

    def update(self, instance, validated_data):
        author_photo_file = validated_data.pop("author_photo_file", None)
        delete_author_photo = validated_data.pop("delete_author_photo", False)

        testimonial = super().update(instance, validated_data)

        if delete_author_photo:
            self.__perform_delete_author_photo(
                testimonial, self.context["request"].user
            )

        if author_photo_file:
            if testimonial.author_photo:
                self.__perform_delete_author_photo(
                    testimonial, self.context["request"].user
                )

            author_photo = perform_create_image_file(
                author_photo_file,
                TESTIMONIAL_AUTHOR_PHOTO_WIDTH,
                TESTIMONIAL_AUTHOR_PHOTO_HEIGHT,
            )
            testimonial.author_photo = author_photo

        testimonial.save()
        return testimonial

    def __perform_delete_author_photo(
        self,
        testimonial: Testimonial,
        user: User,
    ):
        testimonial.author_photo.deleted = True
        testimonial.author_photo.deleted_at = timezone.now()
        testimonial.author_photo.deleted_by = user
        testimonial.author_photo.save()
        testimonial.author_photo = None
