# Generated by Django 5.0.6 on 2025-05-25 00:40

import core.models.blog_category
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
import random
from django.conf import settings
from django.db import migrations, models


def assign_sequential_ids_to_orderitems(apps, schema_editor):
    """Assign sequential IDs starting from 1 to existing OrderItem instances"""
    OrderItem = apps.get_model("core", "OrderItem")
    existing_items = list(OrderItem.objects.all())

    if not existing_items:
        return

    # Assign sequential IDs starting from 1
    for i, item in enumerate(existing_items, start=1):
        item.id = i

    # Bulk update all items with their new IDs
    OrderItem.objects.bulk_update(existing_items, ["id"])

    # Update the sequence to continue from the last assigned ID
    if existing_items:
        db_alias = schema_editor.connection.alias
        from django.db import connections

        connection = connections[db_alias]
        with connection.cursor() as cursor:
            # Reset the sequence to start from the next available ID
            cursor.execute(
                "SELECT setval(pg_get_serial_sequence('core_orderitem', 'id'), %s, false)",
                [len(existing_items) + 1],
            )


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0008_alter_offering_type"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="student",
            name="deleted_by",
        ),
        migrations.RemoveField(
            model_name="student",
            name="user",
        ),
        migrations.AlterModelOptions(
            name="blogcategory",
            options={
                "verbose_name": "Blog Category",
                "verbose_name_plural": "Blog Categories",
            },
        ),
        migrations.RenameField(
            model_name="event",
            old_name="product",
            new_name="offering",
        ),
        migrations.RemoveField(
            model_name="event",
            name="duration",
        ),
        migrations.RemoveField(
            model_name="event",
            name="end_date",
        ),
        migrations.RemoveField(
            model_name="event",
            name="format",
        ),
        migrations.RemoveField(
            model_name="event",
            name="id",
        ),
        migrations.RemoveField(
            model_name="event",
            name="stage",
        ),
        migrations.RemoveField(
            model_name="event",
            name="start_date",
        ),
        migrations.RemoveField(
            model_name="order",
            name="mp_payment_id",
        ),
        migrations.RemoveField(
            model_name="order",
            name="status",
        ),
        migrations.RemoveField(
            model_name="orderitem",
            name="oiid",
        ),
        migrations.AddField(
            model_name="blogcategory",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="children",
                to="core.blogcategory",
                verbose_name="Parent Category",
            ),
        ),
        migrations.AddField(
            model_name="blogcategory",
            name="slug",
            field=models.SlugField(
                default=core.models.blog_category.generate_unique_slug,
                max_length=255,
                unique=True,
                verbose_name="Slug",
            ),
        ),
        migrations.AddField(
            model_name="event",
            name="cover_image",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="events_cover_image",
                to="core.file",
                verbose_name="Cover Image",
            ),
        ),
        migrations.AddField(
            model_name="event",
            name="eid",
            field=models.UUIDField(
                default=uuid.uuid4,
                editable=False,
                help_text="Unique identifier for the event",
                primary_key=True,
                serialize=False,
                verbose_name="Event ID",
            ),
        ),
        migrations.AddField(
            model_name="event",
            name="location",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="Location"
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="is_used",
            field=models.BooleanField(
                default=True, help_text="Indicates if the file is being used"
            ),
        ),
        migrations.AddField(
            model_name="offering",
            name="code_name",
            field=models.CharField(
                blank=True,
                default=None,
                help_text="Code name of the academic offering. Used for internal purposes. Example: 2023-1, 2023-2, etc.",
                max_length=128,
                null=True,
                verbose_name="Code Name",
            ),
        ),
        migrations.AddField(
            model_name="offering",
            name="ext_reference",
            field=models.CharField(
                blank=True,
                default=None,
                help_text="External reference for the offering. Used for integration with external systems.",
                max_length=255,
                null=True,
                verbose_name="External Reference",
            ),
        ),
        migrations.AddField(
            model_name="offering",
            name="order",
            field=models.IntegerField(
                blank=True,
                help_text="Order in the Website",
                null=True,
                verbose_name="Order",
            ),
        ),
        migrations.AddField(
            model_name="offering",
            name="short_name",
            field=models.CharField(
                blank=True,
                default=None,
                help_text="Short name of the academic offering. Small Name.",
                max_length=128,
                null=True,
                verbose_name="Short Name",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="agreed_total",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Total amount agreed with the client. If not set, the total amount of the order will be used.",
                max_digits=10,
                null=True,
                verbose_name="Agreed Total",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="has_full_scholarship",
            field=models.BooleanField(
                default=False, verbose_name="Is Full Scholarship"
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="interested_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Interested At"
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="is_international",
            field=models.BooleanField(default=False, verbose_name="Is International"),
        ),
        migrations.AddField(
            model_name="order",
            name="lost_at",
            field=models.DateTimeField(blank=True, null=True, verbose_name="Lost At"),
        ),
        migrations.AddField(
            model_name="order",
            name="paid_at",
            field=models.DateTimeField(blank=True, null=True, verbose_name="Paid At"),
        ),
        migrations.AddField(
            model_name="order",
            name="prospect_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Prospect At"
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="sales_agent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sales_agent",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Sales Agent",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="stage",
            field=models.CharField(
                choices=[
                    ("prospect", "Prospect"),
                    ("interested", "Interested"),
                    ("to_pay", "To Pay"),
                    ("paid", "Paid"),
                    ("lost", "Lost"),
                ],
                default="prospect",
                max_length=50,
                verbose_name="Stage",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="to_pay_at",
            field=models.DateTimeField(blank=True, null=True, verbose_name="To Pay At"),
        ),
        migrations.AddField(
            model_name="orderitem",
            name="custom_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Custom amount for this order item. If not set, the offering price will be used.",
                max_digits=10,
                null=True,
                verbose_name="Custom Amount",
            ),
        ),
        migrations.AddField(
            model_name="orderitem",
            name="id",
            field=models.BigAutoField(
                auto_created=True,
                primary_key=True,
                serialize=False,
                verbose_name="ID",
            ),
            preserve_default=False,
        ),
        # Assign sequential IDs to existing OrderItem instances
        migrations.RunPython(
            assign_sequential_ids_to_orderitems, migrations.RunPython.noop
        ),
        migrations.AddField(
            model_name="user",
            name="company",
            field=models.CharField(
                blank=True, help_text="Company", max_length=128, null=True
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="google_contact_id",
            field=models.CharField(
                blank=True, help_text="Google Contact ID", max_length=32, null=True
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="last_google_sync",
            field=models.DateTimeField(
                blank=True, help_text="Last Google Sync", null=True
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="ocupation",
            field=models.CharField(
                blank=True,
                choices=[
                    ("student", "Student"),
                    ("employee", "Employee"),
                    ("independent", "Independent"),
                ],
                help_text="Ocupation",
                max_length=52,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="role",
            field=models.CharField(
                blank=True, help_text="Role", max_length=128, null=True
            ),
        ),
        migrations.AlterField(
            model_name="blogcategory",
            name="description",
            field=models.TextField(blank=True, null=True, verbose_name="Description"),
        ),
        migrations.AlterField(
            model_name="event",
            name="modality",
            field=models.CharField(
                choices=[("remote", "Remote"), ("in_person", "In-Person")],
                default="remote",
                max_length=24,
                verbose_name="Modality",
            ),
        ),
        migrations.AlterField(
            model_name="event",
            name="name",
            field=models.CharField(max_length=255, verbose_name="Event Name"),
        ),
        migrations.AlterField(
            model_name="event",
            name="thumbnail",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="events_thumbnail",
                to="core.file",
                verbose_name="Thumbnail",
            ),
        ),
        migrations.AlterField(
            model_name="event",
            name="type",
            field=models.CharField(
                choices=[
                    ("workshop", "Workshop"),
                    ("webinar", "Webinar"),
                    ("hands_of_workshop", "Hands-on Workshop"),
                ],
                default="workshop",
                max_length=24,
                verbose_name="Type",
            ),
        ),
        migrations.AlterField(
            model_name="offering",
            name="name",
            field=models.CharField(
                help_text="Name of the academic offering. Large Name.",
                max_length=255,
                verbose_name="Program Name",
            ),
        ),
        migrations.AlterField(
            model_name="offering",
            name="slug",
            field=models.SlugField(
                help_text="Unique identifier for the offering, used in URLs.",
                max_length=255,
                unique=True,
                verbose_name="Slug",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="email",
            field=models.EmailField(
                blank=True,
                help_text="Email address",
                max_length=128,
                null=True,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=models.CharField(
                blank=True,
                help_text="Phone number & country code",
                max_length=40,
                null=True,
                unique=True,
            ),
        ),
        migrations.CreateModel(
            name="Benefit",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "bid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Benefit",
                "verbose_name_plural": "Benefits",
            },
        ),
        migrations.AddField(
            model_name="order",
            name="benefits",
            field=models.ManyToManyField(
                blank=True,
                related_name="orders",
                to="core.benefit",
                verbose_name="Benefits",
            ),
        ),
        migrations.CreateModel(
            name="BlogTag",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "btid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "slug",
                    models.SlugField(max_length=255, unique=True, verbose_name="Slug"),
                ),
                (
                    "badge_color",
                    models.CharField(
                        blank=True,
                        default="#FFFFFF",
                        help_text="Hexadecimal color code for the badge (e.g., #FFFFFF).",
                        max_length=7,
                        null=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Enter a valid hexadecimal color code (e.g., #FFFFFF or #FFF).",
                                regex="^#(?:[0-9a-fA-F]{3}){1,2}$",
                            )
                        ],
                        verbose_name="Badge Color",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog Tag",
                "verbose_name_plural": "Blog Tags",
            },
        ),
        migrations.CreateModel(
            name="BlogPost",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "bid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        blank=True, max_length=255, unique=True, verbose_name="Slug"
                    ),
                ),
                (
                    "title",
                    models.CharField(blank=True, max_length=255, verbose_name="Title"),
                ),
                ("summary", models.TextField(blank=True, verbose_name="Summary")),
                (
                    "content",
                    models.JSONField(blank=True, null=True, verbose_name="Content"),
                ),
                (
                    "reading_time",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Reading Time (minutes)"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("published", "Published"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "published_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Published At"
                    ),
                ),
                (
                    "featured",
                    models.BooleanField(default=False, verbose_name="Featured"),
                ),
                (
                    "featured_order",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Featured Order"
                    ),
                ),
                (
                    "meta_title",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Meta Title"
                    ),
                ),
                (
                    "meta_description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Meta Description"
                    ),
                ),
                (
                    "meta_keywords",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Meta Keywords",
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(default=0, verbose_name="View Count"),
                ),
                (
                    "authors",
                    models.ManyToManyField(
                        blank=True,
                        related_name="blog_posts",
                        to="core.instructor",
                        verbose_name="Authors",
                    ),
                ),
                (
                    "categories",
                    models.ManyToManyField(
                        blank=True,
                        related_name="blog_posts",
                        to="core.blogcategory",
                        verbose_name="Categories",
                    ),
                ),
                (
                    "cover_image",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="blog_covers",
                        to="core.file",
                        verbose_name="Cover Image",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="blog_posts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "thumbnail",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="blog_thumbnails",
                        to="core.file",
                        verbose_name="Thumbnail",
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True,
                        related_name="blog_posts",
                        to="core.blogtag",
                        verbose_name="Tags",
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog Post",
                "verbose_name_plural": "Blog Posts",
            },
        ),
        migrations.CreateModel(
            name="EducationalInstitution",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "eiid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the educational institution",
                        max_length=128,
                        unique=True,
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        help_text="Country where the institution is located",
                        max_length=64,
                    ),
                ),
                (
                    "region",
                    models.CharField(
                        blank=True,
                        help_text="Region where the institution is located",
                        max_length=128,
                        null=True,
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True,
                        help_text="City where the institution is located",
                        max_length=128,
                        null=True,
                    ),
                ),
                (
                    "acronym",
                    models.CharField(
                        blank=True,
                        help_text="Acronym of the institution",
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "institution_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("university_public", "Public University"),
                            ("university_private", "Private University"),
                            ("institute_public", "Public Institute"),
                            ("institute_private", "Private Institute"),
                            ("college_public", "Public College"),
                            ("college_private", "Private College"),
                        ],
                        help_text="Type of educational institution",
                        max_length=64,
                        null=True,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Educational Institution",
                "verbose_name_plural": "Educational Institutions",
            },
        ),
        migrations.AddField(
            model_name="user",
            name="educational_institution",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="students",
                to="core.educationalinstitution",
                verbose_name="EducationalInstitution",
            ),
        ),
        migrations.CreateModel(
            name="EventSchedule",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "esid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Event Schedule ID",
                    ),
                ),
                (
                    "is_general",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if the schedule is general or specific to a partnership",
                        verbose_name="Is General",
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Start Date"
                    ),
                ),
                (
                    "end_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="End Date"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        default="",
                        help_text="Name of the event schedule",
                        max_length=255,
                        verbose_name="Event Schedule Name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="A brief description of the event schedule",
                        verbose_name="Event Schedule Description",
                    ),
                ),
                (
                    "stage",
                    models.CharField(
                        choices=[
                            ("planning", "Planning"),
                            ("launched", "Launched"),
                            ("enrollment_closed", "Enrollment Closed"),
                            ("finished", "Finished"),
                        ],
                        default="planning",
                        max_length=24,
                        verbose_name="Stage",
                    ),
                ),
                (
                    "modality",
                    models.CharField(
                        choices=[("remote", "Remote"), ("in_person", "In-Person")],
                        default="remote",
                        max_length=24,
                        verbose_name="Modality",
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Location"
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Price",
                    ),
                ),
                (
                    "agenda",
                    models.JSONField(blank=True, null=True, verbose_name="Agenda"),
                ),
                (
                    "ext_event_id",
                    models.CharField(
                        blank=True,
                        help_text="External identifier for the event schedule",
                        max_length=64,
                        null=True,
                        verbose_name="External Event ID",
                    ),
                ),
                (
                    "ext_event_link",
                    models.URLField(
                        blank=True,
                        help_text="Link to the external event schedule",
                        max_length=255,
                        null=True,
                        verbose_name="External Event Link",
                    ),
                ),
                (
                    "cover_image",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="event_schedules_cover_image",
                        to="core.file",
                        verbose_name="Cover Image",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "event",
                    models.ForeignKey(
                        help_text="The event to which this schedule belongs",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="schedules",
                        to="core.event",
                        verbose_name="Event",
                    ),
                ),
                (
                    "instructor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="event_schedules",
                        to="core.instructor",
                        verbose_name="Instructor",
                    ),
                ),
                (
                    "thumbnail",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="event_schedules_thumbnail",
                        to="core.file",
                        verbose_name="Thumbnail",
                    ),
                ),
            ],
            options={
                "verbose_name": "Event Schedule",
                "verbose_name_plural": "Event Schedules",
                "ordering": ["start_date"],
            },
        ),
        migrations.CreateModel(
            name="EventScheduleEnrollment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "interests",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="User interests in offerings and events, stored as a JSON field",
                        verbose_name="Interests in Offerings and Events",
                    ),
                ),
                (
                    "diffusion_channel",
                    models.CharField(
                        blank=True,
                        help_text="The channel through which the user learned about the event schedule",
                        max_length=255,
                        null=True,
                        verbose_name="Diffusion Channel",
                    ),
                ),
                (
                    "has_contact",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if the user has been contacted",
                        verbose_name="Has Contact",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "event_schedule",
                    models.ForeignKey(
                        help_text="The event schedule in which the user is enrolled",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="enrollment_records",
                        to="core.eventschedule",
                        verbose_name="Event Schedule",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="The user who is enrolled in the event schedule",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="event_schedule_enrollments",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="enrollments",
            field=models.ManyToManyField(
                blank=True,
                help_text="Users enrolled in this event schedule",
                related_name="enrolled_event_schedules",
                through="core.EventScheduleEnrollment",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Enrollments",
            ),
        ),
        migrations.CreateModel(
            name="LeadSource",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "lsid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Lead Source",
                "verbose_name_plural": "Lead Sources",
            },
        ),
        migrations.AddField(
            model_name="order",
            name="lead_sources",
            field=models.ManyToManyField(
                blank=True,
                related_name="orders",
                to="core.leadsource",
                verbose_name="Lead Sources",
            ),
        ),
        migrations.CreateModel(
            name="Major",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "mid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=128)),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="user",
            name="major",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="students",
                to="core.major",
                verbose_name="Major",
            ),
        ),
        migrations.CreateModel(
            name="Partnership",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "pid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the partnership",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the partnership", max_length=128
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the partnership",
                        null=True,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "institution",
                    models.ForeignKey(
                        help_text="Educational institution involved in the partnership",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="partnerships",
                        to="core.educationalinstitution",
                    ),
                ),
            ],
            options={
                "verbose_name": "Partnership",
                "verbose_name_plural": "Partnerships",
            },
        ),
        migrations.AddField(
            model_name="eventschedule",
            name="partnerships",
            field=models.ManyToManyField(
                blank=True,
                help_text="Partnerships associated with this event schedule",
                related_name="event_schedules",
                to="core.partnership",
                verbose_name="Partnerships",
            ),
        ),
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "pmid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="Payment Method ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "pid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_paid", models.BooleanField(default=False, verbose_name="Is Paid")),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="Amount",
                    ),
                ),
                (
                    "payment_date",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Payment Date"
                    ),
                ),
                (
                    "ext_payment_id",
                    models.CharField(
                        blank=True,
                        max_length=64,
                        null=True,
                        verbose_name="External Payment ID",
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        choices=[("usd", "USD"), ("pen", "PEN")],
                        default="pen",
                        max_length=3,
                        verbose_name="Currency",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "order",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="core.order",
                        verbose_name="Order",
                    ),
                ),
                (
                    "voucher",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payments",
                        to="core.file",
                        verbose_name="Voucher",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="payments",
                        to="core.paymentmethod",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Template",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "tid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Template Name"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("IN_REVIEW", "In Review"),
                            ("REJECTED", "Rejected"),
                            ("APPROVED", "Approved"),
                            ("PAUSED", "Paused"),
                            ("DISABLED", "Disabled"),
                        ],
                        default="DRAFT",
                        max_length=100,
                        verbose_name="Status",
                    ),
                ),
                (
                    "header_image_meta_url",
                    models.TextField(
                        blank=True, null=True, verbose_name="Header Image Meta URL"
                    ),
                ),
                (
                    "body_text",
                    models.TextField(max_length=1024, verbose_name="Body Text"),
                ),
                (
                    "positional_params_example",
                    models.JSONField(
                        blank=True,
                        null=True,
                        verbose_name="Positional Parameters Example",
                    ),
                ),
                (
                    "buttons",
                    models.JSONField(blank=True, null=True, verbose_name="Buttons"),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "header_image",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="templates",
                        to="core.file",
                        verbose_name="Header Image",
                    ),
                ),
            ],
            options={
                "verbose_name": "Message Template",
                "verbose_name_plural": "Message Templates",
            },
        ),
        migrations.CreateModel(
            name="EventReminder",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "rid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "reminder_type",
                    models.CharField(
                        choices=[
                            ("M0", "M0"),
                            ("M1", "M1"),
                            ("M2", "M2"),
                            ("M3", "M3"),
                        ],
                        default="M0",
                        max_length=2,
                        verbose_name="Reminder Type",
                    ),
                ),
                ("event_alliance_id", models.IntegerField()),
                (
                    "event_name",
                    models.CharField(max_length=255, verbose_name="Event Name"),
                ),
                (
                    "variables",
                    models.JSONField(
                        blank=True, null=True, verbose_name="Template Variables"
                    ),
                ),
                ("send_at", models.DateTimeField(verbose_name="Scheduled Send Time")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("PENDING", "Pending"),
                            ("SENT", "Sent"),
                            ("FAILED", "Failed"),
                        ],
                        default="DRAFT",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "template_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reminders",
                        to="core.template",
                        verbose_name="Message Template",
                    ),
                ),
            ],
            options={
                "verbose_name": "Event Reminder",
                "verbose_name_plural": "Event Reminders",
            },
        ),
        migrations.CreateModel(
            name="BroadcastMessage",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "mid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "offering_id",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="Offering ID"
                    ),
                ),
                (
                    "offering_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Offering Name",
                    ),
                ),
                (
                    "pipeline_state",
                    models.CharField(
                        choices=[
                            ("PROSPECT", "Prospect"),
                            ("LEAD", "Lead"),
                            ("PENDING_PAYMENT", "Pending Payment"),
                            ("SOLD", "Sold"),
                        ],
                        default="PROSPECT",
                        max_length=15,
                        verbose_name="Pipeline State",
                    ),
                ),
                (
                    "variables",
                    models.JSONField(
                        blank=True, null=True, verbose_name="Template Variables"
                    ),
                ),
                ("send_at", models.DateTimeField(verbose_name="Scheduled Send Time")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("PENDING", "Pending"),
                            ("SENT", "Sent"),
                            ("FAILED", "Failed"),
                        ],
                        default="DRAFT",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "template_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="broadcast_messages",
                        to="core.template",
                        verbose_name="Message Template",
                    ),
                ),
            ],
            options={
                "verbose_name": "Broadcast Message",
                "verbose_name_plural": "Broadcast Messages",
                "db_table": "broadcast_message",
            },
        ),
        migrations.CreateModel(
            name="Term",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "tid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Name", max_length=32)),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="user",
            name="term",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="students",
                to="core.term",
                verbose_name="Term",
            ),
        ),
        migrations.DeleteModel(
            name="Blog",
        ),
        migrations.DeleteModel(
            name="Student",
        ),
    ]
