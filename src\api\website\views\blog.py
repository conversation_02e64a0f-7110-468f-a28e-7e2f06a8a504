from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django_filters import rest_framework as filters
from core.models import BlogPost
from api.mixins import SwaggerTagMixin
from api.website.serializers.blog import (
    WebsiteBlogPostListSerializer,
    WebsiteBlogPostDetailSerializer,
    WebsiteFeaturedBlogPostSerializer,
)
from api.website.filters.blog import WebsiteBlogPostFilter
from api.paginations import StandardResultsPagination


class WebsiteBlogPostViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    """
    ViewSet for public blog posts.
    Only published posts are visible.
    """

    model_class = BlogPost

    queryset = BlogPost.objects.filter(
        deleted=False,
        status=BlogPost.PUBLISHED,
    ).order_by("-published_at")

    lookup_field = "slug"

    pagination_class = StandardResultsPagination
    filterset_class = WebsiteBlogPostFilter
    filter_backends = (filters.DjangoFilterBackend,)

    swagger_tags = ["Blog Posts"]

    def get_serializer_class(self):
        """
        Return different serializers based on the action.
        """

        if self.action == "retrieve":
            return WebsiteBlogPostDetailSerializer
        elif self.action == "featured":
            return WebsiteFeaturedBlogPostSerializer
        return WebsiteBlogPostListSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()

        # Increment view count
        instance.view_count += 1
        instance.save(update_fields=["view_count"])

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=False, methods=["GET"])
    def featured(self, request):
        queryset = self.filter_queryset(
            self.get_queryset()
            .filter(status=BlogPost.PUBLISHED, featured=True)
            .order_by("featured_order", "-published_at")
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def list(self, request, *args, **kwargs):
        """
        Override the list method to include the last blog as a separate field.
        """
        # Obtener el último blog destacado
        last_blog = self.get_queryset().first()
        queryset = self.filter_queryset(self.get_queryset())

        # Quitar el último blog de la lista
        if last_blog:
            queryset = queryset.exclude(bid=last_blog.bid)

        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)

            if last_blog:
                last_blog_serializer = WebsiteBlogPostListSerializer(last_blog)
                paginated_response.data["lastBlog"] = last_blog_serializer.data

            return paginated_response

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
