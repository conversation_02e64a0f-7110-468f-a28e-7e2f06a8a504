import logging
from typing import Optional
from minio.error import S3Error
from storage.minio import MinioStorage

logger = logging.getLogger(__name__)


def delete_file_from_bucket(bucket_name: str, object_name: str) -> None:
    storage = MinioStorage()
    try:
        storage.remove(
            bucket_name=bucket_name,
            object_name=object_name,
        )
    except S3Error as e:
        logger.warning(
            f"Failed to delete object {object_name} from bucket {bucket_name}: {e}"
        )
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error deleting object {object_name} from bucket {bucket_name}: {e}"
        )
        raise


def get_object_size(bucket_name: str, object_name: str) -> Optional[int]:
    """
    Get the size of an object in bytes.
    Returns None if the object doesn't exist or there's an error.
    """
    if not bucket_name or not object_name:
        return None

    storage = MinioStorage()
    try:
        stat = storage.stat_object(
            bucket_name=bucket_name,
            object_name=object_name,
        )
        return stat.size
    except S3Error as e:
        if e.code == "NoSuchKey":
            logger.debug(f"Object {object_name} not found in bucket {bucket_name}")
            return None
        logger.warning(
            f"S3 error getting size for {object_name} in bucket {bucket_name}: {e}"
        )
        return None
    except Exception as e:
        logger.error(
            f"Unexpected error getting size for {object_name} in bucket {bucket_name}: {e}"
        )
        return None


def get_object_content_type(bucket_name: str, object_name: str) -> Optional[str]:
    """
    Get the content type of an object.
    Returns None if the object doesn't exist or there's an error.
    """
    if not bucket_name or not object_name:
        return None

    storage = MinioStorage()
    try:
        stat = storage.stat_object(
            bucket_name=bucket_name,
            object_name=object_name,
        )
        return stat.content_type
    except S3Error as e:
        if e.code == "NoSuchKey":
            logger.debug(f"Object {object_name} not found in bucket {bucket_name}")
            return None
        logger.warning(
            f"S3 error getting content type for {object_name} in bucket {bucket_name}: {e}"
        )
        return None
    except Exception as e:
        logger.error(
            f"Unexpected error getting content type for {object_name} in bucket {bucket_name}: {e}"
        )
        return None


def get_presigned_url(bucket_name: str, object_name: str) -> Optional[str]:
    """
    Get a presigned URL for an object.
    Returns None if there's an error generating the URL.
    """
    if not bucket_name or not object_name:
        return None

    storage = MinioStorage()
    try:
        return storage.get_presigned_url(
            bucket_name=bucket_name,
            object_name=object_name,
        )
    except S3Error as e:
        if e.code == "NoSuchKey":
            logger.debug(f"Object {object_name} not found in bucket {bucket_name}")
            return None
        logger.warning(
            f"S3 error generating presigned URL for {object_name} in bucket {bucket_name}: {e}"
        )
        return None
    except Exception as e:
        logger.error(
            f"Unexpected error generating presigned URL for {object_name} in bucket {bucket_name}: {e}"
        )
        return None


def validate_file_exists(bucket_name: str, object_name: str) -> bool:
    """
    Check if a file exists in storage without retrieving additional metadata.
    More efficient than calling get_object_size for existence checks.
    """
    if not bucket_name or not object_name:
        return False

    storage = MinioStorage()
    try:
        storage.stat_object(
            bucket_name=bucket_name,
            object_name=object_name,
        )
        return True
    except S3Error as e:
        if e.code == "NoSuchKey":
            return False
        logger.warning(
            f"S3 error checking existence for {object_name} in bucket {bucket_name}: {e}"
        )
        return False
    except Exception as e:
        logger.error(
            f"Unexpected error checking existence for {object_name} in bucket {bucket_name}: {e}"
        )
        return False
