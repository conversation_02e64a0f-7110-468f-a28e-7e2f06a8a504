from django.utils.html import format_html
from django.contrib import admin
from core.models import File


@admin.register(File)
class FileAdmin(admin.ModelAdmin):
    readonly_fields = [
        "url_link",
        "file_exists",
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "fid",
        "name",
        "is_used",
        "format",
        "safe_content_type",
        "safe_size",
        "safe_url",
        "file_exists_display",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = ("deleted", "is_used", "is_private")
    search_fields = ("name", "description")
    ordering = ("-created_at",)

    def url_link(self, obj):
        if obj.url:
            return format_html('<a href="{}" target="_blank">{}</a>', obj.url, obj.url)
        return "-"

    url_link.short_description = "Source URL"

    def safe_content_type(self, obj):
        """Safe display of content type that handles missing files."""
        content_type = obj.content_type
        return content_type if content_type else "Unknown"

    safe_content_type.short_description = "Content Type"

    def safe_size(self, obj):
        """Safe display of file size that handles missing files."""
        size = obj.size
        if size is None:
            return "N/A"

        # Convert bytes to human readable format
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    safe_size.short_description = "Size"

    def safe_url(self, obj):
        """Safe display of URL that handles missing files."""
        url = obj.url
        if url:
            # Truncate long URLs for display
            display_url = url[:50] + "..." if len(url) > 50 else url
            return display_url
        return "No URL"

    safe_url.short_description = "URL"

    def file_exists(self, obj):
        """Display whether the file exists in storage."""
        return obj.exists

    file_exists.short_description = "File Exists"
    file_exists.boolean = True

    def file_exists_display(self, obj):
        """Display file existence status with icon."""
        exists = obj.exists
        if exists:
            return format_html('<span style="color: green;">✓ Exists</span>')
        else:
            return format_html('<span style="color: red;">✗ Missing</span>')

    file_exists_display.short_description = "Status"
