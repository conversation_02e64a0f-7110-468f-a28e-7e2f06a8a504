from typing import Dict, Any
from api.crm.services.evolution_api import EvolutionAPIError
from api.crm.services.evolution_api.evolution_request import evolution_request
import logging

logger = logging.getLogger(__name__)


def get_info() -> Dict[str, Any]:
    """
    Makes a GET request to the evolution API to fetch info

    Returns:
        Dict containing the API response. Example:
        {
        "status": 200,
        "message": "Welcome to the Evolution API, it is working!",
        "version": "2.2.3",
        "clientName": "evolution_exchange",
        "manager": "http://catalogs-televisions-stations-establishing.trycloudflare.com/manager",
        "documentation": "https://doc.evolution-api.com"
    }
    """
    try:
        response = evolution_request(uri="/", method="GET", data=None)
        return response
    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch info: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching info: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
