from django_filters import rest_framework as filters
from core.models import User as Contact
from django.db.models import Q


class CrmDashboardContactFilter(filters.FilterSet):
    """
    Filter for the Contact Dashboard.
    Allows filtering contacts for dashboard analytics.
    """

    # Search by name, email or phone
    search = filters.CharFilter(
        method="filter_search", label="Search by name, email or phone"
    )

    # Date range filter for registration date
    created_at = filters.DateFromToRangeFilter()

    # Multiple choice filter for occupation
    ocupation = filters.CharFilter(
        field_name="ocupation",
        method="filter_ocupation",
        label="Filter by occupation (comma-separated: student,employee,independent)",
    )

    def filter_ocupation(self, queryset, _, value):
        """
        Filter contacts by occupation (supports comma-separated values)
        Examples: ocupation=student or ocupation=student,employee
        """
        if value:
            occupations = [occupation.strip() for occupation in value.split(",")]
            return queryset.filter(ocupation__in=occupations)
        return queryset

    # Filter for country
    country = filters.Char<PERSON>ilter(
        field_name="country",
        method="filter_country",
        label="Search by country name (e.g., Peru, Colombia)",
    )

    def filter_country(self, queryset, _, value):
        """
        Filter contacts by country (supports comma-separated values)
        """
        if value:
            countries = [country.strip() for country in value.split(",")]

            q_objects = Q()
            for country in countries:
                q_objects |= Q(country__icontains=country)

            return queryset.filter(q_objects)
        return queryset

    # Boolean filter for active status
    active = filters.BooleanFilter(field_name="is_active")

    def filter_search(self, queryset, _, value):
        """
        Filter contacts by name, email or phone number
        """
        if not value:
            return queryset

        return queryset.filter(
            Q(first_name__icontains=value)
            | Q(last_name__icontains=value)
            | Q(email__icontains=value)
            | Q(phone_number__icontains=value)
        )

    force_refresh = filters.BooleanFilter(
        method="filter_force_refresh", help_text="Force refresh cache (true/false)"
    )

    def filter_force_refresh(self, queryset, name, value):
        """
        Dummy filter for force_refresh - actual logic is in ViewSet.initial()
        """
        return queryset

    class Meta:
        model = Contact
        fields = [
            "search",
            "created_at",
            "ocupation",
            "active",
            "country",
            "force_refresh",
        ]
