from rest_framework import viewsets
from core.models import Benefit
from api.crm.serializers.benefit import CrmBenefitSerializer, CrmCreateBenefitSerializer
from api.paginations import StandardResultsPagination
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from api.mixins import AuditMixin, SwaggerTagMixin


class CrmBenefitViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Benefit
    queryset = Benefit.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmBenefitSerializer
    swagger_tags = ["Benefit"]
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    def get_serializer(self, *args, **kwargs):
        if self.action == "create":
            serializer_class = CrmCreateBenefitSerializer
            return serializer_class(*args, **kwargs)
        return super().get_serializer(*args, **kwargs)
