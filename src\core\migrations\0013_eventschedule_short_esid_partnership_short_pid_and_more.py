# Generated by Django 5.0.6 on 2025-07-21 00:34

import django.utils.timezone
import shortuuid
from django.db import migrations, models


def populate_short_esid(apps, schema_editor):
    """Generate short_esid for all existing EventSchedule instances"""
    EventSchedule = apps.get_model("core", "EventSchedule")
    shortuuid.set_alphabet("23456789abcdefghjkmnpqrstuvwxyz")

    existing_short_esids = set()

    for schedule in EventSchedule.objects.all():
        # Generate a unique 8-character short_esid
        short_esid = shortuuid.ShortUUID().random(length=8)

        # Verify uniqueness
        while (
            short_esid in existing_short_esids
            or EventSchedule.objects.filter(short_esid=short_esid).exists()
        ):
            short_esid = shortuuid.ShortUUID().random(length=8)

        existing_short_esids.add(short_esid)
        schedule.short_esid = short_esid
        schedule.save()


def reverse_populate_short_esid(apps, schema_editor):
    """Reverse: clear all short_esid values"""
    EventSchedule = apps.get_model("core", "EventSchedule")
    EventSchedule.objects.all().update(short_esid="")


def populate_short_pid(apps, schema_editor):
    """Generate short_pid for all existing Partnership instances"""
    Partnership = apps.get_model("core", "Partnership")
    shortuuid.set_alphabet("23456789abcdefghjkmnpqrstuvwxyz")

    existing_short_pids = set()

    for partnership in Partnership.objects.all():
        # Generate a unique 8-character short_pid
        short_pid = shortuuid.ShortUUID().random(length=8)

        # Verify uniqueness
        while (
            short_pid in existing_short_pids
            or Partnership.objects.filter(short_pid=short_pid).exists()
        ):
            short_pid = shortuuid.ShortUUID().random(length=8)

        existing_short_pids.add(short_pid)
        partnership.short_pid = short_pid
        partnership.save()


def reverse_populate_short_pid(apps, schema_editor):
    """Reverse: clear all short_pid values"""
    Partnership = apps.get_model("core", "Partnership")
    Partnership.objects.all().update(short_pid="")


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0012_eventscheduleenrollment_already_lead_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="eventschedule",
            name="short_esid",
            field=models.CharField(
                editable=False,
                help_text="Short unique identifier for URLs and display",
                max_length=8,
                unique=True,
                verbose_name="Short Event Schedule ID",
                null=True,
                blank=True,
            ),
        ),
        migrations.AddField(
            model_name="partnership",
            name="short_pid",
            field=models.CharField(
                editable=False,
                help_text="Short unique identifier for URLs and display",
                max_length=8,
                unique=True,
                verbose_name="Short Partnership ID",
                null=True,
                blank=True,
            ),
        ),
        migrations.AlterField(
            model_name="eventscheduleenrollment",
            name="phone_number",
            field=models.CharField(
                blank=True,
                help_text="User's phone number, if available",
                max_length=64,
                null=True,
                verbose_name="Phone Number",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=models.CharField(
                blank=True,
                help_text="Phone number & country code",
                max_length=64,
                null=True,
                unique=True,
            ),
        ),
        migrations.AddIndex(
            model_name="eventschedule",
            index=models.Index(
                fields=["short_esid"], name="eventschedule_short_esid_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="partnership",
            index=models.Index(fields=["short_pid"], name="partnership_short_pid_idx"),
        ),
        migrations.RunPython(
            populate_short_esid,
            reverse_populate_short_esid,
        ),
        migrations.RunPython(
            populate_short_pid,
            reverse_populate_short_pid,
        ),
        migrations.AlterField(
            model_name="eventschedule",
            name="short_esid",
            field=models.CharField(
                editable=False,
                help_text="Short unique identifier for URLs and display",
                max_length=8,
                unique=True,
                verbose_name="Short Event Schedule ID",
            ),
        ),
        migrations.AlterField(
            model_name="partnership",
            name="short_pid",
            field=models.CharField(
                editable=False,
                help_text="Short unique identifier for URLs and display",
                max_length=8,
                unique=True,
                verbose_name="Short Partnership ID",
            ),
        ),
    ]
