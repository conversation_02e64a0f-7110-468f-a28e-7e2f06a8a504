from django.dispatch import receiver
from django.db.models.signals import post_delete
from core.models import Instructor, File
from core.utils import delete_file_from_bucket


@receiver(post_delete, sender=Instructor)
def delete_instructor_profile_photo(sender, instance: Instructor, **kwargs):
    if instance.profile_photo:
        instance.profile_photo.delete()


@receiver(post_delete, sender=File)
def delete_file(sender, instance: File, **kwargs):
    delete_file_from_bucket(instance.bucket_name, instance.object_name)
