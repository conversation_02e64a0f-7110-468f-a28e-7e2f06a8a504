# Generated by Django 5.0.6 on 2025-01-30 21:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0005_alter_offering_options_offering_objectives"),
    ]

    operations = [
        migrations.RenameField(
            model_name="offering",
            old_name="frecuency",
            new_name="frequency",
        ),
        migrations.AddField(
            model_name="offering",
            name="foreign_base_price",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=10,
                verbose_name="Base Price in USD ($)",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="offering",
            name="base_price",
            field=models.DecimalField(
                decimal_places=2, max_digits=10, verbose_name="Base Price in PEN (S/.)"
            ),
        ),
        migrations.AlterField(
            model_name="offering",
            name="hours",
            field=models.PositiveIntegerField(blank=True, verbose_name="Hours"),
        ),
    ]
