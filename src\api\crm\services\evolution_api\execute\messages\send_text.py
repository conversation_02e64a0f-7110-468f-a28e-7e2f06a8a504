"""
Send text message via Evolution API
"""

import logging
from typing import Dict, Any, Optional, List
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def send_text(
    instance_name: str,
    remote_jid: str,
    message_text: str,
    delay: Optional[int] = None,
    link_preview: Optional[bool] = None,
    quoted_message_id: Optional[str] = None,
    mentions_everyone: Optional[bool] = None,
    mentioned_numbers: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Send a text message via Evolution API

    Args:
        instance_name: Name of the WhatsApp instance
        remote_jid: Phone number or group ID to send message to
        message_text: Text content of the message
        delay: Delay in milliseconds before sending
        link_preview: Whether to show link preview
        quoted_message_id: ID of message to quote/reply to
        mentions_everyone: Whether to mention everyone in group
        mentioned_numbers: List of phone numbers to mention

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Build request body
        body = {
            "number": remote_jid,
            "text": message_text,
        }

        # Add optional delay
        if delay is not None:
            body["delay"] = delay

        # Add link preview setting
        if link_preview is not None:
            body["linkPreview"] = link_preview

        # Add quoted message
        if quoted_message_id:
            body["quoted"] = {
                "key": {
                    "id": quoted_message_id,
                }
            }

        # Handle mentions
        if mentions_everyone:
            body["mentionsEveryOne"] = True
        elif mentioned_numbers:
            # Ensure numbers have WhatsApp format
            formatted_numbers = []
            for num in mentioned_numbers:
                num = num.strip()
                if "@s.whatsapp.net" not in num:
                    num = f"{num}@s.whatsapp.net"
                formatted_numbers.append(num)
            body["mentioned"] = formatted_numbers

        # Make the request
        response = evolution_request(
            uri=f"/message/sendText/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Text message sent successfully to {remote_jid} via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to send text message: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error sending text message: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
