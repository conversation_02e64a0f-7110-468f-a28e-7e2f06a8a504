"""
Fetch groups information via Evolution API
"""

import logging
from typing import Dict, Any, Optional, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

SearchMethod = Literal["invite_code", "group_jid", "fetch_all"]


def fetch_groups_by_invite_code(instance_name: str, invite_code: str) -> Dict[str, Any]:
    """
    Fetch group information by invite code

    Args:
        instance_name: Name of the WhatsApp instance
        invite_code: Group invite code

    Returns:
        Dict containing the API response with group info

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        response = evolution_request(
            uri=f"/group/inviteInfo/{instance_name}?inviteCode={invite_code}",
            method="GET",
        )

        logger.info(f"Group info fetched by invite code for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch group by invite code: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching group by invite code: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def fetch_group_by_jid(instance_name: str, group_jid: str) -> Dict[str, Any]:
    """
    Fetch group information by group JID

    Args:
        instance_name: Name of the WhatsApp instance
        group_jid: Group JID identifier

    Returns:
        Dict containing the API response with group info

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        response = evolution_request(
            uri=f"/group/findGroupInfos/{instance_name}?groupJid={group_jid}",
            method="GET",
        )

        logger.info(f"Group info fetched by JID for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch group by JID: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching group by JID: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def fetch_all_groups(
    instance_name: str, get_participants: bool = False
) -> Dict[str, Any]:
    """
    Fetch all groups for an instance

    Args:
        instance_name: Name of the WhatsApp instance
        get_participants: Whether to include participants information

    Returns:
        Dict containing the API response with all groups

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        response = evolution_request(
            uri=f"/group/fetchAllGroups/{instance_name}?getParticipants={str(get_participants).lower()}",
            method="GET",
        )

        logger.info(f"All groups fetched for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to fetch all groups: {e.message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching all groups: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")


def fetch_groups(
    instance_name: str,
    search_method: SearchMethod,
    invite_code: Optional[str] = None,
    group_jid: Optional[str] = None,
    get_participants: bool = False,
) -> Dict[str, Any]:
    """
    Fetch groups information using different search methods

    Args:
        instance_name: Name of the WhatsApp instance
        search_method: Method to search groups (invite_code, group_jid, fetch_all)
        invite_code: Group invite code (required for invite_code method)
        group_jid: Group JID (required for group_jid method)
        get_participants: Whether to include participants (for fetch_all method)

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If required parameters are missing
    """
    try:
        if search_method == "invite_code":
            if not invite_code:
                raise ValueError(
                    "invite_code is required for invite_code search method"
                )
            return fetch_groups_by_invite_code(instance_name, invite_code)

        elif search_method == "group_jid":
            if not group_jid:
                raise ValueError("group_jid is required for group_jid search method")
            return fetch_group_by_jid(instance_name, group_jid)

        elif search_method == "fetch_all":
            return fetch_all_groups(instance_name, get_participants)

        else:
            raise ValueError(f"Invalid search method: {search_method}")

    except ValueError as e:
        logger.error(f"Invalid parameters for fetch_groups: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except EvolutionAPIError:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in fetch_groups: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
