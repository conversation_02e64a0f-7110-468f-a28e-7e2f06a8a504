from rest_framework import routers
from api.classroom.views import offering as offering_views
from api.classroom.views import auth as auth_views

router = routers.DefaultRouter(trailing_slash=False)
router.register(
    r"offerings",
    offering_views.ClassroomOfferingViewset,
    basename="classroom-offerings",
)
router.register(
    r"auth",
    auth_views.ClassroomAuthViewSet,
    basename="classroom-auth",
)

urlpatterns = router.urls
