"""
Check WhatsApp numbers via Evolution API
"""

import logging
from typing import Dict, Any, List, Union
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def check_number(instance_name: str, numbers: Union[List[str], str]) -> Dict[str, Any]:
    """
    Check if phone numbers are registered on WhatsApp

    Args:
        instance_name: Name of the WhatsApp instance
        numbers: List of phone numbers or comma-separated string

    Returns:
        Dict containing the API response with number verification results

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If no numbers are provided
    """
    try:
        # Handle numbers input
        if isinstance(numbers, str):
            numbers_list = [n.strip() for n in numbers.split(",") if n.strip()]
        else:
            numbers_list = numbers

        if not numbers_list:
            raise ValueError("At least one phone number is required")

        # Build request body
        body = {
            "numbers": numbers_list,
        }

        # Make the request
        response = evolution_request(
            uri=f"/chat/whatsappNumbers/{instance_name}", method="POST", data=body
        )

        logger.info(
            f"Number verification completed for {len(numbers_list)} numbers via instance {instance_name}"
        )
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to check WhatsApp numbers: {e.message}")
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for check_number: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error checking WhatsApp numbers: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
