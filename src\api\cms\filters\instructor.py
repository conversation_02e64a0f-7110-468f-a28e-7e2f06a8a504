from django_filters import rest_framework as filters
from core.models import Instructor
from django.db.models import Q


class CmsInstructorFilter(filters.FilterSet):
    search = filters.CharFilter(
        method="filter_search", label="Search by full name or title"
    )

    status = filters.MultipleChoiceFilter(
        choices=Instructor.STATUS_CHOICES,
        field_name="status",
    )

    created_at = filters.DateFromToRangeFilter()

    def filter_search(self, queryset, _, value):
        return queryset.filter(
            Q(full_name__icontains=value) | Q(title__icontains=value)
        )

    class Meta:
        model = Instructor
        fields = ["status", "created_at"]
