"""
Find contacts via Evolution API
"""

import logging
from typing import Dict, Any, Optional
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def find_contacts(
    instance_name: str, list_all: bool = False, remote_jid: Optional[str] = None
) -> Dict[str, Any]:
    """
    Find contacts in WhatsApp instance

    Args:
        instance_name: Name of the WhatsApp instance
        list_all: Whether to list all contacts or search for specific one
        remote_jid: Phone number or contact ID to search for (required if list_all is False)

    Returns:
        Dict containing the API response with contacts data

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If remote_jid is not provided when list_all is False
    """
    try:
        # Build request body
        body = {}

        if not list_all:
            if not remote_jid:
                raise ValueError("remote_jid is required when list_all is False")

            # Ensure proper WhatsApp format
            contact_id = remote_jid
            if "@" not in contact_id:
                contact_id = f"{contact_id}@s.whatsapp.net"

            body = {
                "where": {
                    "id": contact_id,
                }
            }

        # Make the request
        response = evolution_request(
            uri=f"/chat/findContacts/{instance_name}", method="POST", data=body
        )

        logger.info(f"Contacts search completed for instance {instance_name}")
        return response

    except EvolutionAPIError as e:
        logger.error(
            f"Failed to find contacts in instance '{instance_name}': {e.message}"
        )
        raise
    except ValueError as e:
        logger.error(f"Invalid parameters for find_contacts: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error finding contacts: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")
