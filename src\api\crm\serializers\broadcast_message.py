import requests
from rest_framework import serializers
from core.models import BroadcastMessage
from django.conf import settings
from api.crm.utils.meta import (
    build_whatsapp_payloads,
    clean_whatsapp_numbers,
    prepare_whatsapp_numbers,
)
from api.crm.serializers.template import CrmTemplateSerializer
from api.crm.services.sales import get_sales_by_status


class CrmBroadcastMessageSerializer(serializers.ModelSerializer):
    template = serializers.SerializerMethodField()

    class Meta:
        model = BroadcastMessage
        fields = [
            "mid",
            "offering_id",
            "offering_name",
            "pipeline_state",
            "template",
            "variables",
            "send_at",
            "status",
            "created_at",
        ]

    def get_template(self, obj):
        return CrmTemplateSerializer(obj.template_id).data if obj.template_id else None


class CrmSendBroadcastMessageSerializer(CrmBroadcastMessageSerializer):
    def send_broadcast_message(self, broadcast_message: BroadcastMessage):
        """Envía un recordatorio de evento por WhatsApp."""
        messages_sent = 0
        try:
            if broadcast_message.status == BroadcastMessage.SENT:
                raise Exception("Message already sent")

            # MAPEAR ESTADOS DE PIPELINE A ESTADOS DE VENTA
            # "Sold" -> "Vendido"
            # "Prospect" -> "Prospecto"
            # "Lead" -> "Interesado"
            # "Pending Payment" -> "Por Pagar"
            pipeline_state = ""
            if broadcast_message.pipeline_state == BroadcastMessage.SOLD:
                pipeline_state = "Vendido"
            elif broadcast_message.pipeline_state == BroadcastMessage.PROSPECT:
                pipeline_state = "Prospecto"
            elif broadcast_message.pipeline_state == BroadcastMessage.LEAD:
                pipeline_state = "Interesado"
            elif broadcast_message.pipeline_state == BroadcastMessage.PENDING_PAYMENT:
                pipeline_state = "Por Pagar"
            else:
                broadcast_message.status = BroadcastMessage.DRAFT
            print("pipeline_state", pipeline_state)

            offering_id = broadcast_message.offering_id or None

            # 1. Obtener contactos por fase de embudo
            sales_by_status_res = get_sales_by_status(pipeline_state, offering_id)
            sales_by_status = sales_by_status_res["data"]
            # limpiar data
            sales_by_status = clean_whatsapp_numbers(sales_by_status)
            # preparar data
            sales_by_status = prepare_whatsapp_numbers(sales_by_status)
            if not sales_by_status:
                broadcast_message.status = BroadcastMessage.FAILED
                broadcast_message.save()
                raise Exception("No sales found")

            broadcast_message = BroadcastMessage.objects.select_related(
                "template_id"
            ).get(mid=broadcast_message.mid)
            template_data = {
                "template_name": broadcast_message.template_id.name,
                "header_image_meta_url": broadcast_message.template_id.header_image_meta_url,
                "variables": broadcast_message.variables or {},
            }

            # 2. Construir el mensaje de WhatsApp basado en la plantilla
            payloads = build_whatsapp_payloads(template_data, sales_by_status)
            broadcast_message.save()
            # 3. Enviar mensajes a través de la API de WhatsApp
            errors = []
            for payload in payloads:
                response = requests.post(
                    f"https://graph.facebook.com/v21.0/{settings.WHATSAPP_BUSINESS_PHONE_ID}/messages",
                    headers={
                        "Authorization": f"Bearer {settings.META_ACCESS_TOKEN}",
                        "Content-Type": "application/json",
                    },
                    json=payload,
                )

                response_json = response.json()
                if response.status_code != 200:
                    # broadcast_message.status = BroadcastMessage.FAILED
                    broadcast_message.status = BroadcastMessage.DRAFT
                    broadcast_message.save()
                    base_error = (
                        f"Error al enviar recordatorio al número {payload['to']}:"
                    )
                    if "error" in response_json and "message" in response_json["error"]:
                        errors.append(
                            f"{base_error} {response_json['error']['message']}"
                        )
                    else:
                        errors.append(f"{base_error} {str(response_json)}")

                # interrumpir luego de solo una iteración
                messages_sent += 1
                break  # TODO: BORRAR ESTA LINEA en produccion

            if errors and messages_sent == 0:
                raise Exception(errors)
            # 4. Actualizar estado a 'SENT'
            broadcast_message.status = BroadcastMessage.SENT
            broadcast_message.save()
            if errors and messages_sent > 0:
                return {
                    "detail": f"Messages {broadcast_message.mid} sent successfully!",
                    "count": messages_sent,
                    "errors": errors,
                }

            return {
                "detail": f"Messages {broadcast_message.mid} sent successfully!",
                "count": messages_sent,
            }

        except Exception as e:
            broadcast_message.status = BroadcastMessage.DRAFT
            broadcast_message.save()
            raise e


class CrmCreateBroadcastMessageSerializer(CrmBroadcastMessageSerializer):
    class Meta:
        model = BroadcastMessage
        fields = [
            "mid",
            "offering_id",
            "offering_name",
            "pipeline_state",
            "template_id",
            "variables",
            "send_at",
        ]


class CrmUpdateBroadcastMessageSerializer(CrmBroadcastMessageSerializer):
    class Meta:
        model = BroadcastMessage
        fields = [
            "offering_id",
            "offering_name",
            "pipeline_state",
            "template_id",
            "variables",
            "send_at",
            "status",
        ]
