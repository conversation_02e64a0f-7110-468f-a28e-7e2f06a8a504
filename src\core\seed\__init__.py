"""
All CRM seeds registration

Register seeds in the order you want them to be executed in the database
"""

from core.seed.registry import seed_registry
from .template_variable import populate_template_variable_data

# Register the template variables seed
seed_registry.register(
    name="template_variables",
    function=populate_template_variable_data,
    description="Populate template variables for WhatsApp messages",
    order=1,
)

# Future seeds can be registered here or in their respective modules
# Example:
# from .other_seed import populate_other_data
# seed_registry.register(
#     name="other_seed",
#     function=populate_other_data,
#     description="Description of other seed",
#     order=2
# )
