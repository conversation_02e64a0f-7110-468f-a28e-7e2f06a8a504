# Filtros de Fecha de Deadline para Actividades

Este documento describe los nuevos filtros implementados para filtrar actividades por fecha de deadline en el endpoint `/api/crm/activities/`.

## Filtros Disponibles

### 1. `deadline_from` - Filtrar desde una fecha/hora específica
Filtra actividades cuyo deadline sea mayor o igual a la fecha/hora especificada.

**Formato**: `YYYY-MM-DDTHH:MM:SS` o `YYYY-MM-DD`

**Ejemplos**:
```bash
# Actividades con deadline desde el 1 de enero de 2025
GET /api/crm/activities/?deadline_from=2025-01-01

# Actividades con deadline desde el 1 de enero de 2025 a las 14:30
GET /api/crm/activities/?deadline_from=2025-01-01T14:30:00

# Actividades con deadline desde hoy
GET /api/crm/activities/?deadline_from=2025-07-20
```

### 2. `deadline_to` - Filtrar hasta una fecha/hora específica
Filtra actividades cuyo deadline sea menor o igual a la fecha/hora especificada.

**Formato**: `YYYY-MM-DDTHH:MM:SS` o `YYYY-MM-DD`

**Ejemplos**:
```bash
# Actividades con deadline hasta el 31 de diciembre de 2025
GET /api/crm/activities/?deadline_to=2025-12-31

# Actividades con deadline hasta el 31 de diciembre de 2025 a las 23:59
GET /api/crm/activities/?deadline_to=2025-12-31T23:59:59

# Actividades con deadline hasta mañana
GET /api/crm/activities/?deadline_to=2025-07-21
```

### 3. `deadline_date` - Filtrar por una fecha específica
Filtra actividades cuyo deadline coincida exactamente con la fecha especificada (independientemente de la hora).

**Formato**: `YYYY-MM-DD`

**Ejemplos**:
```bash
# Actividades con deadline exactamente el 25 de julio de 2025
GET /api/crm/activities/?deadline_date=2025-07-25

# Actividades con deadline hoy
GET /api/crm/activities/?deadline_date=2025-07-20
```

### 4. `deadline_year` - Filtrar por año
Filtra actividades cuyo deadline esté en el año especificado.

**Formato**: `YYYY`

**Ejemplos**:
```bash
# Actividades con deadline en 2025
GET /api/crm/activities/?deadline_year=2025

# Actividades con deadline en 2024
GET /api/crm/activities/?deadline_year=2024
```

### 5. `deadline_month` - Filtrar por mes
Filtra actividades cuyo deadline esté en el mes especificado.

**Formato**: `1-12`

**Ejemplos**:
```bash
# Actividades con deadline en julio (mes 7)
GET /api/crm/activities/?deadline_month=7

# Actividades con deadline en diciembre (mes 12)
GET /api/crm/activities/?deadline_month=12
```

### 6. `deadline_day` - Filtrar por día del mes
Filtra actividades cuyo deadline esté en el día del mes especificado.

**Formato**: `1-31`

**Ejemplos**:
```bash
# Actividades con deadline el día 15 de cualquier mes
GET /api/crm/activities/?deadline_day=15

# Actividades con deadline el día 1 de cualquier mes
GET /api/crm/activities/?deadline_day=1
```

## Combinando Filtros

Puedes combinar múltiples filtros para obtener resultados más específicos:

### Rangos de Fechas
```bash
# Actividades con deadline entre el 1 y 31 de julio de 2025
GET /api/crm/activities/?deadline_from=2025-07-01&deadline_to=2025-07-31

# Actividades con deadline en los próximos 7 días
GET /api/crm/activities/?deadline_from=2025-07-20&deadline_to=2025-07-27
```

### Filtros Específicos por Período
```bash
# Actividades con deadline en julio de 2025
GET /api/crm/activities/?deadline_year=2025&deadline_month=7

# Actividades con deadline todos los días 15 de 2025
GET /api/crm/activities/?deadline_year=2025&deadline_day=15

# Actividades con deadline todos los viernes de julio 2025 (asumiendo que conoces las fechas)
GET /api/crm/activities/?deadline_year=2025&deadline_month=7&deadline_day=5
GET /api/crm/activities/?deadline_year=2025&deadline_month=7&deadline_day=12
GET /api/crm/activities/?deadline_year=2025&deadline_month=7&deadline_day=19
GET /api/crm/activities/?deadline_year=2025&deadline_month=7&deadline_day=26
```

### Combinando con Otros Filtros Existentes
```bash
# Actividades pendientes con deadline esta semana
GET /api/crm/activities/?status=pending&deadline_from=2025-07-20&deadline_to=2025-07-26

# Actividades vencidas (overdue) de julio 2025
GET /api/crm/activities/?overdue=true&deadline_year=2025&deadline_month=7

# Actividades asignadas a un usuario específico con deadline hoy
GET /api/crm/activities/?responsible=123e4567-e89b-12d3-a456-************&deadline_date=2025-07-20

# Actividades de una orden específica con deadline en los próximos 30 días
GET /api/crm/activities/?order=987fcdeb-51d2-47a1-9f8b-123456789abc&deadline_from=2025-07-20&deadline_to=2025-08-19
```

## Casos de Uso Comunes

### 1. Dashboard de Actividades por Vencer
```bash
# Actividades que vencen hoy
GET /api/crm/activities/?deadline_date=2025-07-20&status=pending,in_progress

# Actividades que vencen esta semana
GET /api/crm/activities/?deadline_from=2025-07-20&deadline_to=2025-07-26&status=pending,in_progress
```

### 2. Reportes Mensuales
```bash
# Actividades completadas en julio 2025
GET /api/crm/activities/?deadline_year=2025&deadline_month=7&status=completed

# Todas las actividades de julio 2025
GET /api/crm/activities/?deadline_year=2025&deadline_month=7
```

### 3. Planificación Semanal
```bash
# Actividades de la próxima semana
GET /api/crm/activities/?deadline_from=2025-07-27&deadline_to=2025-08-02

# Actividades del fin de semana
GET /api/crm/activities/?deadline_from=2025-07-26&deadline_to=2025-07-27
```

### 4. Gestión de Prioridades
```bash
# Actividades vencidas este mes
GET /api/crm/activities/?overdue=true&deadline_year=2025&deadline_month=7

# Actividades con deadline en las próximas 2 horas (útil para notificaciones)
GET /api/crm/activities/?deadline_from=2025-07-20T10:00:00&deadline_to=2025-07-20T12:00:00
```

## Notas Importantes

1. **Zona Horaria**: Los filtros de fecha y hora respetan la zona horaria configurada en el servidor Django.

2. **Formato de Fecha**: Para filtros de fecha completa (`deadline_from`, `deadline_to`), puedes usar tanto formato ISO (`YYYY-MM-DDTHH:MM:SS`) como formato de fecha simple (`YYYY-MM-DD`).

3. **Actividades sin Deadline**: Las actividades que no tienen deadline asignado no aparecerán en los resultados cuando uses estos filtros. Usa el filtro `has_deadline=false` para encontrar actividades sin deadline.

4. **Combinación con Paginación**: Todos estos filtros funcionan correctamente con la paginación estándar del API.

5. **Ordenamiento**: Puedes combinar estos filtros con el ordenamiento por deadline usando `?ordering=deadline` o `?ordering=-deadline`.

## Ejemplo de Respuesta

```json
{
    "count": 25,
    "next": "http://localhost:8000/api/crm/activities/?deadline_from=2025-07-20&page=2",
    "previous": null,
    "results": [
        {
            "aid": "123e4567-e89b-12d3-a456-************",
            "title": "Revisar propuesta comercial",
            "description": "Revisar y aprobar la propuesta comercial para el cliente XYZ",
            "deadline": "2025-07-20T14:00:00Z",
            "status": "pending",
            "responsible": {
                "uid": "456e7890-e89b-12d3-a456-************",
                "first_name": "Juan",
                "last_name": "Pérez"
            },
            "order": {
                "oid": "789e0123-e89b-12d3-a456-************",
                "owner": {
                    "first_name": "María",
                    "last_name": "García"
                }
            },
            "created_at": "2025-07-15T10:30:00Z",
            "updated_at": "2025-07-15T10:30:00Z"
        }
    ]
}
```
