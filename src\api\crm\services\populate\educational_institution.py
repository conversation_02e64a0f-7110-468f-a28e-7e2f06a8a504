import json
import shortuuid
from core.models import EducationalInstitution, Partnership, User

POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"

institutions_json_file_path = (
    f"{POPULATE_CRM_ASSETS_DIR}/1_educational_institutions.json"
)
partnerships_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/5_partnerships.json"


def populate_educational_institution_data():
    with open(institutions_json_file_path) as f:
        data = json.load(f)

        # I need to map the data to the EducationalInstitution model
        # First construct a list of EducationalInstitution objects
        # Then use bulk_create to insert them into the database
        institutions = [
            EducationalInstitution(
                eiid=item["eiid"],
                name=item["name"],
                country=item.get("country", ""),
                region=item.get("region", ""),
                city=item.get("city", ""),
                acronym=item.get("acronym", ""),
                institution_type=item.get("institution_type", ""),
            )
            for item in data
        ]
        EducationalInstitution.objects.bulk_create(institutions)


def generate_short_pid():
    shortuuid.set_alphabet("23456789abcdefghjkmnpqrstuvwxyz")
    return shortuuid.ShortUUID().random(length=8)


def populate_partnerships_data():
    with open(partnerships_json_file_path, encoding="utf-8-sig") as f:
        data = json.load(f)

        # Map the data to the Partnership model
        partnerships = [
            Partnership(
                pid=item["pid"],
                short_pid=generate_short_pid(),
                name=item["name"],
                description=item.get("description", None),
                institution=(
                    EducationalInstitution.objects.get(eiid=item["institution"])
                    if item.get("institution")
                    else None
                ),
                delegate=(
                    User.objects.get(uid=item["delegate"])
                    if item.get("delegate")
                    else None
                ),
            )
            for item in data
        ]
        Partnership.objects.bulk_create(partnerships)
