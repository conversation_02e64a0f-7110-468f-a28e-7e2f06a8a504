from core.models import (
    Activity,
    Order,
    User,
)
import json
from datetime import datetime

POPULATE_CRM_ASSETS_DIR = "api/crm/services/populate/assets"
activities_json_file_path = f"{POPULATE_CRM_ASSETS_DIR}/13_activities.json"


def populate_activity_data():
    with open(activities_json_file_path) as f:
        data = json.load(f)

        activities = [
            Activity(
                aid=item["aid"],
                title=item["title"],
                description=item["description"],
                deadline=(
                    datetime.fromtimestamp(item["activity_date"] / 1000)
                    if item.get("activity_date")
                    else None
                ),
                responsible=(
                    User.objects.get(uid=item["assigned_to"])
                    if item.get("assigned_to")
                    else None
                ),
                status=item["status"],
                order=(
                    Order.objects.get(oid=item["order"]) if item.get("order") else None
                ),
            )
            for item in data
        ]

        Activity.objects.bulk_create(activities)


def add_deadline_date():
    """
    Actualiza las fechas deadline de las actividades existentes usando
    el campo activity_date del JSON original.
    Los timestamps están en milisegundos y deben convertirse a datetime.
    """
    with open(activities_json_file_path) as f:
        data = json.load(f)

        for item in data:
            aid = item.get("aid")
            activity_date_timestamp = item.get("activity_date")

            # Verificar que tenemos aid y activity_date
            if not aid or not activity_date_timestamp:
                continue

            try:
                # Buscar la actividad en la base de datos
                activity = Activity.objects.get(aid=aid)

                # Convertir timestamp de milisegundos a datetime
                # Dividir por 1000 para convertir de milisegundos a segundos
                deadline_datetime = datetime.fromtimestamp(
                    activity_date_timestamp / 1000
                )

                # Actualizar el deadline
                activity.deadline = deadline_datetime
                activity.save()

                print(f"Actualizada actividad {aid} con deadline {deadline_datetime}")

            except Activity.DoesNotExist:
                print(f"Actividad con aid {aid} no encontrada en la base de datos")
            except Exception as e:
                print(f"Error al procesar actividad {aid}: {str(e)}")
